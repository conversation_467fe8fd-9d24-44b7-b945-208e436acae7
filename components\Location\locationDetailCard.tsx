import Image from "next/image";
import {<PERSON><PERSON>, <PERSON><PERSON>, Spin<PERSON>} from "flowbite-react";
import Link from "next/link";
import {Suspense} from "react";

export default function LocationDetailCard({sectionData, isMobile}) {
    return (
        <>
            {isMobile ? <><Modal.Header>{sectionData["name"]}</Modal.Header><Modal.Body>
                    <Image src={`/location/thumbnails/${sectionData["id"]}.png`} alt={"obrázok lokácie"}
                           className={"rounded-2xl shadow-lg"}
                           width={800} height={500}/>
                    <div className={"flex my-4 justify-between"}>
                        <div className={"flex gap-2 items-center"}>
                            <Badge className={"min-w-1/3"} color={"red"}>{sectionData["street"]}</Badge>
                            {sectionData["url"] && <Badge><Link href={sectionData["url"]} target={"_blank"}
                                                                className={"flex hover:underline transition-all gap-1 items-center"}>
                                <svg aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" width="16px" height="16px"
                                     viewBox="0 0 24 24"
                                     fill="currentColor">
                                    <path fillRule="evenodd"
                                          d="M8.64 4.737A7.97 7.97 0 0 1 12 4a7.997 7.997 0 0 1 6.933 4.006h-.738c-.65 0-1.177.25-1.177.9 0 .33 0 2.04-2.026 2.008-1.972 0-1.972-1.732-1.972-2.008 0-1.429-.787-1.65-1.752-1.923-.374-.105-.774-.218-1.166-.411-1.004-.497-1.347-1.183-1.461-1.835ZM6 4a10.06 10.06 0 0 0-2.812 3.27A9.956 9.956 0 0 0 2 12c0 5.289 4.106 9.619 9.304 9.976l.054.004a10.12 10.12 0 0 0 1.155.007h.002a10.024 10.024 0 0 0 1.5-.19 9.925 9.925 0 0 0 2.259-.754 10.041 10.041 0 0 0 4.987-5.263A9.917 9.917 0 0 0 22 12a10.025 10.025 0 0 0-.315-2.5A10.001 10.001 0 0 0 12 2a9.964 9.964 0 0 0-6 2Zm13.372 11.113a2.575 2.575 0 0 0-.75-.112h-.217A3.405 3.405 0 0 0 15 18.405v1.014a8.027 8.027 0 0 0 4.372-4.307ZM12.114 20H12A8 8 0 0 1 5.1 7.95c.95.541 1.421 1.537 1.835 2.415.209.441.403.853.637 1.162.54.712 1.063 1.019 1.591 1.328.52.305 1.047.613 1.6 1.316 1.44 1.825 1.419 4.366 1.35 5.828Z"
                                          clipRule="evenodd"/>
                                </svg>
                                <span>Webová stránka</span>
                            </Link></Badge>}
                        </div>
                        <div className={"flex gap-1"}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" viewBox="0 0 24 24"
                                 fill="currentColor" x="227.5" y="227.5" role="img">
                                <g fill="currentColor">
                                    <path fill="currentColor"
                                          d="M13.5 5.5c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2zM9.8 8.9L7 23h2.1l1.8-8l2.1 2v6h2v-7.5l-2.1-2l.6-3C14.8 12 16.8 13 19 13v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1c-.3 0-.5.1-.8.1L6 8.3V13h2V9.6l1.8-.7"/>
                                </g>
                            </svg>
                            <small>{sectionData["walkingDuration"]} min. pešo</small>
                        </div>

                    </div>
                    {sectionData["busLinky"] && <div className={"flex flex-col my-4 bg-gray-100 rounded-2xl p-5"}>
                        <p>Na tejto autobusovej zastávke stoja tieto linky MHD: </p>
                        <div className={"flex font-medium mt-4 mb-2 py-1 px-4 rounded-lg gap-8 bg-gray-200"}>
                            <p>Linka</p>
                            <p>Smer</p>
                        </div>
                        {sectionData.busLinky?.map((bus: any, index: number) =>
                            <div key={index} className={"flex py-1 px-4 gap-8"}>
                                <p className={"bg-black text-white px-2 w-9 flex justify-center rounded-lg"}>{bus["number"]}</p>
                                <p>{bus["direction"]}</p>
                            </div>
                        )}
                    </div>}
                    <div className={"flex flex-col my-4 bg-gray-100 rounded-2xl p-5"}>
                        <p>{sectionData["openingHours"]}</p>
                    </div>
                </Modal.Body></>
                :
                <div className={"relative"}>
                    <Suspense fallback={<Spinner/>}>
                        <img src={sectionData["image"]} className={"absolute -right-4 -top-4 w-[2vw]"}/>
                        <Image src={`/location/thumbnails/${sectionData["id"]}.png`} alt={"obrázok lokácie"}
                               className={"rounded-2xl shadow-lg"}
                               width={800} height={500}/>
                        <p className={"text-[1.3vw] font-bold my-2"}>{sectionData["name"]}</p>
                        <div className={"flex justify-between"}>
                            <div className={"flex gap-2 items-center"}>
                                <Badge className={"min-w-1/3"} color={"red"}>{sectionData["street"]}</Badge>
                                {sectionData["url"] && <Badge><Link href={sectionData["url"]} target={"_blank"}
                                                                    className={"flex hover:underline transition-all gap-1 items-center"}>
                                    <svg aria-hidden="true"
                                         xmlns="http://www.w3.org/2000/svg" width="16px" height="16px"
                                         viewBox="0 0 24 24"
                                         fill="currentColor">
                                        <path fillRule="evenodd"
                                              d="M8.64 4.737A7.97 7.97 0 0 1 12 4a7.997 7.997 0 0 1 6.933 4.006h-.738c-.65 0-1.177.25-1.177.9 0 .33 0 2.04-2.026 2.008-1.972 0-1.972-1.732-1.972-2.008 0-1.429-.787-1.65-1.752-1.923-.374-.105-.774-.218-1.166-.411-1.004-.497-1.347-1.183-1.461-1.835ZM6 4a10.06 10.06 0 0 0-2.812 3.27A9.956 9.956 0 0 0 2 12c0 5.289 4.106 9.619 9.304 9.976l.054.004a10.12 10.12 0 0 0 1.155.007h.002a10.024 10.024 0 0 0 1.5-.19 9.925 9.925 0 0 0 2.259-.754 10.041 10.041 0 0 0 4.987-5.263A9.917 9.917 0 0 0 22 12a10.025 10.025 0 0 0-.315-2.5A10.001 10.001 0 0 0 12 2a9.964 9.964 0 0 0-6 2Zm13.372 11.113a2.575 2.575 0 0 0-.75-.112h-.217A3.405 3.405 0 0 0 15 18.405v1.014a8.027 8.027 0 0 0 4.372-4.307ZM12.114 20H12A8 8 0 0 1 5.1 7.95c.95.541 1.421 1.537 1.835 2.415.209.441.403.853.637 1.162.54.712 1.063 1.019 1.591 1.328.52.305 1.047.613 1.6 1.316 1.44 1.825 1.419 4.366 1.35 5.828Z"
                                              clipRule="evenodd"/>
                                    </svg>
                                    <span>Webová stránka</span>
                                </Link></Badge>}
                            </div>
                            <div className={"flex gap-1"}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" viewBox="0 0 24 24"
                                     fill="currentColor" x="227.5" y="227.5" role="img">
                                    <g fill="currentColor">
                                        <path fill="currentColor"
                                              d="M13.5 5.5c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2zM9.8 8.9L7 23h2.1l1.8-8l2.1 2v6h2v-7.5l-2.1-2l.6-3C14.8 12 16.8 13 19 13v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1c-.3 0-.5.1-.8.1L6 8.3V13h2V9.6l1.8-.7"/>
                                    </g>
                                </svg>
                                <small>{sectionData["walkingDuration"]} min. pešo</small>
                            </div>

                        </div>
                        {sectionData["busLinky"] && <div className={"flex flex-col my-4 bg-gray-100 rounded-2xl p-5"}>
                            <p>Na tejto autobusovej zastávke stoja tieto linky MHD: </p>
                            <div className={"flex font-medium mt-4 mb-2 py-1 px-4 rounded-lg gap-8 bg-gray-200"}>
                                <p>Linka</p>
                                <p>Smer</p>
                            </div>
                            {sectionData.busLinky?.map((bus: any, index: number) =>
                                <div key={index} className={"flex py-1 px-4 gap-8"}>
                                    <p className={"bg-black text-white px-2 w-9 flex justify-center rounded-lg"}>{bus["number"]}</p>
                                    <p>{bus["direction"]}</p>
                                </div>
                            )}
                        </div>}
                        <div className={"flex flex-col my-4 bg-gray-100 rounded-2xl p-5"}>
                            <p>{sectionData["openingHours"]}</p>
                        </div>
                    </Suspense>
                </div>
            }
        </>
    )
}