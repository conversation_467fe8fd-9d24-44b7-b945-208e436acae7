import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {building} = req.query;
        const {rooms} = req.query;
        let numberOfFlats = [];

        for (let i = 0; i < 5; i++) {
            const pocet = await executeQuery({
                query: 'SELECT COUNT(id) AS id FROM byty WHERE rooms = ? AND budova = ? AND poschodie = ? AND dostupnost = 1',
                values: [rooms, building, i+1]
            })

            const canRooms = await executeQuery({
                query: 'SELECT COUNT(id) AS id FROM byty WHERE canRooms = ? AND budova = ? AND poschodie = ? AND dostupnost = 1',
                values: [rooms, building, i+1]
            });

            let pocetik = canRooms[0].id + pocet[0].id;

            numberOfFlats.push(pocetik);
        }

        res.status(200).json({data: numberOfFlats});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}