import dynamic from "next/dynamic";
import executeQuery from "../../../utils/dbConnection";
import {Alert} from "flowbite-react";
import {useEffect, useRef, useState} from "react";
import {Toaster} from "react-hot-toast";
import VisualisationOverlay from "../../../components/ponuka-bytov/izba/overlayChoose";
import OverlayFloorChoose from "../../../components/ponuka-bytov/izba/overlayFloorChoose";
import {useSearchParams} from "next/navigation";
import PrehladBytov from "../../../components/ponuka-bytov/prehlad-bytov";

const FlatPick = dynamic(() => import('../../../components/ModelComponents/BuildingWrapper/FlatPick'));
const TableDesktop = dynamic(() => import('../../../components/ponuka-bytov/desktopFilter'));
const TableMobile = dynamic(() => import('../../../components/ponuka-bytov/mobileFilter'));
const FlatNavigation = dynamic(() => import('../../../components/ponuka-bytov/izba/flatNavigation'));


export const getServerSideProps = (async (context: any) => {
    let room;
    let numberOfFlats;
    let numberOfFlatsPromises;
    if (context.req.url.match(/\/ponuka-bytov\/(\d+)/)) {
        room = context.req.url.match(/\/ponuka-bytov\/(\d+)/)[1]
    } else {
        room = "all"
    }
    const budovy = ['F', 'E', 'D', 'C', 'B', 'A'];
    if (room === "all") {
        numberOfFlatsPromises = budovy.map(async (budova) => {
            const result = await executeQuery({
                query: 'SELECT COUNT(id) AS id FROM byty WHERE budova = ? AND dostupnost = 1',
                values: [budova]
            });
            console.log(result);
            return result[0].id;
        });
        numberOfFlats = await Promise.all(numberOfFlatsPromises);
    } else {
        numberOfFlatsPromises = budovy.map(async (budova) => {

            const result = await executeQuery({
                query: 'SELECT COUNT(id) AS id FROM byty WHERE rooms = ? AND budova = ? AND dostupnost = 1',
                values: [room, budova]
            });

            const canRooms = await executeQuery({
                query: 'SELECT COUNT(id) AS id FROM byty WHERE canRooms = ? AND budova = ? AND dostupnost = 1',
                values: [room, budova]
            });

            return result[0].id + canRooms[0].id;
        });
        numberOfFlats = await Promise.all(numberOfFlatsPromises);
    }

    let flats = [];
    const rows = await executeQuery({
        query: "SELECT * FROM byty",
        values: [],
    });
    Object.keys(rows).forEach((k, i) => {
        flats.push(rows[k]);
    });
    // @ts-ignore
    flats = JSON.stringify(flats);

    return {props: {room, flats, numberOfFlats}};
})

export default function FlatLayout({room, numberOfFlats, flats, isMobile}) {
    const podlazie = useRef("");
    const byty = useRef(<></>);
    const [selectedFlat, setSelectedFlat] = useState<string>("")
    const [selectedFloor, setSelectedFloor] = useState<number>(null)
    const [flatS, setFlatS] = useState([])
    const [numberOfFlatsFloor, setNumberOfFlatsFloor] = useState([])
    const [is3D, setis3D] = useState(false);
    const params = useSearchParams()
    const byt = params.get("selected")
    const floor = params.get("floor")

    useEffect(() => {
        if (byt !== null) {
            setSelectedFlat(byt)
        }
        if (floor !== null) {
            setSelectedFloor(parseInt(floor))
        }
    }, []);

    const getSelectedFlat = (flat: string) => {
        setSelectedFloor(null)
        setSelectedFlat("")
        if (flat === "reset") {
            setSelectedFlat("")
            setSelectedFloor(null)
        } else {
            setSelectedFlat(flat)
            setTimeout(() => {
                // @ts-ignore
                podlazie.current?.scrollIntoView({block: 'center', behavior: "smooth"})
            }, 200)
        }
    }
    const getSelectedFloor = (floor: number) => {
        if (isNaN(selectedFloor)) {
            setSelectedFloor(null)
        } else {
            setSelectedFloor(floor)
            setTimeout(() => {
                if(byty.current !== null){
                    // @ts-ignore
                    byty.current?.scrollIntoView({behavior: "smooth"})
                }

            }, 200)
        }
    }

    useEffect(() => {
        async function getSelectedFlats() {
            try {
                let response;
                if (room === "all") {
                    response = await fetch('/api/getAllFlatsForMobile?floor=' + (selectedFloor) + "&building=" + selectedFlat);
                } else {
                    response = await fetch('/api/getFlatsForMobile?floor=' + (selectedFloor - 1) + "&building=" + selectedFlat + "&rooms=" + room);
                }
                const result = await response.json();
                setFlatS(result.data)
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }

        async function getNumberOfFlatsOnFloor() {
            try {
                let response;
                if (room === "all") {
                    response = await fetch('/api/getAllFlatsForFloor?floor=' + selectedFloor + "&building=" + selectedFlat);
                } else {
                    response = await fetch('/api/getFlatsForFloor?floor=' + selectedFloor + "&building=" + selectedFlat + "&rooms=" + room);
                }
                const result = await response.json();
                setNumberOfFlatsFloor(result.data)
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }

        getNumberOfFlatsOnFloor().then(() => {
        })
        if (selectedFlat !== "" && selectedFloor !== null) {
            getSelectedFlats().then(() => {
            })
        }
    }, [selectedFlat, selectedFloor]);

    return <>
        <Toaster/>
        <div className={is3D ? "App overflow-hidden relative" : "App bg-white lg:mt-20 relative"}>
            {!is3D && <FlatNavigation getter={getSelectedFlat} isMobile={isMobile} room={room}/>}
            <div className={!is3D && "text-center lg:pt-16 w-full relative bg-white"}>
                {is3D ? <></> :
                    <>
                        <VisualisationOverlay numberOfFlats={numberOfFlats} isOnHome={false} room={room}
                                              selectedBuild={selectedFlat}
                                              getter={getSelectedFlat} isMobile={isMobile}/>

                    {!selectedFlat && !selectedFloor &&
                        <><h2
                            className={"font-bold text-[6vw] lg:text-[3.5vw] relative z-20 mt-28 -mb-14 glassBackdrop w-1/2 rounded-3xl m-auto"}>Tabuľka {room === "all" ? " " : room + "-izbových "}bytov</h2>
                            <PrehladBytov isMobile={isMobile} flats={flats} room={room}/></>}
                        {/*@ts-ignore*/}
                        <div ref={podlazie} className={"text-center h-full pt-[2vw] lg:pt-[10vw] my-60"}>
                            {selectedFlat &&
                                <>
                                    <div
                                        className="flex gap-2 relative z-20 text-center h-full justify-center items-center">
                                        {selectedFloor ?
                                            <svg
                                                className={isMobile ? "w-7 h-7 text-gray-800 -ml-[5vw]" : "w-12 h-12 text-gray-800 -ml-[5vw]"}
                                                aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" fill="#51B314" viewBox="0 0 24 24">
                                                <path fillRule="evenodd"
                                                      d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0Zm13.7-1.3a1 1 0 0 0-1.4-1.4L11 12.6l-1.8-1.8a1 1 0 0 0-1.4 1.4l2.5 2.5c.4.4 1 .4 1.4 0l4-4Z"
                                                      clipRule="evenodd"/>
                                            </svg>

                                            : <svg
                                                className={isMobile ? "w-7 h-7 text-gray-800 -ml-[5vw]" : "w-12 h-12 text-gray-800 -ml-[5vw]"}
                                                aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                                      strokeWidth="1.5"
                                                      d="M8.5 11.5 11 14l4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                            </svg>}
                                        <h1 className={"font-medium text-[7vw] lg:text-[3vw] text-center"}>Vyberte si
                                            podlažie</h1>
                                    </div>
                                    <div className={"px-10 relative z-20 text-center"}>
                                        <small className={"text-gray-500"}>Podlažie si vyberiete ťuknutím na poschodie,
                                            o
                                            ktoré máte
                                            záujem na obrázku nižšie
                                        </small>
                                    </div>
                                    <OverlayFloorChoose numberOfFlatsFloor={numberOfFlatsFloor} building={selectedFlat}
                                                        selectedFloor={selectedFloor}
                                                        getter={getSelectedFloor} isMobile={isMobile}/>
                                </>
                            }
                        </div>
                    {selectedFlat !== "" && selectedFloor !== null ?
                        <>
                            {/*@ts-ignore*/}
                            <div ref={byty} className={"pt-[14vw] lg:pt-[12vw] pb-[5vw]"}>
                                <>
                                    {flatS.length > 0 ?
                                        <>
                                            <h1 className={"font-light text-[5vw] lg:text-[2vw] text-center"}>Zobrazujú
                                                sa
                                                všetky {room === "all" ? "" : room + "-izbové"} byty v
                                                budove <strong
                                                    className={"font-bold text-[2.5vw] bg-gray-300 shadow-md p-2 rounded-xl px-4"}>{selectedFlat}</strong> na {selectedFloor === 1 ? "prízemí" : (selectedFloor - 1) + ". poschodí"}
                                            </h1>
                                            <div className={"flex relative justify-center py-10"}>
                                                <FlatPick floor={selectedFloor} building={selectedFlat}
                                                          isDetailedFlow={true} is3D={false} isMobile={isMobile}
                                                          flatSelectedInPick={flatS} selectedFlatsInFlow={flatS}
                                                          isFlatDetail={false}/>
                                                {isMobile &&
                                                    <div
                                                        className={"flex w-full flex-row-reverse absolute top-[55%] justify-around"}>
                                                        {flatS.map((flat: number, index: number) => (
                                                            <p key={index}
                                                               className={"bg-black opacity-70 rounded-3xl text-white p-1 px-4"}>{flat["flatNumber"]}</p>
                                                        ))}
                                                    </div>
                                                }
                                            </div>
                                            <div className={"lg:mx-40 bg-[#E5E7EB] p-4 lg:p-20 py-10 rounded-xl"}>
                                                {isMobile ?
                                                    <TableMobile filteredFlats={flatS}/> :
                                                    <TableDesktop filteredFlats={flatS} filteredFlatsCount={101}
                                                                  handleChangeToSubmit={undefined} room={room}
                                                                  orderingVymera={undefined} orderingCena={undefined}
                                                                  is3D={true}/>}
                                            </div>
                                        </>
                                        : <><Alert color="info" className={"mx-10 text-3xl"}>
                                            <span className="font-medium text-4xl">Nič sme nenašli!</span> Vaším
                                            kritériám
                                            nevyhovuje
                                            žiaden z bytov, upravte svoje kritériá a skúste to znova.
                                        </Alert></>}
                                </>
                            </div>
                        </> : <></>
                    }
                    </>
                }
            </div>
        </div>
    </>
}
