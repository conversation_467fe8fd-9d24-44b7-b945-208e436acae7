import Image from "next/image";
import Router from "next/router";
import {useEffect, useState} from "react";
import Link from "next/link";
import toast, {Toaster} from "react-hot-toast";
import {useSearchParams} from "next/navigation";
import FlatOptions from "../../components/napiste-nam/flatOptions";
import ParkingOptions from "../../components/napiste-nam/parkingOptions";
import DefaultOptions from "../../components/napiste-nam/defaultOptions";
import {Spinner} from "flowbite-react";

export default function Kontakt({isMobile}) {
    const [isOpen, setIsOpen] = useState(false)
    const [isChoosed, setIsChoosed] = useState(false)
    const [flacik, setFlacik] = useState({rooms: 0, flatNumber: "", poschodie: ""})
    const [isPakringChoosed, setIsParkingChoosed] = useState(false)
    const [loading, setLoading] = useState(true)
    const [parkingPlace, setParkingPlace] = useState({})
    const params = useSearchParams()
    const byt = params.get("byt")
    const rooms = params.get("rooms")
    const parking = params.get("parking")

    useEffect(() => {
        async function getVariant() {
            try {
                let response;
                response = await fetch('/api/getFlat?byt=' + (byt));
                const result = await response.json();
                setFlacik(result.data[0])
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }

        async function getParking() {
            try {
                let response;
                response = await fetch('/api/parking/getParkingPlace?parking=' + parking)
                const result = await response.json()
                setParkingPlace(result.data[0])
            } catch (error) {
                console.error('Error getting parking place: ', error)
            }
        }

        if (byt !== null && rooms !== null) {
            setIsChoosed(true)
            getVariant().then(() => setLoading(false))
        }
        if (parking !== null) {
            setIsParkingChoosed(true)
            getParking().then(() => setLoading(false))
        }
        setLoading(false)
    }, [])

    const handleSubmit = async (e) => {
        e.preventDefault();
        const form = e.currentTarget;
        const data = new FormData(e.currentTarget);
        try {
            const response = await fetch('/api/sendMail', {
                method: 'post', body: new URLSearchParams(data),
            }).then(response => {
                if (!response.ok) {
                    return response.json();
                }
                return response.json();
            })
                .then(data => {
                    if (data.error) {
                        toast.error(data.message)
                    } else {
                        toast.success(data.message);
                    }
                    form.reset();
                    setTimeout(() => {
                        Router.back();
                    }, 1000)
                })
        } catch (err) {
            console.error(err)
        }
    }

    return (<>
        <Toaster/>
        <div className={"grid grid-cols-4 lg:h-screen relative"}>
            <div className={"col-span-4 lg:col-span-3"}>
                <Link href={"/"}>
                    <Image src={"/logo.svg"} className={"m-4"} width={140} height={250}
                           alt={"Laurin Dvor Logo"}/>
                </Link>
                <svg className="w-10 h-10 right-2 top-2 absolute text-gray-800 dark:text-white"
                     onClick={() => Router.back()} aria-hidden="true"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M6 18 18 6m0 12L6 6"/>
                </svg>
                <div className={"px-10 pt-10 lg:pt-0 lg:pl-20 w-full lg:max-w-6xl"}>
                    {!isChoosed && !isPakringChoosed ? <>
                        {isMobile ? <><h1 className={"text-2xl text-[#657492] font-normal"}>Chcem, aby ste ma
                            kontaktovali</h1></> : <><h1
                            className={"text-[3vw] text-[#657492] font-light"}>Napíšte nám</h1><h2
                            className={"text-[1.5vw] text-[#657492] max-w-[75%] mt-5 font-light"}>Zaujímate sa o
                            konkrétny
                            byt,
                            poschodie či
                            dom? Označte ich, prosím, pred odoslaním formulára.</h2></>}
                    </> : isPakringChoosed ? <>
                            <h1
                                className={"text-[2.6vw] px-5 max-w-3xl text-[#657492] font-light"}>Teší nás Váš záujem
                                o parkovacie miesto č.&nbsp;<strong>{parkingPlace["parking_number"]}</strong> v
                                sekcii <strong>{parseInt(parkingPlace["section"]) <= 4 ? "podzemné parkovanie" : parkingPlace["section"] === "V" || parkingPlace["section"] === "V2" ? "vonkajšie parkovanie" : "vonkajšie parkovanie so skladom"}</strong>
                            </h1>
                            <h2 className={"text-[1.5vw] px-5 text-[#657492] font-light"}><b>Pošlite nám</b> vaše
                                kontaktné údaje a my sa Vám <b>čoskoro ozveme.</b></h2>
                        </>

                        : <>
                            {isMobile ? <><h1 className={"text-2xl text-[#657492] font-normal"}>Chcem, aby ste ma
                                kontaktovali</h1></> : <><h1
                                className={"text-[2.6vw] max-w-3xl px-5 text-[#657492] font-light"}>Teší nás Váš záujem
                                o <strong>{rooms}-izbový</strong> byt <strong>{flacik["flatNumber"]}</strong> na <strong>{flacik["poschodie"] === 1 ? "prízemí" : flacik["poschodie"] + ". poschodí"}</strong>
                            </h1>
                                <h2 className={"text-[1.5vw] px-5 text-[#657492] font-light"}><b>Pošlite nám</b> vaše
                                    kontaktné údaje a my sa Vám <b>čoskoro ozveme.</b></h2></>}
                        </>}
                </div>
                <div className={"grid grid-cols-1 mt-10"}>
                    {loading ? <Spinner/> : <form onSubmit={handleSubmit}
                                                  className={"lg:pl-20 flex w-full flex-col lg:flex-row gap-16 lg:max-w-[75%] lg:col-span-2 text-[#657492]"}>
                        {isChoosed ? <FlatOptions selectedFlat={flacik} isMobile={isMobile}/> : isPakringChoosed ?
                            <ParkingOptions selectedParking={parkingPlace}/> : <DefaultOptions/>}
                        <div className={"px-8"}>
                            <div className={"form-group w-full flex flex-col"}>
                                <div className={"flex flex-col"}>
                                    <label className={"mb-0"}>meno:</label>
                                    <input type={"text"} name={"meno"} id={"meno"} required={true}
                                           className={"formInput"}/>
                                </div>
                                <div className={"flex flex-col"}>
                                    <label className={"mb-0"}>e-mail:</label>
                                    <input type={"email"} name={"email"} id={"email"} required={true}
                                           className={"formInput"}/>
                                </div>
                                <div className={"flex flex-col"}>
                                    <label className={"mb-0"}>telefón:</label>
                                    <input type={"number"} name={"phone"} id={"phone"} required={true}
                                           className={"formInput"}/>
                                </div>
                                <div className={"flex flex-col"}>
                                    <label className={"mb-0"}>správa:</label>
                                    <textarea rows={5} name={"message"} required={true} id={"message"}
                                              className={"formInput"}></textarea>
                                </div>
                                <small>Odoslaním tohto formulára dávate súhlas so spracovaním vašich osobných
                                    údajov
                                    pre
                                    potreby spoločnosti. <strong>Tieto údaje nebudú šírené tretím
                                        osobám.</strong></small>
                                <button type={"submit"} className={"ctaBtn my-2"}>ODOSLAŤ</button>
                            </div>
                        </div>
                    </form>}
                </div>
            </div>
            {isMobile === false && <div className={"flex justify-end absolute col-span-2 right-0"}>
                <Image src={"/kon.png"} className={"min-h-screen max-h-screen max-w-[50vw]"}
                       alt={"ilustračný obrázok ženy"} width={1920} height={400}/>
                <svg className="w-20 h-20 right-2 absolute text-gray-800 dark:text-white"
                     onClick={() => Router.back()} aria-hidden="true"
                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                          d="M6 18 18 6m0 12L6 6"/>
                </svg>
            </div>}
        </div>
    </>);
}