export default function ParkingOptions({selectedParking}) {
    if (selectedParking.parking_number === undefined) {
        return null;
    }

    return (
        <div className={"hidden"}>
            <section>
                <input type={"text"} name={"zaujem"} value={"PARKING"}/>
                <input type={"text"} name={"parking"}
                       value={JSON.stringify({
                           parkingNumber: selectedParking.parking_number.toString(),
                           parkingSection: (parseInt(selectedParking["section"]) <= 4) ? "podzemné parkovanie" : selectedParking["section"] === "V" || selectedParking["section"] === "V2" ? "vonkajšie parkovanie" : "vonkajšie parkovanie so skladom"
                       })}/>
            </section>
        </div>
    )
}