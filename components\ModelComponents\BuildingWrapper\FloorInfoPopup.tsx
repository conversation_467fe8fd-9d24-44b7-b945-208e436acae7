//@ts-nocheck
import {useEffect, useState} from "react";
import {Html} from "@react-three/drei";
import {FloorInfo} from "../../../utils/interfaces/FloorInfo";

export default function FloorInfoPopup(props: FloorInfo) {
    const [show, setShow] = useState(false);

    useEffect(() => {
        const timeout = setTimeout(() => {
            setShow(true)
        }, 300)

        return () => clearTimeout(timeout)
    }, [show])

    if (!show) return null

    return (
        <Html distanceFactor={20}>
            <div className="content text-[6vw]">
                {props.floor ? props.floor + '. poschodie' : "Prízemie"} <span
                className="text-gray-400 text-5xl">Počet voľných bytov: {props.popupMessage}</span>
            </div>
        </Html>
    )
}