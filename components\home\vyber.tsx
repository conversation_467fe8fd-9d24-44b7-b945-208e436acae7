import Link from "next/link";
import VisualisationOverlay from "../ponuka-bytov/izba/overlayChoose";

export default function VyberBytu({isMobile}: {isMobile: boolean}){
    return (
        <div
            className="grid md:grid-cols-3 grid-cols-1 items-center relative z-0 mb-10 md:mt-0 ">
            {!isMobile && <div className={"flex pt-10 justify-evenly col-span-3 items-end"}>
                <div className={"hover:bg-gray-300 p-3 transition-all cursor-crosshair rounded-2xl"}>
                    <p>Podunajské Biskupice</p>
                    <p className={"text-[4vw] font-bold"}>Špaldová ulica</p>
                </div>
                <div
                    className={"flex hover:bg-gray-300 p-3 transition-all cursor-crosshair rounded-2xl flex-col justify-center"}>
                    <p className={"text-[4vw] font-bold"}>6 budov</p>
                </div>
                <div className={"hover:bg-gray-300 p-3 transition-all cursor-crosshair rounded-2xl"}>
                    <p className={"text-[4vw] font-bold"}>97 bytov</p>
                </div>
            </div>}
            <div
                className={"flex w-full lg:col-span-3 relative bg-white md:bg-transparent justify-center rounded-2xl shadow-2xl md:shadow-none p-3 md:p-48 pt-10 md:pt-32 md:pb-16 -mt-16 lg:order-last"}>
                {isMobile && <div className="absolute -top-[5vw] flex justify-center">
                    <div className={"flex gap-4"}>
                        {[...Array(5)].map((e, i) => (<Link key={i} href={"/ponuka-bytov/" + (i + 1) + "-izbove"}>
                            <button
                                className="bg-[#7F4F2A] text-white py-[1vw] rounded-full text-[6vw] px-[3.3vw]">
                                <p>{i + 1}i</p></button>
                        </Link>))}
                        <Link href={"/parkovacie-miesta"}>
                            <button
                                className="bg-[#7F4F2A] text-white py-[1vw] rounded-full text-[6vw] px-[3vw]">
                                <p>P</p></button>
                        </Link>
                    </div>
                </div>}
                <VisualisationOverlay numberOfFlats={[]} room={"all"} isOnHome={true} selectedBuild={""}
                                      getter={undefined} isMobile={isMobile}/>
            </div>
        </div>
    )
}