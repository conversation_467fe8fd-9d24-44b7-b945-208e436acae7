import executeQuery from "../../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {article} = req.query;
        const rows = await executeQuery({
            query: 'SELECT * FROM articles WHERE articleSlug = ?',
            values: [article]
        })
        res.status(200).json({data: rows});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: error});
    }
}
