<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 3685.3128 582.9578">
  <defs>
    <clipPath id="clippath">
      <path d="M2409.5484,344.0973l-14.7099-6.7611c-8.4571-4.4874-11.7549-14.9265-7.4105-23.458l19.803-38.8897c4.0389-7.9317,15.0835-8.6759,20.15-1.3577l20.4765,29.5771c5.0808,7.3389,3.8412,17.3229-2.8801,23.1964l-17.6296,15.406c-5.4777,4.7868-11.3734,5.6965-17.7993,2.2869Z" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
    </clipPath>
    <clipPath id="clippath-1">
      <path d="M1963.4678,340.2573l-14.1226-7.438c-8.1194-4.9368-11.2855-16.4211-7.1147-25.8068l19.0123-42.7837c3.8776-8.7259,14.4813-9.5446,19.3455-1.4937l19.6589,32.5387c4.8779,8.0738,3.6878,19.0574-2.7651,25.5191l-16.9257,16.9486c-5.259,5.2661-10.9193,6.2669-17.0886,2.5158Z" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
    </clipPath>
    <clipPath id="clippath-2">
      <path d="M2037.5108,331.291l19.622-9.0188c11.2811-5.9859,15.6801-19.9109,9.8851-31.2912l-26.4157-51.876c-5.3876-10.5803-20.1204-11.573-26.8786-1.8111l-27.3141,39.4538c-6.7774,9.7896-5.1238,23.1074,3.8419,30.9424l23.5166,20.5505c7.3068,6.3853,15.1712,7.5988,23.743,3.0505Z" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
    </clipPath>
    <pattern id="listy_lvd_2" data-name="listy lvd 2" x="0" y="0" width="48.2337" height="55.6542" patternTransform="translate(839.4862 -3127.7473)" patternUnits="userSpaceOnUse" viewBox="0 0 48.2337 55.6542">
      <g>
        <rect width="48.2337" height="55.6542" fill="none" stroke-width="0"/>
        <g>
          <path d="M20.3006,54.1273c.8215-.8036.4574-6.8873.4574-6.8873,0,0-3.6712,1.241-4.3606,3.0174-.3606.9292-.4319,2.2759-.4179,3.3491.0128.9836.908,1.7147,1.8753,1.5356.8849-.1638,1.8832-.4645,2.4458-1.0149Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M11.4106,47.0113c-.2032-.6703,1.7754-3.8136,1.7754-3.8136,0,0,1.5974,1.7396,1.4447,2.8908-.0798.6021-.4367,1.3425-.7589,1.9118-.2953.5217-.988.6498-1.4522.2704-.4247-.347-.8699-.8004-1.009-1.2595Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M2.6696,47.591c-.2211-.6646,1.6726-3.8598,1.6726-3.8598,0,0,1.6434,1.6962,1.5216,2.8511-.0637.6041-.4006,1.3537-.7074,1.9315-.2812.5295-.9702.676-1.4445.3092-.4339-.3355-.891-.7768-1.0424-1.232Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M1.4008,44.6844c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M1.4008,44.7681c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M.5282,51.6159c-.0875-.4999-2.3941-1.8866-2.3941-1.8866,0,0-.4649,1.647.0099,2.3417.2483.3634.721.7257,1.1154.9886.3614.241.8513.0975,1.0276-.2995.1613-.3632.3011-.8018.2412-1.1442Z" fill="#b4bdce" stroke-width="0"/>
        </g>
        <g>
          <path d="M27.456,55.6858c.3951-1.3429,6.5779-2.465,6.5779-2.465,0,0,.5544,5.3744-.8651,6.7953-.7425.7432-2.0375,1.2003-3.1039,1.4673-.9773.2447-2.1331-.7902-2.4252-2.1668-.2672-1.2594-.4543-2.711-.1837-3.6307Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M43.4059,56.9086c.4478-.5386-.1534-4.2039-.1534-4.2039,0,0-2.148.9819-2.4548,2.1019-.1605.5858-.1195,1.4067-.0439,2.0565.0693.5955.6577.9827,1.2329.8137.5262-.1547,1.1126-.3993,1.4193-.7682Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M36.2199,48.8109c-.2032-.6703,1.7754-3.8136,1.7754-3.8136,0,0,1.5974,1.7396,1.4447,2.8908-.0798.6021-.4367,1.3425-.7589,1.9118-.2953.5217-.988.6498-1.4522.2704-.4247-.347-.8699-.8004-1.009-1.2595Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M49.6345,44.6844c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M49.6345,44.7681c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M36.2049,46.3024c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M48.7619,51.6159c-.0875-.4999-2.3941-1.8866-2.3941-1.8866,0,0-.4649,1.647.0099,2.3417.2483.3634.721.7257,1.1154.9886.3614.241.8513.0975,1.0276-.2995.1613-.3632.3011-.8018.2412-1.1442Z" fill="#b4bdce" stroke-width="0"/>
        </g>
        <g>
          <path d="M3.3391,27.8587c.3951-1.3429,6.5779-2.465,6.5779-2.465,0,0,.5544,5.3744-.8651,6.7953-.7425.7432-2.0375,1.2003-3.1039,1.4673-.9773.2447-2.1331-.7902-2.4252-2.1668-.2672-1.2594-.4543-2.711-.1837-3.6307Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M19.2891,29.0815c.4478-.5386-.1534-4.2039-.1534-4.2039,0,0-2.148.9819-2.4548,2.1019-.1605.5858-.1195,1.4067-.0439,2.0565.0693.5955.6577.9827,1.2329.8137.5262-.1547,1.1126-.3993,1.4193-.7682Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M41.8489,28.9359c.8276-.5765,6.013.7349,6.013.7349,0,0-1.6719,2.9617-3.3165,3.2648-.8602.1586-2.033-.0008-2.956-.1888-.846-.1723-1.3295-1.064-1.0165-1.8686.2863-.7361.7092-1.5475,1.276-1.9423Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M14.7295,38.8115c-.4127-.5659-4.1138-.8792-4.1138-.8792,0,0,.4269,2.3229,1.4379,2.8943.5288.2989,1.3348.4599,1.9833.5454.5944.0784,1.1137-.3974,1.0904-.9965-.0213-.548-.1152-1.1765-.3978-1.564Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M44.4174,26.3002c.8215-.8036.4574-6.8873.4574-6.8873,0,0-3.6712,1.241-4.3606,3.0174-.3606.9292-.4319,2.2759-.4179,3.3491.0128.9836.908,1.7147,1.8753,1.5356.8849-.1638,1.8832-.4645,2.4458-1.0149Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M35.5275,19.1841c-.2032-.6703,1.7754-3.8136,1.7754-3.8136,0,0,1.5974,1.7396,1.4447,2.8908-.0798.6021-.4367,1.3425-.7589,1.9118-.2953.5217-.988.6498-1.4522.2704-.4247-.347-.8699-.8004-1.009-1.2595Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M33.0532,30.7747c.546-.109.8033-2.3102.8033-2.3102,0,0-2.2579-.3338-2.8015.1426-.2844.2492-.4308.7047-.5058,1.0825-.0687.3463.4017.7888.9852.9284.5337.1277,1.1449.2313,1.5188.1567Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M12.103,20.9838c-.2032-.6703,1.7754-3.8136,1.7754-3.8136,0,0,1.5974,1.7396,1.4447,2.8908-.0798.6021-.4367,1.3425-.7589,1.9118-.2953.5217-.988.6498-1.4522.2704-.4247-.347-.8699-.8004-1.009-1.2595Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M24.4199,38.2349c.6002-.361,4.132.789,4.132.789,0,0-1.2962,1.9742-2.4498,2.1077-.6034.0698-1.4086-.0951-2.0393-.2684-.5781-.1588-.8716-.7991-.6173-1.342.2327-.4967.5634-1.0392.9745-1.2864Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M30.5423,33.4417c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M26.7865,19.7639c-.2211-.6646,1.6726-3.8598,1.6726-3.8598,0,0,1.6434,1.6962,1.5216,2.8511-.0637.6041-.4006,1.3537-.7074,1.9315-.2812.5295-.9702.676-1.4445.3092-.4339-.3355-.891-.7768-1.0424-1.232Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M25.5176,16.8573c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M25.5176,16.9409c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M12.0881,18.4752c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M24.6451,23.7888c-.0875-.4999-2.3941-1.8866-2.3941-1.8866,0,0-.4649,1.647.0099,2.3417.2483.3634.721.7257,1.1154.9886.3614.241.8513.0975,1.0276-.2995.1613-.3632.3011-.8018.2412-1.1442Z" fill="#b4bdce" stroke-width="0"/>
        </g>
        <g>
          <path d="M17.732,1.1088c.8276-.5765,6.013.7349,6.013.7349,0,0-1.6719,2.9617-3.3165,3.2648-.8602.1586-2.033-.0008-2.956-.1888-.846-.1723-1.3295-1.064-1.0165-1.8686.2863-.7361.7092-1.5475,1.276-1.9423Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M8.9364,2.9476c.546-.109.8033-2.3102.8033-2.3102,0,0-2.2579-.3338-2.8015.1426-.2844.2492-.4308.7047-.5058,1.0825-.0687.3463.4017.7888.9852.9284.5337.1277,1.1449.2313,1.5188.1567Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M.3031,10.4078c.6002-.361,4.132.789,4.132.789,0,0-1.2962,1.9742-2.4498,2.1077-.6034.0698-1.4086-.0951-2.0393-.2684-.5781-.1588-.8716-.7991-.6173-1.342.2327-.4967.5634-1.0392.9745-1.2864Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M6.4254,5.6145c.0265-.4403-1.6449-2.0766-1.6449-2.0766,0,0-.7242,1.2991-.4625,1.9819.1369.3572.4637.7586.7443,1.0602.2571.2764.7001.2536.9291-.0466.2094-.2746.4159-.6174.434-.9189Z" fill="#b4bdce" stroke-width="0"/>
        </g>
        <g>
          <path d="M27.456.0316c.3951-1.3429,6.5779-2.465,6.5779-2.465,0,0,.5544,5.3744-.8651,6.7953-.7425.7432-2.0375,1.2003-3.1039,1.4673-.9773.2447-2.1331-.7902-2.4252-2.1668-.2672-1.2594-.4543-2.711-.1837-3.6307Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M43.4059,1.2543c.4478-.5386-.1534-4.2039-.1534-4.2039,0,0-2.148.9819-2.4548,2.1019-.1605.5858-.1195,1.4067-.0439,2.0565.0693.5955.6577.9827,1.2329.8137.5262-.1547,1.1126-.3993,1.4193-.7682Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M38.8463,10.9844c-.4127-.5659-4.1138-.8792-4.1138-.8792,0,0,.4269,2.3229,1.4379,2.8943.5288.2989,1.3348.4599,1.9833.5454.5944.0784,1.1137-.3974,1.0904-.9965-.0213-.548-.1152-1.1765-.3978-1.564Z" fill="#b4bdce" stroke-width="0"/>
          <path d="M48.5367,10.4078c.6002-.361,4.132.789,4.132.789,0,0-1.2962,1.9742-2.4498,2.1077-.6034.0698-1.4086-.0951-2.0393-.2684-.5781-.1588-.8716-.7991-.6173-1.342.2327-.4967.5634-1.0392.9745-1.2864Z" fill="#b4bdce" stroke-width="0"/>
        </g>
      </g>
    </pattern>
    <clipPath id="clippath-3">
      <path d="M2654.5175,94.7596l2.7627,13.2791c.2519,1.2109-.353,2.4377-1.4669,2.9751h0c-1.2553.6057-1.8414,2.0688-1.3513,3.3736l5.5335,14.7338c.2538.6759.7683,1.2216,1.4281,1.5147l5.4179,2.4075c.8146.362,1.3974,1.1035,1.5567,1.9806l6.8839,37.9071c.1079.5943.013,1.2077-.2696,1.7416l-10.946,20.6821c-.7365,1.3916-.1278,3.1155,1.3191,3.7361l53.6353,23.0059c1.2604.5406,2.7258.0481,3.4038-1.144l17.1524-30.1583c.6237-1.0967.4029-2.4793-.5315-3.3271l-12.1097-10.9877c-.6835-.6202-1.002-1.5471-.844-2.4564l1.4738-8.4814c.171-.9842.8725-1.7929,1.8227-2.1013l3.878-1.2586c1.0746-.3487,1.8174-1.3309,1.8605-2.4598l.115-3.0129c.0317-.8321.4465-1.6028,1.1235-2.0876l.6765-.4845c1.4767-1.0575,2.3867-2.7339,2.4691-4.5484l.8252-18.1758c.0046-.1004.0147-.2004.0304-.2996l2.2764-14.3612c.0906-.5718-.0053-1.1575-.2736-1.6705l-4.6222-8.8366c-.2907-.5558-.3784-1.1955-.2479-1.809l2.0801-9.7827c.3758-1.7675.273-3.6034-.2979-5.3179l-5.0637-15.2076c-.9637-2.8944-2.8303-5.4033-5.3255-7.1584l-11.5731-8.1401c-7.0438-4.9543-15.6868-7.0929-24.2281-5.9948l-11.8947,1.5292c-9.7049,1.2477-17.9891,7.6178-21.6876,16.6766l-3.6357,8.905c-1.9161,4.6931-2.388,9.8511-1.3555,14.814Z" fill="none" stroke-width="0"/>
    </clipPath>
    <clipPath id="clippath-4">
      <path d="M2680.0714,42.0204c5.4076.8868,7.9702,4.134,10.1129,7.0945,4.8293,6.6725,8.0393,16.489,8.0393,16.489l27.1022-3.3043s-5.68-13.889-9.1802-19.4346c-2.3592-3.7379-5.3547-6.7191-8.0612-7.953-2.7065-1.2339-32.5139,1.7603-32.5139,1.7603,0,0-3.0308,1.6977-4.1648,6.4987,0,0,4.1643-1.8887,8.6657-1.1505Z" fill="none" stroke-width="0"/>
    </clipPath>
  </defs>
  <g id="Prípravné_práce_grey" data-name="Prípravné práce grey">
    <path d="M139.8671,265.035h-41.8379c-1.9642,0-3.4773-1.7327-3.2126-3.679l5.5784-41.0256h37.1063l5.5784,41.0256c.2646,1.9463-1.2484,3.679-3.2126,3.679Z" fill="#b6bfd0" stroke-width="0"/>
    <path d="M0,186.7118v17.8669c0,5.015,4.1031,9.118,9.118,9.118h219.6603c5.0149,0,9.118-4.103,9.118-9.118v-17.8669H0Z" fill="#b6bfd0" stroke-width="0"/>
    <path d="M237.8964,69.1109c0-5.0149-4.1031-9.118-9.118-9.118H9.118c-5.0149,0-9.118,4.1031-9.118,9.118v117.601h237.8964v-117.601Z" fill="#515a6a" stroke-width="0"/>
    <rect x="12.718" y="71.7743" width="212.4603" height="103.156" fill="#6b7484" stroke-width="0"/>
    <g>
      <g>
        <polygon points="97.2537 103.5963 97.2537 98.2234 91.4134 98.2234 91.4134 87.6189 77.8643 87.6188 77.8643 98.2233 77.8643 103.5962 36.6331 103.5963 36.6331 143.4649 138.7961 143.4649 138.7961 103.5963 97.2537 103.5963" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <polyline points="61.5566 143.4649 61.5566 120.1942 53.0608 120.1942" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="36.6331" y1="120.1942" x2="43.6031" y2="120.1942" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="61.5566" y1="120.1942" x2="89.4487" y2="120.1942" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <polyline points="98.2651 120.1942 103.555 120.1942 103.555 143.4649" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="82.2352" y1="142.7964" x2="82.2352" y2="128.8504" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="37.9926" y1="131.8295" x2="53.0608" y2="131.8295" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <polyline points="103.555 120.1942 121.3482 120.1942 121.3482 113.4617" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="121.3482" y1="128.8504" x2="121.3482" y2="120.1942" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="121.3482" y1="142.7964" x2="121.3482" y2="135.8234" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="138.7961" y1="120.1942" x2="127.7602" y2="120.1942" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
      </g>
      <line x1="36.6331" y1="156.752" x2="138.7961" y2="156.752" fill="none" stroke="#fff" stroke-miterlimit="10"/>
      <line x1="81.3736" y1="151.1971" x2="94.0556" y2="151.1971" fill="none" stroke="#fff" stroke-miterlimit="10"/>
      <line x1="152.9399" y1="143.4649" x2="152.9399" y2="103.5963" fill="none" stroke="#fff" stroke-miterlimit="10"/>
      <line x1="147.385" y1="126.0051" x2="147.385" y2="121.0561" fill="none" stroke="#fff" stroke-miterlimit="10"/>
    </g>
    <rect x="191.2519" y="84.8981" width="15.7555" height="15.7555" rx="3.8394" ry="3.8394" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
    <rect x="191.2519" y="109.7465" width="15.7555" height="15.7555" rx="3.8394" ry="3.8394" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
    <path d="M328.9156,185.7169l29.0379-84.6951-84.6951-29.0379,55.6572,113.733ZM298.3349,94.1106l32.9578,11.2997-22.1288,10.8291-10.829-22.1288ZM314.7894,127.7348l24.6045-12.0406-12.5639,36.6452-12.0406-24.6046Z" fill="#b4bdce" stroke-width="0"/>
    <rect x="191.2519" y="134.5949" width="15.7555" height="15.7555" rx="3.8394" ry="3.8394" fill="none" stroke="#fff" stroke-miterlimit="10" stroke-width="2"/>
    <g>
      <path d="M186.1731,154.1634h245.9545l-26.0853,147.9369c-1.9195,10.8859-12.3003,19.7106-23.1861,19.7106h-226.2439l29.5608-167.6475Z" fill="#e6f0ff" stroke="#b6bfd0" stroke-miterlimit="10" stroke-width="2"/>
      <g>
        <line x1="344.1311" y1="182.3732" x2="259.6793" y2="182.3732" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="349.8191" y1="193.7027" x2="250.0195" y2="193.7027" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="248.0424" y1="204.9151" x2="347.842" y2="204.9151" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="246.0654" y1="216.1275" x2="345.865" y2="216.1275" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="243.7528" y1="229.243" x2="343.5524" y2="229.243" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <line x1="342.3747" y1="183.0065" x2="334.2219" y2="229.243" fill="none" stroke="#657492" stroke-miterlimit="10"/>
        <line x1="261.684" y1="182.3732" x2="253.4142" y2="229.2735" fill="none" stroke="#657492" stroke-miterlimit="10"/>
        <polyline points="247.035 229.2735 254.2343 188.4442 347.4587 188.4442 340.2594 229.2735" fill="none" stroke="#657492" stroke-miterlimit="10"/>
        <polyline points="276.5865 182.3732 277.1682 179.0742 311.4605 179.0742 327.8292 179.0742 327.2474 182.3732" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <polyline points="293.5369 179.0742 294.0392 176.2257 311.9627 176.2257 311.4605 179.0742" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      </g>
      <g>
        <polygon points="327.1262 266.4405 327.5673 263.9393 330.2746 263.9393 331.0753 259.3984 337.0385 259.3984 336.2378 263.9393 335.7967 266.4411 353.9429 266.4411 350.849 283.9877 306.0978 283.8056 309.1917 266.259 327.1262 266.4405" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <polygon points="283.174 266.4405 283.6116 263.9587 284.4157 259.3984 290.3789 259.3984 289.5771 263.9454 292.2856 263.9393 291.8446 266.4405 309.1917 266.259 306.0978 283.8056 261.9335 283.9877 265.0276 266.4405 283.174 266.4405" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <polygon points="219.0788 285.5385 220.4158 277.9561 222.7805 277.9561 226.3896 257.4879 243.9362 257.4879 235.2375 306.8206 217.6909 306.8206 220.3257 291.8781 215.0994 291.8781 216.2172 285.5385 219.0788 285.5385" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
        <polygon points="248.9659 290.4531 349.709 290.4531 346.823 306.8206 246.0799 306.8206 248.9659 290.4531" fill="none" stroke="#657492" stroke-miterlimit="10"/>
      </g>
      <path d="M432.1276,154.1634l-26.2262,148.7361c1.8416-10.4445-5.8631-18.9114-17.209-18.9114l19.557-110.9134,3.3346-18.9114,3.3346-18.9114c11.3459,0,19.0507,8.4669,17.209,18.9114Z" fill="#b6bfd0" stroke="#b6bfd0" stroke-miterlimit="10" stroke-width="2"/>
    </g>
    <g>
      <polygon points="333.2612 375.1158 75.1548 280.2223 85.9863 250.7611 55.429 239.5266 23.6409 325.9889 54.1982 337.2234 65.0297 307.7622 323.1361 402.6557 333.2612 375.1158" fill="#9ba4b4" stroke-width="0"/>
      <polygon points="333.9514 373.2386 75.845 278.3451 86.6765 248.8839 56.1191 237.6494 24.331 324.1117 54.8884 335.3462 65.7199 305.885 323.8263 400.7785 333.9514 373.2386" fill="#c7d0e0" stroke-width="0"/>
      <line x1="85.9496" y1="282.0601" x2="82.8428" y2="290.5105" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="99.0165" y1="286.8642" x2="95.9097" y2="295.3145" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="112.0833" y1="291.6682" x2="108.9765" y2="300.1186" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="125.1502" y1="296.4723" x2="122.0434" y2="304.9227" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="138.2171" y1="301.2764" x2="135.1103" y2="309.7267" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="151.2839" y1="306.0804" x2="148.1771" y2="314.5308" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="164.3508" y1="310.8845" x2="161.244" y2="319.3349" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="177.4177" y1="315.6886" x2="174.3109" y2="324.1389" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="190.4845" y1="320.4926" x2="187.3777" y2="328.943" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="203.5514" y1="325.2967" x2="200.4446" y2="333.7471" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="216.6183" y1="330.1008" x2="213.5115" y2="338.5511" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="229.6852" y1="334.9049" x2="226.5784" y2="343.3552" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="242.752" y1="339.7089" x2="239.6452" y2="348.1593" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="255.8189" y1="344.513" x2="252.7121" y2="352.9634" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="268.8858" y1="349.3171" x2="265.779" y2="357.7674" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="281.9526" y1="354.1211" x2="278.8458" y2="362.5715" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="295.0195" y1="358.9252" x2="291.9127" y2="367.3756" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="308.0864" y1="363.7293" x2="304.9796" y2="372.1796" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
      <line x1="321.1532" y1="368.5333" x2="318.0464" y2="376.9837" fill="none" stroke="#657492" stroke-miterlimit="10" stroke-width="2"/>
    </g>
  </g>
  <g id="začiatok_výstavby_grey" data-name="začiatok výstavby grey">
    <path d="M816.527,290.7288h-96.7264c-1.6237,0-2.94-1.3163-2.94-2.94v-3.0421h-35.1543v23.4029h35.1543v-3.2779c0-1.6237,1.3163-2.94,2.94-2.94h96.7264c2.2905,0,4.1473-1.8568,4.1473-4.1473v-2.9084c0-2.2905-1.8568-4.1473-4.1473-4.1473Z" fill="#8d96a6" stroke-width="0"/>
    <g>
      <path d="M643.9867,280.4246c-11.4062,0-20.6855-11.4658-20.6855-25.5605v-95.2744c0-11.7314,7.7388-21.2744,17.251-21.2744,49.98.2773,75.8462,3.1406,92.2617,10.2119,19.9751,8.6045,26.1274,24.2793,31.1387,40.9482,8.5635,28.4873,13.4443,49.1826,15.8252,67.1055.7852,5.9102-.5513,11.9277-3.6665,16.5088-3.1689,4.6621-7.8223,7.335-12.7666,7.335h-119.3579Z" fill="#cbd4e5" stroke-width="0"/>
      <path d="M640.5523,139.295c49.8518.2771,75.5903,3.1179,91.8739,10.1323,19.5879,8.4375,25.6457,23.8918,30.5877,40.3306,7.7085,25.6418,13.2032,47.4639,15.7921,66.9519.7538,5.6748-.524,11.4441-3.5054,15.8296-2.9836,4.3889-7.3414,6.9058-11.9561,6.9058h-119.3578c-10.8659,0-19.7056-11.0266-19.7056-24.5808v-95.2749c0-11.1904,7.2992-20.2944,16.2712-20.2944M640.5578,137.335h-.0054c-10.0527,0-18.2312,9.9832-18.2312,22.2544v95.2749c0,14.6345,9.7192,26.5408,21.6656,26.5408h119.3578c5.2742,0,10.2228-2.8298,13.577-7.7639,3.2486-4.7786,4.6437-11.0439,3.8274-17.1895-2.3882-17.9773-7.279-38.7205-15.858-67.2581-5.0805-16.8997-11.3271-32.7954-31.6893-41.5664-16.5472-7.1279-42.5205-10.0137-92.6384-10.2922h-.0054Z" fill="#838c9d" stroke-width="0"/>
    </g>
    <path d="M762.4018,228.8521c2.6663,11.0884,4.5472,20.8728,5.7395,29.8467.2817,2.1216-.179,4.2537-1.264,5.8496-.9317,1.3708-2.2194,2.157-3.5328,2.157h-119.3578c-4.9127,0-8.9095-5.3118-8.9095-11.8411v-26.0122h127.3247M764.7123,225.9122h-132.5751v28.9521c0,8.1504,5.3156,14.781,11.8495,14.781h119.3578c2.3024,0,4.4762-1.2551,5.9643-3.4443,1.4857-2.1853,2.1225-5.0605,1.7469-7.8892-1.2782-9.6211-3.327-20.1401-6.3433-32.3997h0Z" fill="#838c9d" stroke-width="0"/>
    <path d="M755.658,193.1986c-9.0268-30.0269-18.063-41.1187-86.7196-43.5698v68.7746h92.9866c-1.8771-8.8964-3.9733-17.5752-6.267-25.2048Z" fill="#515a6a" stroke-width="0"/>
    <path d="M640.5904,149.095c-4.6596-.0259-8.4532,4.6821-8.4532,10.4946v58.8139h29.9881v-68.9847c-6.6493-.1747-13.8058-.2809-21.5348-.3238Z" fill="#515a6a" stroke-width="0"/>
    <rect x="610.0159" y="327.467" width="200.4954" height="57.8032" rx="28.9016" ry="28.9016" fill="#8d96a6" stroke-width="0"/>
    <rect x="691.1619" y="265.9206" width="38.2036" height="180.8957" rx="19.1015" ry="19.1015" transform="translate(1066.6322 -353.8952) rotate(90)" fill="#515a6a" stroke-width="0"/>
    <polyline points="600.1947 337.4072 617.2075 316.3532 803.0991 316.3532 820.0338 337.3106" fill="none" stroke="#cbd4e5" stroke-linecap="round" stroke-linejoin="round" stroke-width="9.7998"/>
    <line x1="822.5608" y1="176.5358" x2="921.9859" y2="137.7558" fill="none" stroke="#939598" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2.94"/>
    <line x1="933.1533" y1="166.2941" x2="967.9915" y2="287.2851" fill="none" stroke="#939598" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2.94"/>
    <path d="M902.9787,153.2457l-65.8237,24.3382c-2.1951.8116-2.608,3.738-.7231,5.1253l5.5461,4.2856c.7992.5882,1.8413.7336,2.7709.3865l54.9189-20.2679c.5729-.2139,1.0649-.6011,1.4074-1.1077l5.3587-8.3559c1.6002-2.3671-.7753-5.3949-3.4552-4.404Z" fill="#cbd4e5" stroke-width="0"/>
    <path d="M785.0952,285.8289h33.0792c1.3733,0,2.5636-.9507,2.8672-2.29l21.7139-88.9224c.2504-1.1047-.1561-2.2551-1.0451-2.9572l-19.7995-15.6368c-1.6162-1.2764-4.0099-.5385-4.627,1.4263l-34.9936,104.5592c-.5948,1.894.8197,3.8209,2.8049,3.8209Z" fill="#cbd4e5" stroke-width="0"/>
    <path d="M823.6074,189.8499l8.5157,6.7253-21.214,80.4338h-16.4178l29.1161-87.1592M822.6325,186.5826l-30.8623,92.3865h20.649l21.9267-83.136-11.7134-9.2505h0Z" fill="#838c9d" stroke-width="0"/>
    <polyline points="949.001 337.2667 972.8034 281.7663 944.7756 297.977" fill="none" stroke="#838c9d" stroke-linecap="round" stroke-linejoin="round" stroke-width="5.8799"/>
    <path d="M955.2762,279.7995l-27.5126-90.5198c-.1558-.5126-.1686-1.058-.0369-1.5773l8.0306-31.6815c.153-.6035.1104-1.24-.1216-1.8178l-7.2245-17.993c-.9055-2.2553-4.001-2.5004-5.2501-.4157l-19.0381,31.7727c-.4548.7591-.5436,1.6826-.2416,2.5144l39.5869,115.9282c.6139,1.691,2.589,2.439,4.169,1.5789l6.2316-4.3519c1.2251-.6668,1.813-2.1027,1.4073-3.4372Z" fill="#cbd4e5" stroke-width="0"/>
    <path d="M924.6394,150.4871l2.0973,5.2239-7.8126,31.1299-5.833-17.0813,11.5483-19.2725M924.9874,146.0933l-2.0292,3.3865-11.5483,19.2725-.4675.78.2939.8606,5.833,17.0813,2.1422,6.2734,1.6136-6.4297,7.8126-31.1299.1548-.6169-.237-.5903-2.0973-5.2239-1.4709-3.6636h0Z" fill="#838c9d" stroke-width="0"/>
    <g>
      <circle cx="651.9965" cy="356.3685" r="15.8365" fill="#8d96a6" stroke-width="0"/>
      <circle cx="690.8413" cy="356.3685" r="15.8365" fill="#8d96a6" stroke-width="0"/>
      <circle cx="729.6861" cy="356.3685" r="15.8365" fill="#8d96a6" stroke-width="0"/>
      <circle cx="768.5309" cy="356.3685" r="15.8365" fill="#8d96a6" stroke-width="0"/>
    </g>
    <g>
      <path d="M918.0048,359.8758c-4.9673,0-9.8198-1.7314-13.6631-4.876l-21.6543-17.7168c-.8867-.7256-1.3452-1.8389-1.2261-2.9795.1187-1.1396.7974-2.1348,1.8145-2.6611.4805-.249,1.0205-.3799,1.561-.3799.5234,0,1.0469.123,1.5151.3564l5.0112,2.502c.1982.0996.4194.1514.6401.1514.2744,0,.541-.0781.772-.2256l38.8618-24.8496c.3315-.2129.5635-.5557.6367-.9414l1.2173-6.4404c.1743-.9219.7285-1.7363,1.5195-2.2373l8.1538-5.165c.5459-.3457,1.1719-.5283,1.8101-.5283,1.6538,0,3.062,1.1816,3.3481,2.8086l5.2217,29.7178c1.5703,8.9395-2.6704,17.9678-10.5527,22.4678l-14.2959,8.1602c-3.252,1.8555-6.9492,2.8369-10.6904,2.8369h-.0005Z" fill="#cbd4e5" stroke-width="0"/>
      <path d="M944.9747,294.8646c1.115.0002,2.1688.781,2.3827,1.9983l5.2213,29.7178c1.5037,8.5581-2.5267,17.1392-10.0731,21.4468l-14.296,8.1602c-3.1808,1.8157-6.7003,2.7083-10.2043,2.7083-4.6532,0-9.2787-1.575-13.0429-4.6548l-21.6542-17.717c-1.343-1.0989-1.1225-3.2136.4184-4.0115h0c.348-.1802.729-.2705,1.1102-.2705.3692,0,.7387.0847,1.0783.2544l5.0098,2.5017c.3406.1702.71.2544,1.0784.2544.4535,0,.9056-.1277,1.3004-.3801l38.8614-24.8499c.5602-.3584.9479-.9319,1.0714-1.5852l1.2176-6.4409c.1242-.657.5154-1.2329,1.0803-1.5908l8.1543-5.1643c.4073-.2578.8513-.3767,1.2861-.3767M944.9749,292.9046s0,0,0,0c-.8241,0-1.6315.2354-2.3348.6809l-8.1543,5.1643c-1.02.6462-1.7333,1.6968-1.9575,2.8826l-1.2176,6.4409c-.0231.1223-.0967.231-.2017.2981l-38.861,24.8499c-.0923.0588-.1839.0713-.2445.0713-.0708,0-.139-.0161-.2024-.0479l-5.0102-2.5017c-.6034-.3015-1.2791-.4609-1.9539-.4609-.697,0-1.3925.1694-2.0115.49-1.3112.679-2.1855,1.9612-2.3388,3.4297-.1533,1.469.4376,2.9041,1.5805,3.8391l21.6542,17.717c4.0179,3.2876,9.0907,5.0979,14.284,5.0979,3.9118,0,7.7764-1.0256,11.1759-2.9661l14.296-8.1602c8.2405-4.7039,12.6739-14.1428,11.0319-23.488l-5.2213-29.7178c-.3684-2.0969-2.1824-3.6189-4.313-3.6191h0Z" fill="#838c9d" stroke-width="0"/>
    </g>
    <path d="M781.6097,385.2703h-142.6923c-15.9619,0-28.9016-12.9397-28.9016-28.9016s12.9397-28.9016,28.9016-28.9016h142.6923c15.9619,0,28.9016,12.9397,28.9016,28.9016s-12.9397,28.9016-28.9016,28.9016Z" fill="none" stroke="#515a6a" stroke-dasharray="0 0 5.8366 3.8911" stroke-miterlimit="10" stroke-width="3.9199"/>
  </g>
  <g id="hrubá_stavba_grey" data-name="hrubá stavba grey">
    <polyline points="1080.8663 77.9456 1245.1132 2.45 1451.6652 77.9456" fill="none" stroke="#939598" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.94"/>
    <polygon points="1287.4408 344.2807 1245.1132 344.2807 1245.1132 2.45 1287.4408 49.432 1287.4408 344.2807" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    <g>
      <line x1="1287.4408" y1="301.9531" x2="1245.1132" y2="344.2807" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
      <line x1="1245.1132" y1="301.9531" x2="1287.4408" y2="344.2807" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    </g>
    <polyline points="1287.4408 259.6256 1245.1132 301.9531 1287.4408 301.9531 1245.1132 259.6256" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    <polyline points="1287.4408 217.2981 1245.1132 259.6256 1287.4408 259.6256 1245.1132 217.2981" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    <polyline points="1287.4408 175.3883 1245.1132 217.7158 1287.4408 217.7158 1245.1132 175.3883" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    <polyline points="1287.4408 133.365 1245.1132 175.6925 1287.4408 175.6925 1245.1132 133.365" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    <g>
      <line x1="1287.4408" y1="91.3418" x2="1245.1132" y2="133.6693" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
      <line x1="1245.1132" y1="91.3418" x2="1287.4408" y2="133.6693" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    </g>
    <polyline points="1287.4408 49.432 1245.1132 91.7595 1287.4408 91.7595 1245.1132 49.432" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    <polyline points="1267.4113 27.3247 1245.1132 49.6228 1287.4408 49.6228" fill="none" stroke="#838c9d" stroke-linejoin="round" stroke-width="4.8999"/>
    <polygon points="1297.117 77.9456 1297.117 100.1321 1476.2913 100.1321 1451.6652 77.9456 1297.117 77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1319.3035" y1="77.9456" x2="1297.117" y2="100.1321" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1341.4899" y1="100.1321" x2="1319.3035" y2="77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1363.6764" y1="77.9456" x2="1341.4899" y2="100.1321" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1385.6438" y1="100.1321" x2="1363.4574" y2="77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1407.6708" y1="77.9456" x2="1385.4843" y2="100.1321" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1429.6977" y1="100.1321" x2="1407.5113" y2="77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1451.6652" y1="77.9456" x2="1429.4788" y2="100.1321" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <rect x="1080.8663" y="77.9456" width="154.5482" height="22.1864" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <g>
      <line x1="1355.7892" y1="240.9744" x2="1355.7892" y2="377.6932" fill="none" stroke="#8d96a6" stroke-miterlimit="10" stroke-width="4.8999"/>
      <line x1="1422.6481" y1="240.9744" x2="1422.6481" y2="377.6932" fill="none" stroke="#8d96a6" stroke-miterlimit="10" stroke-width="4.8999"/>
      <line x1="1489.5069" y1="240.9744" x2="1489.5069" y2="377.6932" fill="none" stroke="#8d96a6" stroke-miterlimit="10" stroke-width="4.8999"/>
      <line x1="1556.3658" y1="240.9744" x2="1556.3658" y2="377.6932" fill="none" stroke="#8d96a6" stroke-miterlimit="10" stroke-width="4.8999"/>
      <line x1="1623.2246" y1="240.9744" x2="1623.2246" y2="377.6932" fill="none" stroke="#8d96a6" stroke-miterlimit="10" stroke-width="4.8999"/>
    </g>
    <line x1="1213.2281" y1="77.9456" x2="1235.4145" y2="100.1321" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1191.0417" y1="100.1321" x2="1213.2281" y2="77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1168.8552" y1="77.9456" x2="1191.0417" y2="100.1321" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1146.8877" y1="100.1321" x2="1169.0742" y2="77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1124.8608" y1="77.9456" x2="1147.0472" y2="100.1321" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1102.8338" y1="100.1321" x2="1125.0203" y2="77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <line x1="1103.0528" y1="100.1321" x2="1080.8663" y2="77.9456" fill="none" stroke="#838c9d" stroke-miterlimit="10" stroke-width="4.8999"/>
    <g>
      <path d="M1222.0477,107.4813h83.9585c7.3016,0,13.2295,5.9279,13.2295,13.2295v27.6528c0,7.3018-5.9282,13.23-13.23,13.23h-83.9585c-7.3016,0-13.2295-5.9279-13.2295-13.2295v-27.6528c0-7.3018,5.9282-13.23,13.23-13.23Z" fill="#cbd4e5" stroke-width="0"/>
      <path d="M1306.0059,108.951c6.4679,0,11.7598,5.292,11.7598,11.76v27.6526c0,6.468-5.2919,11.7598-11.7598,11.7598h-83.9579c-6.4679,0-11.7598-5.2917-11.7598-11.7598v-27.6526c0-6.468,5.2919-11.76,11.7598-11.76h83.9579M1306.0059,106.011h-83.9579c-8.1055,0-14.6998,6.5945-14.6998,14.7v27.6526c0,8.1055,6.5943,14.6997,14.6998,14.6997h83.9579c8.1055,0,14.6998-6.5942,14.6998-14.6997v-27.6526c0-8.1055-6.5943-14.7-14.6998-14.7h0Z" fill="#838c9d" stroke-width="0"/>
    </g>
    <path d="M1310.9063,122.6707c0-3.7826-3.0778-6.8599-6.8599-6.8599h-49.4617v28.4414h56.3215v-21.5815Z" fill="#515a6a" stroke-width="0"/>
    <rect x="1077.9264" y="106.3976" width="77.1146" height="33.2741" rx="4.8999" ry="4.8999" fill="#8d96a6" stroke-width="0"/>
    <rect x="1404.9547" y="106.3976" width="22.6499" height="9.7999" rx="2.94" ry="2.94" fill="#8d96a6" stroke-width="0"/>
    <path d="M1232.6196,351.1673h67.3148c2.7043,0,4.8999,2.1956,4.8999,4.8999v8.4719h-77.1146v-8.4719c0-2.7043,2.1956-4.8999,4.8999-4.8999Z" fill="#515a6a" stroke-width="0"/>
    <line x1="1349.125" y1="206.0113" x2="1416.2796" y2="171.5023" fill="none" stroke="#8d96a6" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1.96"/>
    <rect x="1201.6079" y="364.5391" width="129.3382" height="18.054" rx="4.8999" ry="4.8999" fill="#515a6a" stroke-width="0"/>
    <rect x="1343.7768" y="372.7933" width="291.4602" height="9.7998" rx="2.94" ry="2.94" fill="#515a6a" stroke-width="0"/>
    <rect x="1343.7768" y="325.5871" width="291.4602" height="9.7998" rx="2.94" ry="2.94" fill="#515a6a" stroke-width="0"/>
    <rect x="1343.7768" y="278.3808" width="291.4602" height="9.7998" rx="2.94" ry="2.94" fill="#515a6a" stroke-width="0"/>
    <path d="M1492.4469,231.1746h142.7901v9.7998h-142.7901c-1.6226,0-2.94-1.3173-2.94-2.94v-3.9199c0-1.6226,1.3173-2.94,2.94-2.94Z" transform="translate(3124.7439 472.149) rotate(-180)" fill="#515a6a" stroke-width="0"/>
    <path d="M1416.4832,146.5945v10.3266l5.4673,5.3299c2.691,2.6234,2.7184,6.9402.0611,9.5976l-.7712.7712c-2.6273,2.6273-6.887,2.6273-9.5143,0l-3.1537-3.1537" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="3.9199"/>
    <rect x="1409.4325" y="145.6566" width="13.6942" height="5.8868" fill="#515a6a" stroke-width="0"/>
    <line x1="1416.2796" y1="111.2976" x2="1416.2796" y2="144.2522" fill="#8d96a6" stroke="#939598" stroke-miterlimit="10" stroke-width="2.94"/>
    <rect x="1089.695" y="112.0109" width="13.4758" height="22.5263" fill="#515a6a" stroke-width="0"/>
    <rect x="1109.7458" y="112.0109" width="13.4758" height="22.5263" fill="#515a6a" stroke-width="0"/>
    <line x1="1483.4342" y1="206.0113" x2="1416.2796" y2="171.5023" fill="none" stroke="#8d96a6" stroke-linecap="round" stroke-miterlimit="10" stroke-width="1.96"/>
    <rect x="1129.7965" y="112.0109" width="13.4758" height="22.5263" fill="#515a6a" stroke-width="0"/>
    <path d="M1346.7168,200.9225h142.7901v9.7998h-142.7901c-1.6226,0-2.94-1.3173-2.94-2.94v-3.9199c0-1.6226,1.3173-2.94,2.94-2.94Z" fill="#515a6a" stroke-width="0"/>
    <path d="M1732.7672,354.83v-48.7272s-29.5144-.6786-35.3853,1.8504c-7.0622,3.0421-9.2463,8.614-11.0281,14.5408-1.6144,5.3703-3.2745,11.9301-4.3824,16.4853-.8736,3.592-1.2745,7.2806-1.1967,10.9766l.1026,4.874h51.8899Z" fill="#b1bacb" stroke-width="0"/>
    <path d="M1715.6664,336.5291v9.4385c0,2.387-1.4679,4.3291-3.2722,4.3291h-26.1596c-.4938,0-.9761-.293-1.3234-.804-.3993-.5874-.569-1.3711-.4655-2.1506.4319-3.25,1.1139-6.7942,2.0815-10.813h29.1392M1716.6664,335.5291h-30.925c-1.0875,4.4202-1.8262,8.2126-2.287,11.6814-.1354,1.0198.0942,2.0566.6298,2.8445.5365.7893,1.3203,1.2417,2.1504,1.2417h26.1596c2.3557,0,4.2722-2.3906,4.2722-5.3291v-10.4385h0Z" fill="#838c9d" stroke-width="0"/>
    <path d="M1689.0058,323.7346c3.2545-10.8259,2.907-13.0223,27.6605-13.906v22.9933h-29.92c.6768-3.2075,1.4325-6.3366,2.2595-9.0874Z" fill="#58595b" stroke-width="0"/>
    <path d="M1730.7795,356.9116v12.4945h-12.4057c-1.1363-6.3737-6.7151-11.2291-13.4103-11.2291s-12.2738,4.8555-13.4102,11.2291h-13.4855v-12.4945h52.7117Z" fill="#8d96a6" stroke-width="0"/>
    <circle cx="1704.9635" cy="371.8027" r="11.3894" fill="#58595b" stroke-width="0"/>
    <path d="M1704.9634,378.1444c3.497,0,6.3419-2.8449,6.3419-6.3415s-2.8449-6.3419-6.3419-6.3419-6.3417,2.8449-6.3417,6.3419,2.8449,6.3415,6.3417,6.3415Z" fill="#8d96a6" stroke-width="0"/>
    <path d="M1719.9705,297.8718h23.1019c.5669,0,1.0264.4595,1.0264,1.0264v9.904c0,.5669.4595,1.0264,1.0264,1.0264h63.1726c.5669,0,1.0264.4596,1.0264,1.0264v9.1897c0,.5669-.4595,1.0264-1.0264,1.0264h-2.3494c-.437,0-.826.2767-.9695.6894l-8.2005,33.7875c-.2318.667.2634,1.3634.9695,1.3634h10.5498c.5669,0,1.0264.4596,1.0264,1.0264v10.4416c0,.5669-.4595,1.0264-1.0264,1.0264h-2.894c-1.1397-6.533-6.8482-11.5164-13.7023-11.5164s-12.5626,4.9835-13.7023,11.5164h-45.1909v-12.4945h1.0264c.5669,0,1.0264-.4596,1.0264-1.0264v-52.368c0-.5669-.4595-1.0264-1.0264-1.0264h-13.8642c-.5669,0-1.0264-.4596-1.0264-1.0264v-2.5661c0-.5669.4595-1.0264,1.0264-1.0264Z" fill="#b1bacb" stroke-width="0"/>
    <circle cx="1791.7015" cy="371.8027" r="11.3894" fill="#58595b" stroke-width="0"/>
    <path d="M1791.7014,378.1444c3.497,0,6.3419-2.8449,6.3419-6.3415s-2.8449-6.3419-6.3419-6.3419-6.3417,2.8449-6.3417,6.3419,2.8449,6.3415,6.3417,6.3415Z" fill="#8d96a6" stroke-width="0"/>
    <rect x="1778.7223" y="321.3253" width="4.8347" height="33.5047" rx="2.4174" ry="2.4174" fill="#838c9d" stroke-width="0"/>
    <rect x="1765.3422" y="321.3253" width="4.8347" height="33.5047" rx="2.4174" ry="2.4174" fill="#838c9d" stroke-width="0"/>
    <rect x="1751.9621" y="321.3253" width="4.8347" height="33.5047" rx="2.4174" ry="2.4174" fill="#838c9d" stroke-width="0"/>
  </g>
  <g id="dokončenie_stavby_grey" data-name="dokončenie stavby grey">
    <rect x="2040.1496" y="162.8387" width="325.972" height="25.8273" fill="#b4bdce" stroke-width="0"/>
    <g>
      <rect x="2013.7157" y="189.6658" width="378.8408" height="191.8369" fill="#fff" stroke-width="0"/>
      <path d="M2391.5562,190.6661v189.8362h-376.8409v-189.8362h376.8409M2393.5562,188.6661h-380.8409v193.8362h380.8409v-193.8362h0Z" fill="#b6bfd0" stroke-width="0"/>
    </g>
    <rect x="2099.6555" y="144.1614" width="206.9603" height="44.5046" fill="#c7d0e0" stroke-width="0"/>
    <rect x="1999.2848" y="210.0774" width="407.7017" height="7.1299" fill="#c7d0e0" stroke-width="0"/>
    <rect x="2030.5863" y="156.8228" width="345.0988" height="6.0159" fill="#9ba4b4" stroke-width="0"/>
    <rect x="2025.862" y="174.3783" width="25.8273" height="2.748" transform="translate(1863.0233 2214.5279) rotate(-90)" fill="#9ba4b4" stroke-width="0"/>
    <rect x="2354.5821" y="174.3783" width="25.8273" height="2.748" transform="translate(2191.7434 2543.248) rotate(-90)" fill="#9ba4b4" stroke-width="0"/>
    <rect x="2166.525" y="130.632" width="73.2213" height="13.5294" fill="#9ba4b4" stroke-width="0"/>
    <rect x="1999.2848" y="263.333" width="407.7017" height="7.1299" fill="#c7d0e0" stroke-width="0"/>
    <rect x="2056.0422" y="217.2072" width="18.1338" height="46.1257" fill="#8d96a6" stroke-width="0"/>
    <rect x="2081.5218" y="217.2072" width="18.1338" height="46.1257" fill="#8d96a6" stroke-width="0"/>
    <rect x="2174.974" y="217.2072" width="18.1338" height="46.1257" fill="#8d96a6" stroke-width="0"/>
    <rect x="2199.6248" y="217.2072" width="18.1338" height="46.1257" fill="#8d96a6" stroke-width="0"/>
    <rect x="2223.5398" y="217.2072" width="18.1338" height="46.1257" fill="#8d96a6" stroke-width="0"/>
    <rect x="2301.5393" y="217.2072" width="18.1338" height="46.1257" fill="#8d96a6" stroke-width="0"/>
    <rect x="2347.5296" y="217.2072" width="18.1338" height="46.1257" fill="#8d96a6" stroke-width="0"/>
    <rect x="2356.5964" y="270.4628" width="18.1338" height="46.1257" transform="translate(4731.3266 587.0514) rotate(180)" fill="#8d96a6" stroke-width="0"/>
    <rect x="2331.1169" y="270.4628" width="18.1338" height="46.1257" transform="translate(4680.3676 587.0514) rotate(180)" fill="#8d96a6" stroke-width="0"/>
    <rect x="2237.6646" y="270.4628" width="18.1338" height="46.1257" transform="translate(4493.463 587.0514) rotate(180)" fill="#8d96a6" stroke-width="0"/>
    <rect x="2180.6744" y="270.4628" width="18.1338" height="46.1257" transform="translate(4379.4825 587.0514) rotate(180)" fill="#8d96a6" stroke-width="0"/>
    <rect x="2156.7594" y="270.4628" width="18.1338" height="46.1257" transform="translate(4331.6526 587.0514) rotate(180)" fill="#8d96a6" stroke-width="0"/>
    <rect x="2111.0994" y="270.4628" width="18.1338" height="46.1257" transform="translate(4240.3325 587.0514) rotate(180)" fill="#8d96a6" stroke-width="0"/>
    <rect x="2065.1091" y="270.4628" width="18.1338" height="46.1257" transform="translate(4148.3519 587.0514) rotate(180)" fill="#8d96a6" stroke-width="0"/>
    <rect x="1999.2848" y="316.5885" width="407.7017" height="7.1299" fill="#c7d0e0" stroke-width="0"/>
    <rect x="1960.9519" y="364.0884" width="486.2442" height="18.4139" fill="#8b94a4" stroke-width="0"/>
    <g>
      <rect x="2144.2484" y="166.7237" width="23.2566" height="21.9422" fill="#838c9d" stroke-width="0"/>
      <rect x="2170.2819" y="166.7237" width="23.2566" height="21.9422" fill="#838c9d" stroke-width="0"/>
      <rect x="2218.4812" y="166.7237" width="15.6741" height="8.0507" fill="#838c9d" stroke-width="0"/>
    </g>
    <g>
      <rect x="2020.7569" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2046.7904" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2072.824" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2117.2349" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2143.2684" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2169.3019" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2213.7128" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2239.7463" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2265.7799" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2310.1907" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2336.2243" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
      <rect x="2362.2578" y="329.1035" width="23.2566" height="34.9849" fill="#838c9d" stroke-width="0"/>
    </g>
    <g>
      <line x1="2053.88" y1="155.8439" x2="2055.35" y2="155.8439" fill="none" stroke="#c57b42" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2075.321" y1="155.8439" x2="2340.9358" y2="155.8439" fill="none" stroke="#c57b42" stroke-dasharray="0 0 2.9957 19.971" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2350.9213" y1="155.8439" x2="2352.3912" y2="155.8439" fill="none" stroke="#c57b42" stroke-miterlimit="10" stroke-width="2.94"/>
    </g>
    <g>
      <g>
        <g clip-path="url(#clippath)">
          <rect x="2374.354" y="254.5704" width="96.5533" height="92.1705" fill="#b4bdce" stroke-width="0"/>
          <g>
            <path d="M2417.609,340.75s-12.0761-5.1232-18.3176-10.9577c-1.6621-1.5537-5.8232-5.1171-7.5021-7.3514-4.6256-6.1557-6.2749-11.9161-6.2749-11.9161" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.9679"/>
            <path d="M2389.7146,303.9189s6.0525,6.5406,6.5406,11.1289-1.483,10.5896-1.483,10.5896" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2418.9933,338.3638s-11.3163-14.3348-13.7569-28.8804c-2.4405-14.5456-6.0525-24.0149-6.0525-24.0149" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.9679"/>
            <path d="M2419.1963,335.4507s5.7347-29.7143,6.9311-48.7132c.6629-10.526.8531-22.1103.8531-22.1103" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.9679"/>
            <path d="M2451.9687,300.7095s-1.7219,8.7495-11.9123,20.4716c-7.2376,8.3255-17.6925,10.9003-17.6925,10.9003" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2420.563,336.0364s6.2836-13.3808,11.2135-27.1455c4.9299-13.7647,7.9878-29.6215,7.9878-29.6215" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.9679"/>
            <path d="M2406.002,270.8377s.761,19.9752,2.9086,34.2647c2.1477,14.2895,11.1643,25.9152,11.1643,25.9152" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.9679"/>
            <path d="M2415.2404,264.6272s1.1422,18.7485-.8825,29.5845c-2.0247,10.836-4.712,13.8959-4.712,13.8959" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2433.7797,276.9353s-3.2652,12.6459-2.4842,18.4056c.781,5.7597,1.9709,9.1634,1.9709,9.1634" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2392.5523,291.4137s5.0432,11.0361,7.9587,12.5052c2.9156,1.4691,4.1837,2.5163,4.1837,2.5163" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2382.811,324.9924s8.719,8.6196,15.9288,9.1501c3.0277.2228,4.813-.9983,4.813-.9983" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2449.5882,297.3734s-6.3234,16.8248-14.4663,23.0352c-3.0231,2.3057-9.246,3.5638-9.246,3.5638" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.9679"/>
            <path d="M2419.1963,263.611s-1.0333,21.1156,0,28.3424c1.0333,7.2267,5.3016,10.1744,5.3016,10.1744" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2442.9261,290.0337s-4.178,7.7408-4.7425,12.094c-.5646,4.3531,1.5808,13.4808,1.5808,13.4808" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2393.0237,299.0515s2.7377,3.6938,2.9303,6.0509-.9602,6.2083-.9602,6.2083" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".656"/>
            <path d="M2386.5191,306.1117s3.3375,5.5198,5.9691,6.7393c2.6315,1.2195,3.7671,2.1968,3.7671,2.1968" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".656"/>
            <path d="M2383.1815,318.2424s5.5198,4.5208,6.8676,7.3951c1.3479,2.8743,1.5909,6.0293,1.5909,6.0293" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".656"/>
            <path d="M2396.9168,288.7822s2.4753,6.0933,2.624,9.7611c.1487,3.6678-.3569,4.2778-.3569,4.2778" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".656"/>
            <path d="M2409.6459,268.564s.1555,8.554,1.6584,12.9261c1.5029,4.3722,3.8146,6.8489,3.8146,6.8489" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2423.089,266.4608s.2024,6.0598-1.4715,9.4958c-1.2811,2.6297-2.8789,4.6454-2.8789,4.6454" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2446.4097,294.1967s-2.8009,7.4463-4.9187,9.4275-3.3322,1.9943-3.3322,1.9943" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
            <path d="M2452.8313,310.2507s-7.0482,13.6399-11.2036,14.4144c-4.2851.7987-5.6655.4336-5.6655.4336" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".656"/>
          </g>
        </g>
        <path d="M2409.5484,344.0973l-14.7099-6.7611c-8.4571-4.4874-11.7549-14.9265-7.4105-23.458l19.803-38.8897c4.0389-7.9317,15.0835-8.6759,20.15-1.3577l20.4765,29.5771c5.0808,7.3389,3.8412,17.3229-2.8801,23.1964l-17.6296,15.406c-5.4777,4.7868-11.3734,5.6965-17.7993,2.2869Z" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3119"/>
      </g>
      <polygon points="2415.6702 325.737 2417.291 331.6334 2414.6676 382.448 2420.9935 382.448 2421.4903 336.0364 2420.0749 331.0176 2415.6702 325.737" fill="#8b94a4" stroke-width="0"/>
    </g>
    <g>
      <g clip-path="url(#clippath-1)">
        <rect x="1929.6785" y="241.7659" width="92.6984" height="101.3996" fill="#b4bdce" stroke-width="0"/>
        <path d="M1971.2065,336.5748s-11.5939-5.6362-17.5863-12.055c-1.5958-1.7093-5.5907-5.6295-7.2025-8.0875-4.441-6.7721-6.0243-13.1092-6.0243-13.1092" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.0225"/>
        <path d="M1944.4258,296.0558s5.8109,7.1956,6.2795,12.2432-1.4237,11.65-1.4237,11.65" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1972.5355,333.9496s-10.8645-15.7701-13.2076-31.7722c-2.3431-16.0021-5.8109-26.4196-5.8109-26.4196" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.0225"/>
        <path d="M1972.7304,330.7448s5.5057-32.6896,6.6544-53.5909c.6364-11.5799.819-24.3242.819-24.3242" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.0225"/>
        <path d="M2004.1943,292.5249s-1.6531,9.6256-11.4367,22.5214c-6.9487,9.1592-16.9861,11.9918-16.9861,11.9918" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1974.0426,331.3892s6.0328-14.7206,10.7658-29.8635c4.7331-15.1429,7.6689-32.5875,7.6689-32.5875" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.0225"/>
        <path d="M1960.0629,259.6621s.7306,21.9754,2.7925,37.6957c2.0619,15.7203,10.7186,28.5101,10.7186,28.5101" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.0225"/>
        <path d="M1968.9324,252.8297s1.0966,20.6258-.8473,32.5468c-1.9438,11.921-4.5238,15.2873-4.5238,15.2873" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1986.7316,266.3702s-3.1348,13.9122-2.385,20.2486c.7498,6.3364,1.8922,10.0809,1.8922,10.0809" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1947.1502,282.2984s4.8418,12.1412,7.641,13.7574,4.0167,2.7683,4.0167,2.7683" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1937.7979,319.2393s8.3708,9.4826,15.2928,10.0663c2.9068.2451,4.6209-1.0983,4.6209-1.0983" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M2001.909,288.8548s-6.0709,18.5094-13.8887,25.3418c-2.9024,2.5366-8.8769,3.9207-8.8769,3.9207" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.0225"/>
        <path d="M1972.7304,251.7117s-.992,23.2299,0,31.1803c.992,7.9504,5.0899,11.1931,5.0899,11.1931" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1995.5128,280.7802s-4.0112,8.5159-4.5532,13.3049,1.5177,14.8307,1.5177,14.8307" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1947.6028,290.701s2.6284,4.0637,2.8133,6.6568-.9219,6.83-.9219,6.83" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".6742"/>
        <path d="M1941.3579,298.4681s3.2043,6.0725,5.7307,7.4141c2.5265,1.3416,3.6167,2.4168,3.6167,2.4168" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".6742"/>
        <path d="M1938.1536,311.8134s5.2994,4.9735,6.5934,8.1356c1.294,3.1621,1.5274,6.6331,1.5274,6.6331" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".6742"/>
        <path d="M1951.3405,279.4033s2.3764,6.7035,2.5192,10.7385-.3426,4.7061-.3426,4.7061" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".6742"/>
        <path d="M1963.5613,257.1607s.1492,9.4105,1.5922,14.2204c1.4429,4.8099,3.6623,7.5347,3.6623,7.5347" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1976.4677,254.847s.1943,6.6666-1.4128,10.4466c-1.23,2.893-2.764,5.1106-2.764,5.1106" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M1998.8573,285.36s-2.6891,8.1919-4.7223,10.3714c-2.0332,2.1795-3.1991,2.194-3.1991,2.194" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
        <path d="M2005.0225,303.0215s-6.7668,15.0057-10.7563,15.8578c-4.114.8787-5.4393.477-5.4393.477" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".6742"/>
      </g>
      <path d="M1963.4678,340.2573l-14.1226-7.438c-8.1194-4.9368-11.2855-16.4211-7.1147-25.8068l19.0123-42.7837c3.8776-8.7259,14.4813-9.5446,19.3455-1.4937l19.6589,32.5387c4.8779,8.0738,3.6878,19.0574-2.7651,25.5191l-16.9257,16.9486c-5.259,5.2661-10.9193,6.2669-17.0886,2.5158Z" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.3483"/>
    </g>
    <polygon points="1969.3451 320.0585 1970.9012 326.5453 1968.3825 382.448 1974.4559 382.448 1974.9329 331.3892 1973.574 325.8679 1969.3451 320.0585" fill="#8b94a4" stroke-width="0"/>
    <g>
      <g>
        <g clip-path="url(#clippath-2)">
          <rect x="1955.6624" y="211.8685" width="128.7952" height="122.9488" transform="translate(4040.12 546.6857) rotate(180)" fill="#b4bdce" stroke-width="0"/>
          <g>
            <path d="M2026.7586,326.8259s16.1086-6.834,24.4344-14.6168c2.2171-2.0725,7.7677-6.8259,10.0072-9.8062,6.1703-8.2113,8.3702-15.8952,8.3702-15.8952" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.6251"/>
            <path d="M2063.9677,277.6959s-8.0737,8.7248-8.7248,14.8451c-.6511,6.1204,1.9782,14.1258,1.9782,14.1258" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M2024.912,323.6428s15.0952-19.1216,18.3507-38.5244c3.2555-19.4028,8.0737-32.0342,8.0737-32.0342" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.6251"/>
            <path d="M2024.6412,319.757s-7.6497-39.6367-9.2456-64.9799c-.8842-14.0409-1.1379-29.4936-1.1379-29.4936" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.6251"/>
            <path d="M1980.9252,273.4147s2.2969,11.6712,15.8901,27.3077c9.6545,11.1057,23.6005,14.5403,23.6005,14.5403" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M2022.8181,320.5383s-8.3819-17.849-14.9581-36.2101c-6.5761-18.3611-10.6552-39.513-10.6552-39.513" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.6251"/>
            <path d="M2042.2415,233.5679s-1.0151,26.6455-3.8799,45.7067c-2.8648,19.0612-14.8924,34.569-14.8924,34.569" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.6251"/>
            <path d="M2029.9182,225.2835s-1.5236,25.0091,1.1772,39.4635c2.7007,14.4544,6.2854,18.5361,6.2854,18.5361" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M2005.188,241.7016s4.3555,16.8688,3.3137,24.5518c-1.0418,7.683-2.629,12.2233-2.629,12.2233" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M2060.1824,261.0147s-6.7272,14.7214-10.6164,16.6811c-3.8892,1.9597-5.5808,3.3566-5.5808,3.3566" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M2073.1766,305.8063s-11.6305,11.4979-21.2479,12.2056c-4.0388.2972-6.4202-1.3317-6.4202-1.3317" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M1984.1006,268.9646s8.435,22.443,19.297,30.7273c4.0327,3.0757,12.3335,4.7539,12.3335,4.7539" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="2.6251"/>
            <path d="M2024.6412,223.9279s1.3783,28.1667,0,37.8067c-1.3783,9.64-7.0719,13.5719-7.0719,13.5719" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M1992.9874,259.174s5.5731,10.3257,6.3262,16.1325-2.1087,17.9824-2.1087,17.9824" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M2059.5535,271.2031s-3.652,4.9273-3.9088,8.0715,1.2808,8.2815,1.2808,8.2815" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".875"/>
            <path d="M2068.2303,280.6208s-4.452,7.363-7.9623,8.9897-5.025,2.9304-5.025,2.9304" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".875"/>
            <path d="M2072.6823,296.8023s-7.363,6.0305-9.1609,9.8645c-1.7979,3.8341-2.1222,8.0427-2.1222,8.0427" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".875"/>
            <path d="M2054.3605,257.5045s-3.3018,8.1281-3.5002,13.0207c-.1983,4.8926.4761,5.7063.4761,5.7063" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".875"/>
            <path d="M2037.3808,230.5349s-.2074,11.4104-2.2122,17.2425c-2.0048,5.8321-5.0883,9.136-5.0883,9.136" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M2019.4487,227.7294s-.27,8.0834,1.9629,12.6667c1.7089,3.5079,3.8403,6.1966,3.8403,6.1966" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M1988.3405,264.7271s3.7362,9.9329,6.5612,12.5756c2.8249,2.6427,4.4449,2.6603,4.4449,2.6603" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
            <path d="M1979.7746,286.142s9.4018,18.1947,14.9449,19.2278c5.716,1.0654,7.5573.5783,7.5573.5783" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width=".875"/>
          </g>
        </g>
        <path d="M2037.5108,331.291l19.622-9.0188c11.2811-5.9859,15.6801-19.9109,9.8851-31.2912l-26.4157-51.876c-5.3876-10.5803-20.1204-11.573-26.8786-1.8111l-27.3141,39.4538c-6.7774,9.7896-5.1238,23.1074,3.8419,30.9424l23.5166,20.5505c7.3068,6.3853,15.1712,7.5988,23.743,3.0505Z" fill="none" stroke="#8b94a4" stroke-miterlimit="10" stroke-width="1.75"/>
      </g>
      <polygon points="2029.3448 306.7996 2027.1828 314.665 2030.6822 382.448 2022.2439 382.448 2021.5812 320.5383 2023.4692 313.8436 2029.3448 306.7996" fill="#8b94a4" stroke-width="0"/>
    </g>
    <rect x="1960.9519" y="364.0884" width="486.2442" height="18.4139" fill="url(#listy_lvd_2)" stroke-width="0"/>
  </g>
  <g id="kolaudácia_grey" data-name="kolaudácia grey">
    <path d="M2730.9284,185.4288l34.6093,14.6271c15.0065,6.3422,26.9315,18.3106,33.2192,33.3401l46.533,111.2279,4.4922,24.144-66.157-.4309-52.6968-182.9081Z" fill="#b6bfd0" stroke-width="0"/>
    <g clip-path="url(#clippath-3)">
      <rect x="2630.0691" y="45.2884" width="178.3944" height="187.8249" transform="translate(-3.5428 149.0215) rotate(-3.1371)" fill="#c7d0e0" stroke-width="0"/>
      <g opacity=".38">
        <path d="M2742.6253,98.1142l.755,13.7752c.009.1635-.0167.3271-.0752.48l-3.5083,9.169c-1.5728,4.1106-2.1966,8.5235-1.8247,12.909l.2554,3.0122c.0665.7842-.0506,1.5731-.3419,2.3042l-4.9572,12.44c-.029.0729-.0506.1485-.0645.2257l-1.46,8.1462c-.0852.4755-.4551.849-.9296.9389l-15.8187,2.9981c-.2146.0407-.4362.0202-.6397-.0591l-27.0546-10.5399c-3.3257-1.2956-5.8658-4.0558-6.8813-7.4775l-2.891-9.7415c-.6126-2.0643.9438-4.1621,3.0965-4.1164.2619.0056.5418.032.8408.0829,2.1667.3691,5.0169-.6428,6.9557-1.5143.6646-.2988.5237-1.2806-.1978-1.3829-2.3343-.3307-5.7634-.9887-7.3599-2.1384-1.016-.7317-2.1154-2.5151-3.0697-4.3939-1.4449-2.8447.2312-6.2823,3.3641-6.887l25.3873-4.9005c.6165-.119,1.2163.272,1.3563.884l2.1051,9.2054c.0371.1621.039.3302.0057.4931l-1.2307,6.0169c-.0519.2539-.0177.5179.0973.7502l.6601,1.3335c.1604.324.4622.5552.8168.6257l11.6878,2.3236c.3256.0647.6634-.0126.9285-.2125l6.2676-4.7271c.1684-.127.2991-.2973.3783-.4928h0c.3975-.9816-.615-1.9414-1.574-1.4919l-4.5358,2.1258c-.1606.0753-.3363.1129-.5136.1099l-9.5621-.1583c-.4573-.0076-.8678-.282-1.0496-.7017l-.4496-1.0381c-.1505-.3474-.1227-.7463.0744-1.0695l3.7577-6.1613c.1203-.1973.1793-.426.1693-.6569l-.7129-16.4724c-.0516-1.1914-.6358-2.2964-1.5914-3.0099l-4.1812-3.1218c-1.6166-1.207-3.6398-1.7365-5.6404-1.4763l-15.3964.8627-12.0442,2.0971s4.2256-10.088,13.9358-9.9384c12.5943.194,43.3858-2.9011,48.1263-3.1795.7086-.0416,1.1436-.8064.9395-1.4864l-.9247-3.0807c-.0558-.186-.0643-.3829-.0246-.573l1.3431-6.4318c.0893-.4278-.0683-.8695-.4082-1.1442l-30.5608-24.6908c-.2724-.22-.6292-.3059-.9719-.2338l-45.249,9.5217c-.1874.0394-.3623.1245-.509.2476l-12.4172,10.4219c-.2808.2357-.4343.5899-.4142.956l4.3646,79.6349c.0076.1379.0396.2733.0945.4l15.8995,36.6359c.3571.823,1.4715.9502,2.005.229l6.5863-8.9035c.164-.2217.244-.4943.2258-.7694l-.8193-12.4209c-.0551-.8355.763-1.4545,1.5521-1.1743l18.1086,6.4302c.317.1125.5698.3566.6935.6694l4.8593,12.2908c.2883.7293,1.372.5216,1.3703-.2626l-.022-10.1588c-.0017-.7821.7525-1.3436,1.5013-1.1177l11.2681,3.3996c.3084.0931.5646.3097.7075.5984l.7993,1.6147c1.1135,2.2496,1.3867,4.8229.7702,7.2561l-1.9324,7.6273c-.1225.4837.4802.8123.8204.4473l6.4877-6.9606c.1524-.1635.2546-.3675.2942-.5875l1.2655-7.0188c.0293-.1623.0927-.3166.1861-.4526l2.0221-2.9452c.1646-.2397.4128-.4092.696-.4751l16.1163-3.755c.4896-.1141.8508-.5294.8958-1.0301l6.2768-69.7832c.0665-.7391-.5659-1.3511-1.3024-1.2604l-7.0384.8662c-.6081.0748-1.0543.608-1.0208,1.2198Z" fill="#838c9d" stroke-width="0"/>
        <path d="M2720.8535,105.2831l-.4456-3.3988c-.1379-1.0522.0182-2.122.4512-3.0908l1.1815-2.6439c.989-2.2132,3.1519-3.6702,5.5745-3.7552l10.0438-.3527c1.3188-.0463,2.3748,1.0826,2.2407,2.3953l-.9814,9.6101c-.1062,1.0402-.9374,1.8577-1.9792,1.9467l-13.7432,1.1742c-1.1592.099-2.1912-.7314-2.3424-1.8849Z" fill="#838c9d" stroke-width="0"/>
      </g>
      <path d="M2677.5357,73.5143l-5.5582,4.2958,5.0531,9.9794-8.3007,15.3322,2.2511,10.5348-4.1532,4.09-3.2179-7.0335s-3.0857-1.7061-4.2148-1.8782c-1.0455-.1593-2.7105,1.5097-2.7105,1.5097l-5.0474-34.1084,25.8986-2.7218Z" fill="#6b7484" stroke-width="0"/>
    </g>
    <path d="M2703.3378,142.8222l2.9423,1.5693c.5897.2635,3.8831.5138,4.5073.3481l8.7017-2.5884c1.9681-.5226,3.9846-.8421,6.0179-.9535l5.775-.3165c.5281-.0289.7794-.6637.4143-1.0463l-2.8735-3.0108c-.7316-.7665-1.826-1.0677-2.8467-.7834l-4.5502,1.2673c-.0407.0113-.0825.0185-.1247.0213l-6.1862.4157c-1.5822.1063-3.0693.7934-4.1758,1.9293l-2.2648,2.1143c-.1084.1113-2.5469,1.0714-2.7021,1.0799l-2.5384-.565c-.2843.0156-.3559.4026-.0959.5188Z" fill="#838c9d" opacity=".55" stroke-width="0"/>
    <path d="M2718.0698,148.0785l3.8656-1.3709c1.0076-.3574,2.0841-.4778,3.1458-.352l3.0556.362.8134,3.0302-3.8588-.7807c-1.0368-.2098-2.1135-.0823-3.0727.3637l-4.8359,2.2487.887-3.501Z" fill="#838c9d" opacity=".55" stroke-width="0"/>
    <path d="M2650.0409,97.2621l2.4519-26.8329c.6368-6.9687,3.5599-13.5312,8.3143-18.6658l2.7282-2.9463c3.9264-4.2404,9.3089-6.8421,15.071-7.2847l27.0532-2.0782c6.6393-.51,13.2429,1.3634,18.6253,5.2839l2.6153,1.905c3.5213,2.5649,6.1262,6.1924,7.4316,10.3486l6.1411,19.5532c.2211.7039-.2633,1.4323-.9979,1.5007l-34.3495,1.8133-26.2054-.5499-14.1909,3.2582-8.4387,8.3315-.3066,8.2456c-.086.2774-.2731.5124-.5242.6584l-2.4282,1.4119-2.9904-3.9527Z" fill="#b1bacb" stroke="#838c9d" stroke-miterlimit="10" stroke-width="1.0934"/>
    <path d="M2656.2813,103.4977s-4.3237,1.667-6.5875-1.5291c-1.2658-1.7871-2.2573-4.6724-.7789-8.9635,1.4546-4.2221,6.021-11.6712,21.3878-13.5658,12.4889-1.5398,20.6385-1.8144,53.905-4.3229,10.9809-.828,13.7328-1.9113,13.8591-3.069.1699-1.557-.3014-2.3784-.3014-2.3784l5.5383-.3891s8.6585,11.1541-8.0505,12.2927c-34.9807,2.3837-43.9326-3.8018-59.0556-.7645-15.123,3.0373-18.2276,11.6086-19.3871,14.6342-1.6777,4.3783-.5292,8.0552-.5292,8.0552Z" fill="#838c9d" stroke-width="0"/>
    <polygon points="2687.941 111.7793 2691.2816 105.4965 2700.5486 102.7348 2705.1221 107.881 2694.5225 108.6986 2687.941 111.7793" fill="#515a6a" stroke-width="0"/>
    <polygon points="2721.7622 105.7489 2725.2468 100.2153 2732.2832 100.3228 2732.5026 104.3261 2730.3375 103.8944 2721.7622 105.7489" fill="#515a6a" stroke-width="0"/>
    <path d="M2617.8177,215.1678l46.5253-29.0319,50.0175,15.4633,24.6852-14.1318,13.6256,11.8629c20.6014,17.9362,33.9091,42.8054,37.4021,69.8965l12.8345,99.5411-209.4222-4.6794,7.2741-120.2474c.7111-11.7549,7.067-22.4389,17.0579-28.6732Z" fill="#8b94a4" stroke-width="0"/>
    <polygon points="2720.8591 98.7935 2723.5749 92.866 2740.9978 91.4389 2740.222 96.4361 2720.8591 98.7935" fill="#6b7484" stroke-width="0"/>
    <polygon points="2707.0229 94.5423 2686.1466 94.9174 2676.7926 105.1896 2686.6824 101.2795 2706.5445 99.4662 2707.0229 94.5423" fill="#6b7484" stroke-width="0"/>
    <polygon points="2705.9295 139.3993 2701.1162 141.8426 2700.4595 146.4121 2705.9295 139.3993" fill="#838c9d" opacity=".55" stroke-width="0"/>
    <g clip-path="url(#clippath-4)">
      <rect x="2665.9213" y="26.5784" width="73.9086" height="45.3441" transform="translate(1.3552 147.9898) rotate(-3.1371)" fill="#b1bacb" stroke-width="0"/>
      <path d="M2669.954,38.2754s.4922-1.1157,5.6164-1.6032c5.1243-.4876,8.6753-.0399,12.2099,3.1274,3.0552,2.7377,8.6701,15.9057,10.9123,24.1377.0592.2174,31.0805-3.4874,31.0805-3.4874l1.8676,1.8662s-33.6056,4.2557-33.5667,4.4333l-28.9011-22.5286.7812-5.9454Z" fill="#838c9d" stroke-width="0"/>
    </g>
    <polygon points="2928.9573 213.6423 2814.9903 213.6423 2791.4706 374.9459 2905.4377 374.9459 2928.9573 213.6423" fill="#fff" stroke="#8d96a6" stroke-miterlimit="10" stroke-width="2.94"/>
    <g>
      <line x1="2817.0239" y1="260.8836" x2="2914.6348" y2="260.8836" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2814.8707" y1="276.2045" x2="2912.4816" y2="276.2045" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2812.7175" y1="291.5255" x2="2910.3284" y2="291.5255" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2810.5643" y1="306.8465" x2="2908.1751" y2="306.8465" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2808.411" y1="322.1674" x2="2906.0219" y2="322.1674" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2806.2578" y1="337.4884" x2="2903.8687" y2="337.4884" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="2.94"/>
      <line x1="2804.1046" y1="352.8094" x2="2901.7155" y2="352.8094" fill="none" stroke="#515a6a" stroke-miterlimit="10" stroke-width="2.94"/>
    </g>
    <polygon points="2698.3299 213.6423 2717.5227 228.7515 2727.3465 365.5792 2758.0058 368.7678 2724.7631 227.2798 2730.1591 205.2051 2719.6123 197.8468 2707.3485 200.5449 2698.3299 213.6423" fill="#515a6a" stroke-width="0"/>
    <path d="M2754.8761,356.8948l21.4771-14.5143c.404-.273.8804-.4189,1.3679-.4189h15.7026c1.3492,0,2.443,1.0938,2.443,2.443v.4718c0,1.3649,1.1183,2.465,2.483,2.4427l17.6103-.2887c.3216-.0053.639-.074.934-.2023l11.6558-5.0678c1.6135-.7015,3.4171.481,3.4171,2.2404v14.364c0,.522-.1672,1.0302-.4771,1.4503l-14.0316,19.0207c-.4272.5791-1.088.9404-1.8061.9875l-39.9489,2.6196c-.0532.0035-.1065.0052-.1599.0052h-33.076c-1.1347,0-2.12-.7813-2.3787-1.8862l-4.7399-20.2483c-.3588-1.5328.8045-2.9998,2.3787-2.9998h15.7806c.4876,0,.9639-.1459,1.3679-.4189Z" fill="#c7d0e0" stroke-width="0"/>
    <path d="M2612.0916,222.1563l-3.3155,2.7261c-11.8749,9.7638-18.8743,24.2413-19.1511,39.6124l-1.6484,91.5358c-.3114,17.293,13.9116,31.3274,31.1989,30.7852l137.5211-4.3134-6.4528-33.4071-97.9314-10.6282-.5718-69.4785c-.1221-14.835-4.6665-29.2965-13.052-41.5348l-1.1559-1.687c-5.7789-8.434-17.5436-10.1036-25.4408-3.6103Z" fill="#b6bfd0" stroke-width="0"/>
    <path d="M2668.1418,180.5331l-5.4077,5.6028c-3.6862,3.9508-2.6611,10.3165,2.0788,12.9105l42.2791,23.2689,7.2686-20.7162,19.1355,15.6081,6.6382-24.8838c.7927-2.9716-.1651-6.1358-2.4727-8.1689l-8.7113-7.6749-12.0172,17.2517c-.5969.8569-1.5055,1.4461-2.5314,1.6415l-2.1384.4073c-.7729.1472-1.5721.063-2.2973-.2421l-37.6981-15.8582c-.8203-.3451-3.5188.2024-4.1259.8531Z" fill="#f1f2f2" stroke-width="0"/>
    <g>
      <path d="M2950.5096,247.4383c-33.8125,0-61.3203-27.5078-61.3203-61.3203s27.5078-61.3193,61.3203-61.3193,61.3203,27.5078,61.3203,61.3193-27.5078,61.3203-61.3203,61.3203Z" fill="#fff" stroke-width="0"/>
      <path d="M2950.5096,127.7376c32.1912,0,58.3806,26.1895,58.3806,58.3806s-26.1895,58.3804-58.3806,58.3804-58.3804-26.1895-58.3804-58.3804,26.1892-58.3806,58.3804-58.3806M2950.5096,121.8577c-35.49,0-64.2604,28.7705-64.2604,64.2605s28.7704,64.2603,64.2604,64.2603,64.2605-28.7703,64.2605-64.2603-28.7705-64.2605-64.2605-64.2605h0Z" fill="#8b94a4" stroke-width="0"/>
    </g>
    <polyline points="2916.5079 181.0237 2943.3431 207.8589 2988.2032 162.9987" fill="#fff" stroke="#b4bdce" stroke-miterlimit="10" stroke-width="17.6206"/>
    <path d="M2892.1812,368.1338l12.7644,6.5592c.3248.1669.6735.2529,1.0258.2529h10.1353c1.2738,0,2.3677-1.1096,2.6055-2.6428l4.1697-27.3237c.3105-2.002-.7531-4.2431-2.5711-4.8492l-16.1667-4.8868c-.183,0-.3656.0232-.5447.0693l-12.6385,3.2497c-1.3153.3382-2.2162,1.8284-2.0969,3.4685l1.7015,23.3947c.0873,1.1999.7092,2.2422,1.6159,2.7081Z" fill="#c7d0e0" stroke-width="0"/>
  </g>
  <g id="odovzdávanie_kľúčov_grey" data-name="odovzdávanie kľúčov grey">
    <g>
      <path d="M3251.7639,319.612c-1.6717-3.8695.0279-8.366,3.8422-10.1601,12.4808-5.8702,37.7216-17.7805,38.314-18.3694.555-.5517,13.6036-15.3995,21.2667-24.1261,3.5743-4.0703,8.2872-6.9703,13.5312-8.3281l2.4073-.6233c2.9774-.7709,6.1299.278,8.0519,2.679h0c2.2761,2.8434,2.2837,6.8828.0183,9.7348l-30.4469,38.33c-.7766.9777-1.7764,1.7549-2.9155,2.2663l-41.1722,18.4851c-3.973,1.7838-8.6374-.028-10.3646-4.026l-2.5326-5.8622Z" fill="#c7d0e0" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
      <path d="M3280.9967,325.8452c-1.0903-3.9242,1.1296-8.0044,5.0172-9.2187,13.9388-4.3536,43.974-13.7725,44.712-14.3285.6662-.5019,16.3453-14.0592,25.9199-22.3452,4.7573-4.117,10.6388-6.7069,16.8875-7.4386l2.6304-.308c2.9513-.3456,5.8306,1.0738,7.3538,3.6251h0c1.8343,3.0724,1.2575,7.0107-1.3807,9.4281l-39.2633,35.976c-.7644.7004-1.6649,1.2357-2.6454,1.5725l-47.2674,16.2357c-4.1078,1.411-8.5554-.9299-9.7181-5.1147l-2.2459-8.0838Z" fill="#c7d0e0" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
      <path d="M3277.3695,330.6671c-.677-3.7818,1.7659-7.4172,5.5245-8.2132,15.8607-3.3591,54.2877-11.5372,55.2386-12.1226.9048-.557,28.2636-17.6244,41.637-25.9676,4.6296-2.8883,10.2037-3.8428,15.5312-2.6628l2.813.6231c1.7919.3969,3.3611,1.4709,4.3798,2.9976h0c2.0396,3.0569,1.4157,7.164-1.4397,9.4773l-50.252,40.712c-.8719.7064-1.8986,1.1965-2.9961,1.4304l-59.5758,12.6941c-3.9327.8379-7.7779-1.747-8.4864-5.7051l-2.3742-13.2632Z" fill="#c7d0e0" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
      <path d="M3105.43,356.1919l-23.723-53.6293c-1.5197-3.4355.008-7.4542,3.4261-9.0125l82.2968-37.5205c16.2737-7.4195,34.5034-9.404,51.9924-5.66l11.8813,2.5435c8.7283,1.8685,16.4405,6.9379,21.6172,14.2095l5.8616,8.2337c.5793.8138,1.3295,1.4912,2.198,1.9847l28.8708,16.4068c3.8566,2.1916,6.7881,5.7068,8.2514,9.8943l.5586,1.5986c.6815,1.9503.4474,4.1039-.6374,5.8621l-5.5338,8.9694c-.1122.1819.0052.4185.2179.4391l50.9947,4.9404c1.1345.1099,2.2784-.0647,3.3284-.5081l47.0146-19.8527c5.3233-2.2478,11.1932-2.8683,16.8688-1.7832l4.7473.9077c2.5199.4818,4.5601,2.3301,5.2875,4.7904l.957,3.2366c.9268,3.1344-.4878,6.4873-3.3797,8.0106l-68.0078,35.8229c-.5498.2896-1.1364.5031-1.7437.6347l-67.1394,14.5455c-6.5499,1.419-13.3703.9162-19.6413-1.4479l-89.2773-33.657c-1.6803-.6335-3.542-.5825-5.1851.142l-53.0654,23.3986c-3.462,1.5265-7.5062-.0398-9.0368-3.5Z" fill="#c7d0e0" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
      <line x1="3292.2467" y1="320.4666" x2="3227.2788" y2="295.4964" fill="#c7d0e0" stroke="#838c9d" stroke-linecap="round" stroke-miterlimit="10" stroke-width="3.7799"/>
      <path d="M3071.546,389.8091l-33.6735-91.5715,55.8306-13.702c2.5357-.6223,5.0202.4513,5.905,2.5519l28.7686,68.294c.9459,2.2455-.2287,4.9501-2.7116,6.2431l-54.1192,28.1845Z" fill="#838c9d" stroke-width="0"/>
    </g>
    <g>
      <path d="M3469.7986,190.3693c-28.9834-43.3535-69.8051-66.9154-91.1777-52.627-21.3726,14.2884-15.2029,61.0164,13.7806,104.3699,28.9834,43.3534,69.805,66.9153,91.1777,52.6269,21.3726-14.2884,15.2029-61.0164-13.7805-104.3698ZM3379.7351,161.7479c-2.9394-4.3967-1.7579-10.3437,2.6388-13.2831s10.3438-1.758,13.2831,2.6387c2.9394,4.3967,1.758,10.3438-2.6387,13.2832-4.3967,2.9394-10.3438,1.7579-13.2832-2.6388Z" fill="#f1f2f2" stroke-width="0"/>
      <g>
        <path d="M3434.0227,40.3005c3.7722-1.2708,7.8711.6802,9.2612,4.41,4.5488,12.2045,13.7845,36.8895,14.2925,37.4931.5741.6821,19.3099,20.1052,24.9189,25.918,1.0265,1.0638,1.7053,2.4072,1.9561,3.8641l1.3459,7.8173c.4928,2.8622-.7408,5.7471-3.1506,7.3682h0c-2.8538,1.9198-6.6559,1.6111-9.1628-.7437l-33.6923-31.6496c-.8594-.8073-1.5127-1.809-1.9049-2.9209l-14.1779-40.1919c-1.3681-3.8784.7015-8.1264,4.599-9.4394l5.7149-1.9252Z" fill="#b1bacb" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
        <path d="M3426.9887,39.2898c3.8238-.4141,7.2853,2.2802,7.8205,6.0889,1.9189,13.6559,6.089,43.0872,6.4939,43.8601.4531.8649,16.0634,26.3062,20.4662,33.4791.7472,1.2173,1.11,2.6292,1.0435,4.056l-.4389,9.4191c-.1306,2.8031-1.8948,5.2683-4.5058,6.2963h0c-3.1442,1.238-6.7255.0965-8.5733-2.7326l-27.5006-42.1031c-.5354-.8197-.896-1.7409-1.0595-2.7062l-7.8791-46.5338c-.6847-4.044,2.1786-7.8293,6.2563-8.2709l7.8769-.853Z" fill="#b1bacb" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
        <path d="M3426.1558,40.0194c3.6136-.3237,6.8311,2.2753,7.2688,5.8769,1.8469,15.1983-15.0752,43.3465-14.6033,44.2895.5132,1.0254,18.4511,36.338,23.3786,46.038.7969,1.5688.9446,3.3836.415,5.0616l-2.4127,7.6433c-.5217,1.6528-1.662,3.0401-3.1825,3.8719h0c-3.0444,1.6656-6.8564.7387-8.7961-2.1389l-34.1375-50.6425c-.5923-.8787-.9684-1.885-1.0975-2.9369l9.6502-46.4893c2.0566-8.5185,7.0619-9.0995,10.8438-9.4383l12.6733-1.1354Z" fill="#b1bacb" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
      </g>
      <path d="M3389.0563,155.018h0c1.3554,2.7636,4.0568,4.6176,7.1229,4.8886l5.3292.4711c6.752.5969,13.555-.1788,19.9994-2.2803l72.1084-23.5143c1.6227-.5292,3.3643-.5748,5.0125-.1313l3.4152.9189c7.6179,2.0497,15.6855,1.6715,23.0783-1.0819l35.3939-13.1824c1.7156-.639,3.5877-.7267,5.3556-.2511l25.7276,6.9224c.1861.0501.3775-.0602.4276-.2463l17.8889-66.4852c.0501-.1861-.0602-.3775-.2463-.4276l-75.4604-20.3039c-.381-.1025-.7684-.1793-1.1596-.2298l-107.5662-13.8819c-8.0053-1.0331-16.1249.6672-23.0431,4.8254l-46.2205,27.7809c-1.4491.871-2.6165,2.1412-3.3624,3.6585l-27.2631,55.4571c-2.1648,4.4036-.3081,9.7269,4.1258,11.8289l3.9359,1.8659c2.5509,1.2093,5.5272,1.1208,8.0018-.2379l6.256-3.4352c1.2328-.6769,2.2854-1.6396,3.0693-2.8073l28.2994-42.1524c1.3415-1.9982,3.4431-3.3594,5.8151-3.7665l28.1662-4.834c1.4475-.2484,2.9341-.1305,4.3242.3431l17.7188,6.0359c2.0355.6934,3.7464,2.1083,4.8095,3.9774l7.1453,12.5624c2.2844,4.0164,1.0719,9.1156-2.7758,11.6738l-52.4021,34.8403c-3.6763,2.4442-4.9713,7.2348-3.0273,11.1984Z" fill="#c7d0e0" stroke="#838c9d" stroke-miterlimit="10" stroke-width="3.7799"/>
      <g>
        <path d="M3459.3055,254.0795c.3335,1.4545.7172,2.6656,1.6391,3.726.2906.4346.4845.7247.1396,1.1645-.3446.4402-.8326.3474-1.2201-.2323-1.1158-1.3505-1.9373-2.8978-2.0731-4.6937-.0467-.3884-.1434-.533-.4845-.7247-1.0729-.3307-1.9986-.7597-2.9247-1.1892-.3408-.1912-.488-.0928-.5888.3936-.0076,1.2628.2792,2.3289.9573,3.3432,2.7587,4.4451,6.6018,7.3266,11.8739,8.2042,6.8837,1.0585,13.2439-.2589,19.081-3.9514.8831-.5904,1.9134-1.2792,2.8467-2.113-5.9653.6338-11.5317-.047-17.1951-.8729.4339.9677,1.3095,1.6401,1.9408,2.266,1.1196.7191,2.2895,1.1947,3.6069,1.5724.488.0928.8288.284.6778,1.0138-.1005.4868-.639.637-.9798.4458-.5849-.2378-1.1698-.4757-1.8053-.4705-1.7548-.7135-3.6066-1.572-4.4744-3.5073-.7245-1.4023-1.6506-1.8317-2.9212-1.821-.2439-.0462-.488-.0928-.585-.2378-.7321-.1395-1.5609-.4235-2.4402-.4645-.1007.4864.0929.776.1396,1.1645.6743,1.6457,1.7898,2.9958,3.2506,3.9065.4378.3362.7785.5274.5306,1.1123-.2476.5852-.8326.3474-1.1736.1557-2.239-1.4377-3.9865-3.4145-4.1649-6.2298.0502-.2434-.0467-.3884-.2906-.4346-1.0694-.9625-2.1423-1.2931-3.3627-1.5258ZM3488.366,256.4544c-1.0694-.9625-1.7977-1.7334-2.623-2.6492-1.7472-1.9763-3.2505-3.9065-5.048-5.6394-5.0054-4.62-13.7371-7.1685-21.2861-3.1696-1.8632,1.0358-3.5324,2.3616-4.5703,4.3132-.5926,1.025-.4956,1.17.6743,1.6457.2438.0462.5849.2378.8288.284.3411.1917.4883.0932.8329-.347,1.6304-2.9766,4.1326-4.6494,7.363-5.5513.5385-.1502,1.0265-.0574,1.1699.4756.1434.533-.2982.8282-.8364.9788-.7826.1035-1.6152.4509-2.3511.9429-1.716.9374-2.9942,2.211-3.9314,3.6762-.1974.3418-.1008.4864.2403.6781.9763.186,1.902.6151,2.8782.8011.2439.0462.3408.1912.5382-.1506,1.53-2.4898,3.9816-3.9196,6.6271-5.0593,1.3714-.4972,2.7425-.9947,4.1603-1.1039.3913-.0518.7321.1394.8755.6725.0464.388-.2982.8282-.7862.7354-2.7386.3633-5.237,1.4046-7.3481,3.0252-.8831.5904-1.6693,1.3258-2.2618,2.3508,1.4643.2789,2.7813.6561,4.3425,1.08.4883.0932.8793.041,1.2242-.3987l1.0303-.6888c2.9437-1.968,6.2206-2.4819,9.6875-2.0743.2442.0466.3408.1912.488.0928.3411.1917.2909.4351.2403.6781-.0502.2434-.1005.4868-.4918.5386-.1472.0984-.3914.0518-.6352.0056-2.5877-.3665-5.0823.0434-7.4839,1.2294-.5887.3936-1.1775.7872-1.8164,1.4242,6.0042,1.0171,12.0588,1.7911,18.2683,1.204ZM3491.984,256.1321c-.1008.4864-.151.7298-.4956,1.17-6.2939,6.5134-13.9323,9.1044-22.6754,8.4503-5.7638-.339-10.3895-3.117-13.9306-7.4581-1.0189-1.2055-1.7434-2.6078-2.0263-4.3053-.0932-.7764-.1863-1.5529-.0353-2.2827.1007-.4864.0038-.6314-.337-.8227-1.0227-.5741-2.0454-1.1481-2.9212-1.821-.4378-.3362-.6314-.6258-.3834-1.2106.3446-.4402.8326-.3474,1.2703-.0112.8758.6729,1.8985,1.247,2.8242,1.676,1.638-4.2395,5.1199-6.3581,9.2802-7.462,6.8522-1.8556,12.8528-.2067,18.4469,4.0197,2.1888,1.6812,4.0329,3.8025,5.7299,6.0223.2906.4346.8253.9159,1.1158,1.3505,1.0189,1.2055,2.3321,2.2142,3.9941,2.1517.0309.2585.0788.4363.1434.533Z" fill="#b4bdce" stroke-width="0"/>
        <path d="M3391.4861,176.0351c-.6459.4318-1.024.9146-1.0281,1.6073.051.4258.3187.4769.6927.6868,1.0709.204,2.0356.249,3.0554.0272.4292-.0569.6459-.4318.4334-.7497-.3146-1.1696-2.0231-2.3272-3.1533-1.5716ZM3412.2513,190.6707c.6459-.4318,1.1302-.7556.8667-1.4993-.1573-.5848-.799-.8457-1.3897-.6808-2.5752.3417-4.8316,1.1602-6.7181,2.8814-.1614.1079-.4844.3238-.4333.7496.2125.3179.4802.3689.748.4199.2677.051.5354.1021.9647.0451,2.2522-.1258,4.2409-.9953,5.9618-1.9158ZM3419.5413,235.4735l.8073-.5397c.3229-.2159.4334-.7496.3823-1.1754-.051-.4258-.4253-.6361-.9607-.7381-.2675-.0508-.429.0572-.6969.006-2.3075.3926-4.5635,1.2112-6.5565,2.7733-.323.2159-.9689.6478-.8113,1.2326.1572.5851.8539.5792,1.4447.4139,2.1458-.2845,4.2409-.995,6.391-1.9725ZM3411.9259,173.4095c2.4095.459,4.6066.6,7.0203.3663.8584-.1139,1.6105-.3868,2.4178-.9265.4844-.3239.7011-.6987.7053-1.3914-.1573-.5847-.5313-.7947-1.0668-.8967-.8032-.153-1.6064-.306-2.3585-.0331-1.0199.2219-2.2012.5516-3.1148.9324-1.1813.3298-2.4178.9265-3.6033,1.949ZM3402.4944,190.754c.45-3.5206,4.2189-6.2703,7.6994-5.6073,2.1418.408,3.5274,1.7816,4.1056,3.6948.1062.1589.2125.3179.5865.5278,1.4449.4139,2.4012,1.8445,2.3929,3.2299-.0041.6927-.2208,1.0675-.8115,1.2325-2.4731,1.1934-4.9461,2.3867-7.2066,3.898-.3229.2159-.8074.5397-1.024.9146-.3781.4828-.1656.8007.2084,1.0106.2677.051.5355.102.8032.153,1.6616.0391,3.3783-.1887,4.8315-1.1602,1.2366-.5967,2.2054-1.2444,3.5481-1.6821,1.5043-.5457,3.0002.2941,3.5273,1.7815.2636.7437-.1146,1.2265-.7604,1.6583-1.6657.6536-3.0637,1.3582-4.6232,2.1708-1.075.4887-2.0438,1.1364-2.9064,1.943-.3229.2159-.9688.6477-.65,1.1245s.748.4199,1.1772.363c1.823-.0689,3.5439-.9894,5.3159-1.484.5906-.1649.9136-.3808,1.5043-.5457,2.0439-1.1364,3.3783-.1887,4.9253,1.0769l.1063.159c.9053,1.0047.8501,1.2715-.3865,1.8683-2.1501.9775-4.3003,1.955-6.3441,3.0913-.6459.4318-1.2917.8636-1.6699,1.3464-.5396.5907-.3823,1.1755.4209,1.3285.5355.102,1.2324.096,1.9293.0901,1.2876-.1709,2.6304-.6086,3.8117-.9384.1615-.108.3229-.2159.3782-.4828.2208-1.0676,2.0522-2.5219,3.2845-2.4258.8032.153,1.4959.8398,1.7596,1.5835-.0552.2669-.1104.5338-.2719.6417-.3229.2159-.7521.2728-1.2365.5966.8583-.114,1.4491-.2787,2.3075-.3926,1.9846-.1767,2.8386.4018,3.3659,1.8895.527,1.4873-.1782,2.8786-1.7375,3.6914-2.3116,1.0855-4.3555,2.2217-6.4546,3.625l-1.7761,1.1874c-.378.483-.4334.7496.2084,1.0107.2678.0512.8033.1531.9647.0452,1.9843-.1771,3.7013-.4046,5.4774-1.592.8074-.5397,1.7762-1.1874,2.8511-1.6764.1614-.1079.4844-.3238.8583-.1139,1.3387.2551,2.3545.726,3.0984,1.8387.3186.4766.3696.9024-.1148,1.2262-2.5835,1.7272-5.3794,3.1365-8.069,4.7049l-1.1302.7556c-.3783.4825-.2719.6417.102.8516.5354.102,1.0709.2039,1.5002.1471,2.6815-.1827,5.2565-.5245,7.4107-2.1949.6459-.4318,1.2917-.8636,2.2014-.5513,1.0155.4705,2.1376,1.1007,2.6137,2.1621.3189.477.102.8516-.4885,1.0167-1.3981.7044-2.6345,1.3013-3.9262,2.1649.5905-.1651,1.1813-.3298,1.7718-.495,1.3427-.4378,2.7919-.7164,4.1306-.4613,1.6613.0388,2.622.7768,2.8815,2.2133.4209,1.3286-.2293,2.4528-1.6271,3.1577-2.2054,1.2442-4.4106,2.4888-6.7775,3.8409-.8074.5397-1.8826,1.0283-2.745,1.8351-.3229.2159-.5394.5909-.3269.9087s.48.3686.7479.4198c.5354.1019,1.1262-.0627,1.6616.0392,2.4687-.501,4.9377-1.0015,7.0878-1.979.3229-.2159.6458-.4318.9647.0452s-.1104.5337-.5948.8576c-2.473,1.1934-4.8356,1.8531-7.517,2.0358-.6969.006-1.1262.0628-1.9292-.09-1.3387-.2551-2.618-1.4697-2.8774-2.9062-.102-.8516.0635-1.6524.7647-2.3508.5395-.5909,1.3468-1.1306,1.9927-1.5624,2.5835-1.7272,5.1119-3.1872,7.9078-4.5965.3229-.2159.752-.2731.7009-.6989-.051-.4258-.6418-.2611-.9647-.0452-2.7365.4498-5.3669,1.0582-8.0525,1.9338-.5905.1651-1.1813.3298-1.8782.3358-.2679-.0512-.4293.0568-.4293.0568-5.0565,2.9206-10.164,5.4153-15.1143,8.4951-.8072.5397-1.7208.9202-2.4217,1.619-.1617.1081-.4847.324-.4335.7497.2125.3178.3738.21.6416.2606,2.4139-.2335,4.8275-.4674,7.088-1.9785,1.1302-.7556,2.2605-1.5112,3.4418-1.8411.3229-.2159.5908-.1647.8584-.1139,1.1772.363,2.0315.942,2.9919,1.6796.6929.6869.6375.9535-.2759,1.3345-1.2367.5965-2.473,1.1934-3.7095,1.7898-1.5593.8128-3.2806,1.7333-4.5213,3.0226-.323.2159-.3779.4829-.7008.6988-.2169.3746-.7011.6984-.3823,1.1753.1569.5847.8539.5792,1.283.522,2.0907-.018,3.9183-.7793,5.7453-1.5408,1.9888-.8697,3.7096-1.7904,5.4857-2.9779l1.1302-.7556c.3229-.2159.6969-.006,1.0158.471.2125.3178-.1104.5337-.2719.6417-3.0679,2.051-6.3482,3.7841-9.892,4.7736-1.1261.0627-2.4139.2335-3.4849.0296-2.9999-.294-4.3263-2.6271-2.8098-5.251.2722-.6419.7564-.9656,1.1343-1.4485-.2125-.3178-.535-.1022-.6967.0059-1.0197.2218-1.8782.3358-2.843.2907-1.3935.0118-2.2478-.5671-2.9365-1.9463-.527-1.4873-.468-2.4472.8239-3.3108l3.5523-2.3749-.1061-.1587c-.5911.1649-1.2878.1708-1.8782.3358-.8584.114-1.8233.0689-2.4651-.1922-1.7124-.4652-2.4011-1.8444-1.9635-3.287.2759-1.3345,1.1936-2.4079,2.3238-3.1635,1.2919-.8637,2.6899-1.5681,4.2452-1.688,2.3075-.3926,4.5512.8668,5.665,2.8823.2125.3178.3699.9028.4209,1.3286.0339.2837.1587.3538.374.2099,2.3669-1.3521,4.7335-2.7046,7.1005-4.0568-.374-.2099-.5354-.1019-.8033-.1531-1.3938.012-2.6261-.0839-3.6929-.9807-2.0272-1.6344-1.9637-3.2868.0843-5.1159-.6969.006-1.3941.0116-2.091.0176-1.4999-.1467-2.622-.7768-3.1491-2.2641-.4763-1.0619-.3105-1.8623.2843-2.7199.378-.483.8624-.8068,1.3468-1.1307,2.2605-1.5112,4.6274-2.8633,6.9941-4.2159.4844-.3238,1.0752-.4885,1.6147-1.0795-.374-.2099-.6969.006-1.1259.0632-1.0198.2219-2.0399.4434-3.0044.3986-.5908.1647-1.2877.1707-1.8741-.3571-.2125-.3179-.5355-.102-.8584.1139-1.5043.5457-2.847.9834-4.4024,1.1033-1.3938.0119-2.6262-.0841-3.37-1.1968-1.5428-1.9584-1.2074-4.2524.7854-5.8147.1614-.1079.4844-.3238.5396-.5907-.1615.108-.4292.0569-.7521.2728-.9136.3808-1.772.4947-2.7366.4496-1.3938.0119-2.3033-.3-3.1534-1.5716-.6886-1.3795-.574-2.606.2375-3.8385.1615-.108.2167-.3748.5396-.5907-2.5752.3417-4.3941-.2822-5.1296-2.7803-.523-2.1801.8792-3.5775,2.6553-4.7649l-.1063-.159c-3.9179.7794-6.5869-1.116-8.661-3.8691-1.5429-1.9583-2.1721-4.2974-2.6399-6.7445-.2636-.7437-.3699-.9027-1.0668-.8967-2.146.2848-3.8586-.1802-5.3504-1.7127-.7991-.8457-1.1689-1.7484-1.2158-2.8669.0083-1.3855.6583-2.51,1.7334-2.9987.9136-.3808,2.2522-.1258,3.1575.8789.5865.5278,1.0116,1.1636,1.5429,1.9583.3188.4769.6928.6868,1.2834.5219.2719-.6417.7011-.6986,1.1855-1.0225,2.5282-1.4603,4.9503-3.0795,7.1597-5.0166-.8542-.5788-1.8699-1.0497-2.5627-1.7365-.6927-.6868-1.3855-1.3736-1.7002-2.5431.5355.102.748.4199.9605.7378,1.0115,1.1636,2.346,2.1113,3.8972,2.6842.374.2099.8032.153,1.1261-.0629,1.7761-1.1874,3.9262-2.1649,6.2889-2.8245,1.9334-.6026,3.9179-.7794,5.8983-.2635,1.6064.306,2.9408,1.2537,3.5742,2.9001.5824,1.2206.2001,2.3961-.9302,3.1517-.9688.6477-1.8824,1.0285-3.0085,1.0914-2.5751.3417-5.0399.1496-7.288-.4173-.9095-.3119-1.6616-.0391-2.3075.3927l-6.2972,4.2099c-1.4532.9715-2.8512,1.6762-4.6232,2.1708-.5906.1649-.8073.5397-.65,1.1245.6803,2.7649,1.5221,5.4219,3.6004,7.4823.2125.3179.4803.3689.6928.6868.0708.106.1955.1759.374.21Z" fill="#6b7484" stroke-width="0"/>
        <path d="M3432.1614,258.5444l.323-.2159c.7009-.6989,1.6146-1.0794,1.9967-2.2553.2168-.3746.1658-.8004-.2081-1.0103-.374-.2099-.6418-.2611-.9648-.0452-1.0752.4885-1.9971,2.2549-1.8439,3.5322.0511.4264.2128.3183.3741.2105l.323-.2159ZM3432.7258,253.7971c-.2125-.3178-.5354-.1019-.8033-.1531-2.4685.5009-4.674,1.7451-6.3482,3.7841-.9177,1.0734-.4417,2.1355,1.0035,2.5491,1.3386.2552,2.52-.0748,3.701-.4044.323-.2159.2166-.3751.2722-.6419-.0983-1.5442.3393-2.9868,1.4185-4.1683.2168-.3746.5397-.5905.7563-.9655ZM3432.0465,259.7707c.8543.5789,1.764.8906,2.6735,1.2029.2675.0508.3229-.2159.4844-.3238l1.7761-1.1874c.9135-.381,1.5553-.12,2.1417.4078.799.8456.5824,1.2206-.3864,1.8683l-.6459.4318c1.0198-.2219,1.8782-.3358,2.7365-.4498,1.3938-.012,2.142.4082,2.7241,1.6284.4209,1.3286.1449,2.6631-.8239,3.3108l-1.6147,1.0795c-2.205,1.2445-4.3557,2.2218-6.4548,3.6251-.3225.2156-.6455.4316-.8621.8066-.3786.4827-.4335.7497.3697.9029.6416.2606,1.2326.0957,1.9294.0898,1.8782-.3358,3.8115-.9382,5.7452-1.5408l.1615-.108c.1614-.1079.5905-.1651.6415.2607.051.4258-.1104.5337-.4334.7496-1.9333.6029-3.7602,1.3644-5.9065,1.6492-.8584.114-1.7169.228-2.52.0748-1.6064-.3058-2.5115-1.3106-2.9324-2.6392-.3701-.9026.0634-1.6523.6029-2.2433.3781-.4824,1.0241-.9143,1.6701-1.3461,2.4221-1.6193,4.7886-2.9717,7.317-4.4318l.9688-.6477c-.3739-.2099-.8033-.1531-1.1262.0628-1.0198.2219-2.1457.285-3.1658.5065-1.0198.2219-1.6061-.306-1.9762-1.2086-.051-.4258-.3186-.4766-.6924-.6866-.8036-.1529-1.5515-.5727-2.4059-1.1516-.3738-.21-.6414-.2613-.9644-.0454-.9138.3812-1.8274.7618-2.9532.8249-1.7172.2276-3.1063-.4531-4.0626-1.8836-.9563-1.4305-1.3223-3.0261-.5618-4.6846,1.4105-2.7825,6.0888-5.2201,9.3014-4.6084,1.8741.357,2.8858,1.5209,3.6807,3.0593.6334,1.6464-.0168,2.7706-.9897,4.1112-1.3471,1.1302-2.3159,1.7779-3.4463,2.5337Z" fill="#6b7484" stroke-width="0"/>
        <path d="M3439.5756,220.01c-1.4532.9715-3.4888.7224-4.1773-.6569-.2128-.3183-.3189-.477-.1023-.852.1658-.8004,1.6741-2.039,2.5324-2.1529.8584-.1139,2.0866.6749,2.4565,1.5777.2084,1.0107.098,1.5445-.7093,2.0842Z" fill="#6b7484" stroke-width="0"/>
      </g>
      <g>
        <g>
          <path d="M3245.9139,241.4783l64.9443-71.4805-11.6465-10.5811c-4.3496-3.9531-4.6729-10.708-.7207-15.0576l26.4141-29.0723c2.0098-2.2119,4.8877-3.4795,7.8965-3.4795,2.6602,0,5.2031.9795,7.1611,2.7578l45.3213,41.1777c2.0996,1.9072,3.3311,4.5283,3.4678,7.3799.1357,2.8516-.8398,5.5781-2.7471,7.6768l-26.4141,29.0732c-2.0098,2.2119-4.8867,3.4795-7.8955,3.4795h-.001c-2.6602,0-5.2031-.9795-7.1611-2.7578l-11.6455-10.582-56.5859,62.2803-17.5596.8418-12.8281-11.6562ZM3332.5399,140.6092c-2.7314,0-4.9258.8252-6.3438,2.3867-2.04,2.2451-2.3906,5.8516-.9873,10.1543,1.4795,4.5352,4.7031,9.2207,9.0762,13.1943,5.6553,5.1377,12.2607,8.2051,17.6709,8.2051,2.7324,0,4.9258-.8252,6.3438-2.3857,2.04-2.2451,2.3906-5.8516.9873-10.1543-1.4795-4.5352-4.7031-9.2207-9.0762-13.1943-5.6543-5.1387-12.2607-8.2061-17.6709-8.2061Z" fill="#58595b" stroke-width="0"/>
          <path d="M3332.8011,113.0047c2.2727,0,4.5515.8081,6.3557,2.4473l45.3213,41.1775c3.8516,3.4993,4.1396,9.5134.6404,13.3647l-26.4146,29.073c-1.8601,2.0471-4.4304,3.0874-7.009,3.0874-2.2727,0-4.5515-.8081-6.3557-2.4473l-12.5332-11.387-57.0574,62.7996-16.5681.7937-11.5745-10.5164,64.9446-71.4802-12.533-11.3872c-3.8513-3.4993-4.1394-9.5134-.6404-13.3647l26.4148-29.0728c1.8601-2.0474,4.4304-3.0876,7.009-3.0876M3351.9567,175.7479c2.9812,0,5.5212-.8975,7.23-2.7783,4.8428-5.3301,1.1848-16.5413-8.1699-25.041-6.0542-5.5005-13.0095-8.5173-18.4775-8.5168-2.9812.0002-5.5212.8975-7.23,2.7781-4.8428,5.3301-1.1848,16.5415,8.1702,25.041,6.0542,5.5005,13.0093,8.5171,18.4773,8.5171M3332.8011,110.6089c-3.3452,0-6.5464,1.4114-8.7822,3.8723l-26.4148,29.0728c-4.3962,4.8389-4.0361,12.3525.8025,16.749l10.7598,9.7761-63.3335,69.707-1.6111,1.7732,1.7732,1.6111,11.5745,10.5164.7346.6672.9912-.0474,16.5681-.7937.991-.0476.6675-.7344,55.4463-61.0264,10.76,9.7759c2.179,1.9797,5.0083,3.0698,7.9668,3.0698,3.3452,0,6.5461-1.4114,8.7822-3.8721l26.4146-29.073c2.1228-2.3364,3.208-5.3694,3.0562-8.54-.1519-3.1709-1.5222-6.0862-3.8586-8.209l-45.3213-41.1775c-2.179-1.9797-5.0083-3.0698-7.9668-3.0698h0ZM3351.9567,173.3521c-5.0439,0-11.5066-3.0249-16.8662-7.8945-4.2217-3.8354-7.3267-8.3384-8.7427-12.679-1.2646-3.8762-1.0039-7.0645.7346-8.9778,1.4966-1.6475,3.7937-1.9934,5.457-1.9937,5.0444,0,11.5068,3.0251,16.8662,7.8945,8.2205,7.469,11.8877,17.3867,8.0078,21.6567-1.4968,1.6477-3.7937,1.9937-5.4568,1.9937h0Z" fill="#58595b" stroke-width="0"/>
        </g>
        <g>
          <path d="M3249.4051,243.5438l64.9443-71.4805-13.4189-12.1924c-3.3721-3.0645-3.623-8.3008-.5596-11.6729l26.4141-29.0723c1.5566-1.7129,3.7881-2.6953,6.123-2.6953,2.0625,0,4.0342.7578,5.5498,2.1357l45.3213,41.1777c3.3721,3.0635,3.623,8.2998.5596,11.6719l-26.415,29.0732c-1.5557,1.7129-3.7871,2.6953-6.1211,2.6953h-.001c-2.0625,0-4.0342-.7588-5.5498-2.1367l-13.4199-12.1924-57.5293,63.3193-15.5771.7461-10.3203-9.377ZM3332.6454,140.4412c-3.4248,0-6.2314,1.0957-8.1162,3.1699-5.3682,5.9102-1.7441,17.6523,8.251,26.7334,6.085,5.5283,13.2939,8.8281,19.2832,8.8281,3.4258,0,6.2324-1.0957,8.1172-3.1699,2.6436-2.9102,3.1738-7.3525,1.4912-12.5088-1.6074-4.9238-5.0674-9.9756-9.7432-14.2246-6.084-5.5283-13.293-8.8281-19.2832-8.8281Z" fill="#d1d3d4" stroke-width="0"/>
          <path d="M3332.9078,117.6277c1.7644,0,3.4495.6482,4.7446,1.825l45.3215,41.1775c2.8835,2.6199,3.0979,7.0972.478,9.9805l-26.4146,29.0728c-1.3296,1.4636-3.2383,2.303-5.2361,2.303-1.7644,0-3.4492-.6479-4.7444-1.8247l-12.533-11.3872-1.7732-1.6111-1.6111,1.7732-56.3901,62.0652-14.5859.6987-9.0667-8.2378,63.3337-69.7073,1.6111-1.7732-1.7732-1.6111-12.5332-11.387c-2.8835-2.6199-3.0979-7.0972-.478-9.9805l26.4146-29.073c1.3296-1.4636,3.238-2.303,5.2358-2.303M3352.0634,180.3709c3.7722,0,6.8855-1.2319,9.0032-3.5627,2.9458-3.2424,3.5652-8.103,1.7437-13.686-1.6702-5.1189-5.2483-10.3533-10.0757-14.7393-6.2988-5.7229-13.8079-9.1396-20.0884-9.1396-3.7725.0005-6.8857,1.2324-9.0032,3.563-2.946,3.2424-3.5652,8.1028-1.7437,13.686,1.6702,5.1187,5.2483,10.353,10.0757,14.7393,6.2988,5.7229,13.8086,9.1394,20.0884,9.1394M3332.9078,115.2322c-2.5786,0-5.1489,1.0403-7.009,3.0874l-26.4146,29.073c-3.4993,3.8513-3.2112,9.8655.6401,13.3647l12.5332,11.387-64.9448,71.4805,11.5745,10.5161,16.5681-.7937,57.0576-62.7996,12.533,11.3872c1.8042,1.6392,4.083,2.4473,6.3555,2.4473,2.5786,0,5.1492-1.0403,7.0093-3.0876l26.4146-29.0728c3.4993-3.8513,3.2112-9.8655-.6401-13.3647l-45.3215-41.1775c-1.8042-1.6392-4.0833-2.4473-6.3557-2.4473h0ZM3352.0634,177.9752c-5.468,0-12.4231-3.0164-18.4773-8.5168-9.355-8.4998-13.0129-19.7109-8.1699-25.041,1.7087-1.8809,4.2488-2.7781,7.2302-2.7783,5.4678-.0005,12.4229,3.0164,18.4771,8.5171,9.355,8.4995,13.0127,19.7109,8.1699,25.041-1.7087,1.8806-4.2488,2.7781-7.23,2.7781h0Z" fill="#939598" stroke-width="0"/>
        </g>
        <polygon points="3262.6558 253.9796 3259.3016 254.1541 3318.798 188.6702 3323.8023 193.217 3268.8656 253.6822 3265.5244 253.8422 3320.4611 193.377 3318.9581 192.0114 3262.6558 253.9796" fill="#58595b" stroke-width="0"/>
      </g>
      <g>
        <path d="M3369.2187,120.4223c-7.275-1.9575-14.5128-1.0175-19.9654,2.0403l3.0766,2.8169c4.4258-2.0759,10.1189-2.5964,15.8645-1.0504,6.7351,1.8122,12.1106,6.3409,14.5911,11.2991,0,0,1.4119-.5354,1.9048-.6898.4652-.1458,1.8542-.5108,1.8542-.5108-2.8954-6.1486-9.2829-11.7412-17.3258-13.9052Z" fill="#8b94a4" stroke-width="0"/>
        <path d="M3381.2289,149.3618c-4.3136,5.1653-12.6983,7.6811-21.2448,5.3815-4.7772-1.2854-8.8898-3.7145-11.6307-6.9039,0,0-3.9866-2.9769-7.148-4.2037,2.5665,6.7484,9.1089,12.5881,17.7544,14.9143,12.3439,3.3213,24.5687-2.1272,28.1569-11.6842,0,0-1.5042-.0023-3.4137.8547-1.286.5772-2.4742,1.6412-2.4742,1.6412Z" fill="#8b94a4" stroke-width="0"/>
      </g>
      <path d="M3658.8941,173.3129l-69.3297-38.7229c-2.2366-1.2492-3.3378-3.8589-2.6722-6.3327l18.5225-68.8397c.8021-2.9812,3.8692-4.7477,6.8504-3.9456l73.0476,19.6547-26.4186,98.1862Z" fill="#657492" stroke-width="0"/>
    </g>
  </g>
  <g id="text">
    <text transform="translate(589.2689 488.9373)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="36" font-weight="600"><tspan x="0" y="0">Z</tspan><tspan x="22.1484" y="0" letter-spacing="-.0356em">A</tspan><tspan x="45.5625" y="0">ČI</tspan><tspan x="79.541" y="0" letter-spacing="-.0879em">A</tspan><tspan x="101.0742" y="0" letter-spacing="-.0312em">T</tspan><tspan x="122.3438" y="0">OK VÝS</tspan><tspan x="250.2422" y="0" letter-spacing="-.0879em">T</tspan><tspan x="269.4727" y="0" letter-spacing="-.084em">A</tspan><tspan x="291.1465" y="0">VBY</tspan></text>
    <text transform="translate(650.1605 563.4986)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="60.9368" font-weight="600"><tspan x="0" y="0">2Q 20</tspan><tspan x="167.457" y="0" letter-spacing="-.0156em">2</tspan><tspan x="202.1211" y="0" letter-spacing="0em">4</tspan></text>
    <text transform="translate(100.9423 563.4986)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="60.9368" font-weight="600"><tspan x="0" y="0">1Q 20</tspan><tspan x="155.2876" y="0" letter-spacing="-.0156em">2</tspan><tspan x="189.9517" y="0" letter-spacing="0em">4</tspan></text>
    <text transform="translate(1519.537 563.4986)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="60.9368" font-weight="600"><tspan x="0" y="0">1Q 2025</tspan></text>
    <text transform="translate(2093.6097 563.4986)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="60.9368" font-weight="600"><tspan x="0" y="0">2Q 2026</tspan></text>
    <text transform="translate(2652.2571 563.4986)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="60.9368" font-weight="600"><tspan x="0" y="0">3Q 2026</tspan></text>
    <text transform="translate(3269.4476 563.4986)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="60.9368" font-weight="600"><tspan x="0" y="0">3Q 2026</tspan></text>
    <text transform="translate(54.98 488.9373)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="36" font-weight="600"><tspan x="0" y="0">PRÍPR</tspan><tspan x="96.3281" y="0" letter-spacing="-.084em">A</tspan><tspan x="118.002" y="0">VNÉ PR</tspan><tspan x="240.0469" y="0" letter-spacing="-.0356em">Á</tspan><tspan x="263.4609" y="0">CE</tspan></text>
    <text transform="translate(1082.8282 489.4051)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="36" font-weight="600"><tspan x="0" y="0">VÝS</tspan><tspan x="70.7344" y="0" letter-spacing="-.0879em">T</tspan><tspan x="89.9648" y="0" letter-spacing="-.084em">A</tspan><tspan x="111.6387" y="0">VBA</tspan></text>
    <text transform="translate(1378.1221 489.4051)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="36" font-weight="600"><tspan x="0" y="0">DO</tspan><tspan x="51.2578" y="0" letter-spacing="-.0386em">K</tspan><tspan x="72.8965" y="0">ONČENIE HRUBEJ S</tspan><tspan x="396.5449" y="0" letter-spacing="-.0879em">T</tspan><tspan x="415.7754" y="0" letter-spacing="-.084em">A</tspan><tspan x="437.4492" y="0">VBY</tspan></text>
    <text transform="translate(2019.3639 488.9373)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="36" font-weight="600"><tspan x="0" y="0">DO</tspan><tspan x="51.2578" y="0" letter-spacing="-.0386em">K</tspan><tspan x="72.8965" y="0">ONČENIE S</tspan><tspan x="254.7422" y="0" letter-spacing="-.0879em">T</tspan><tspan x="273.9727" y="0" letter-spacing="-.084em">A</tspan><tspan x="295.6465" y="0">VBY</tspan></text>
    <text transform="translate(2666.8986 488.9373)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="36" font-weight="600"><tspan x="0" y="0" letter-spacing="-.0386em">K</tspan><tspan x="21.6387" y="0">O</tspan><tspan x="48.1816" y="0" letter-spacing=".0273em">L</tspan><tspan x="68.1855" y="0">AU</tspan><tspan x="117.9316" y="0" letter-spacing="-.0356em">DÁ</tspan><tspan x="164.7773" y="0">CIA</tspan></text>
    <text transform="translate(3279.2044 488.9373)" fill="#657492" font-family="InterTight-SemiBold, &apos;Inter Tight&apos;" font-size="36" font-weight="600"><tspan x="0" y="0">OD</tspan><tspan x="51.2578" y="0" letter-spacing="-.0386em">O</tspan><tspan x="76.4121" y="0">VZ</tspan><tspan x="123.2578" y="0" letter-spacing="-.0356em">D</tspan><tspan x="146.6895" y="0">ANIE</tspan></text>
  </g>
</svg>