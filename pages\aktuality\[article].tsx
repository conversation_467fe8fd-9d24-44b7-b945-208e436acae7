import Image from "next/legacy/image";
import {Avatar} from "flowbite-react";
import executeQuery from "../../utils/dbConnection";
import Head from "next/head";

export const getServerSideProps = (async (context: any) => {
    let articleData = await executeQuery({
        query: 'SELECT * FROM articles WHERE articleSlug = ?',
        values: [context.params.article]
    })
    articleData = JSON.stringify(articleData[0])
    return {props: {articleData}}
})


export default function Aktuality({articleData}) {
    articleData = JSON.parse(articleData)
    const myLoader = (image: any) => {
        return `https://admin.laurindvor.com/files/${image}?w=400&q=75`
    }

    return <>
        <Head>
            <title>{articleData["metaTitle"]}</title>
            <meta name="description" content={articleData["metaDescription"]}/>
        </Head>
        <div className={"bg-[#E2E2E7] overflow-x-hidden lg:mt-20"}>
            <div className={"w-screen flex items-center overflow-y-hidden relative justify-center"}>
                <div className={`absolute blur-md h-screen w-screen`}
                     style={{backgroundImage: "url(" + myLoader(articleData["articleImage"].split('/').pop()) + ")"}}>
                </div>
                <Image src={`https://admin.laurindvor.com/files/${articleData["articleImage"].split('/').pop()}`}
                       width={800} quality={20} height={500} alt={articleData["metaTitle"]}/>
            </div>
            <div className={"px-0 md:px-[10vw] xl:px-[20vw] md:mx-10"}>
                <div
                    className={"flex py-4 shadow-md rounded-b-xl w-full items-center justify-between px-[2vw]"}>
                    <div className={"author flex gap-2 items-center"}>
                        <Avatar size={"sm"} rounded/>
                        <div className={"flex flex-col"}>
                            <p className={"text-[3vw] md:text-[2vw] lg:text-[1vw]"}>
                                <strong>{articleData["articleAuthor"]}</strong></p>
                            <small className={"text-[2vw] md:text-[1vw] lg:text-[.8vw] text-gray-500"}>Autor
                                článku</small>
                        </div>
                    </div>
                    <div className={"flex flex-col"}>
                        <p className={"text-[3vw] md:text-[2vw] lg:text-[1vw]"}>{articleData["dateCreate"]}</p>
                        <small className={"text-[2vw] md:text-[1vw] lg:text-[.8vw] text-gray-500"}>Dátum
                            uverejnenia</small>
                    </div>
                </div>
                <h1 className={"text-[7vw] md:text-[4vw] xl:text-[3vw] mt-3 lg:mt-6 px-2 md:px-10 text-center"}>{articleData["articleHeading"]}</h1>
                <div className={"lg:px-20 px-10 py-8 lg:py-14"}
                     dangerouslySetInnerHTML={{__html: articleData["articleText"]}}></div>
            </div>

        </div>
    </>
}
