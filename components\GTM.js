// (components)/GTM.js
import { useEffect } from 'react';
import { useRouter } from 'next/router';
import TagManager from 'react-gtm-module';

const GTM = ({ gtmId }) => {
    const router = useRouter();

    useEffect(() => {
        TagManager.initialize({ gtmId });
        router.events.on('routeChangeComplete', handleRouteChange);

        return () => {
            router.events.off('routeChangeComplete', handleRouteChange);
        };
    }, []);

    const handleRouteChange = (url) => {
        TagManager.dataLayer({
            dataLayer: {
                event: 'pageView',
                pagePath: url,
            },
        });
    };

    return null;
};

export default GTM;
