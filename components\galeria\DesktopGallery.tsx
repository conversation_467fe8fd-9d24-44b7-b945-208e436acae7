import {<PERSON>ouse<PERSON>, <PERSON><PERSON><PERSON>} from "flowbite-react";
import {useState} from "react";
import Image from "next/image";
import Link from "next/link";


export default function DesktopGallery({isMobile, vystavbaFotky}) {
    const [gallery, setGallery] = useState<any[]>(["exterier", 7]);
    const [isHidden, setIsHidden] = useState(false)
    const [isInterior, setIsInterior] = useState(false)
    const [isBuilding, setIsBuilding] = useState(false)
    const [liveViewEnabled, enableLiveView] = useState(false)
    return <>
        <div className={"bg-[#E2E2E7] h-screen"}>
            <div className="mt-16 relative flex justify-center w-full h-screen">
                <div
                    className={isHidden ? "absolute top-11 md:top-16 shadow-2xl flex items-center gap-4 text-white lg:left-8 z-30 px-2 py-2 rounded-xl glassBackdrop" : "absolute top-11 md:top-16 shadow-2xl flex items-center gap-4 text-white lg:w-auto lg:left-8 z-30 px-4 py-2 rounded-xl justify-around glassBackdrop"}>
                    {!isMobile && <div className={"w-full h-full relative"}>
                        <Tooltip placement={"right"}
                                 content={isHidden ? "Zobraziť panel" : "Skryť panel"}>
                            <button onClick={() => setIsHidden(!isHidden)}
                                    className={"bg-black p-2 hover:bg-gray-700 transition-all hover:shadow-xl cursor-pointer rounded-full text-white"}>
                                {isHidden ?
                                    <svg className="w-[1.5vw] h-[1.5vw] text-white"
                                         aria-hidden="true"
                                         xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                         fill="none"
                                         viewBox="0 0 24 24">
                                        <path stroke="currentColor" strokeWidth="2"
                                              d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                                        <path stroke="currentColor" strokeWidth="2"
                                              d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                    </svg>
                                    : <svg className="w-[1.5vw] h-[1.5vw] text-white"
                                           aria-hidden="true"
                                           xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                           fill="none"
                                           viewBox="0 0 24 24">
                                        <path stroke="currentColor" strokeLinecap="round"
                                              strokeLinejoin="round"
                                              strokeWidth="2"
                                              d="M3.933 13.909A4.357 4.357 0 0 1 3 12c0-1 4-6 9-6m7.6 3.8A5.068 5.068 0 0 1 21 12c0 1-3 6-9 6-.314 0-.62-.014-.918-.04M5 19 19 5m-4 7a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                    </svg>}
                            </button>
                        </Tooltip>
                    </div>}
                    {!isHidden && <>
                        <p className={gallery[0] === "exterier" ? "bg-black opacity-100 transition-all w-full text-center font-bold hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                           onClick={() => {
                               setGallery(["exterier", 7])
                               setIsInterior(false)
                               setIsBuilding(false)
                               enableLiveView(false)
                           }}>Exteriér
                        </p>
                        <p className={gallery[0] === "interier" || gallery[0] === 1 || gallery[0] === 2 || gallery[0] === 3 || gallery[0] === 4 || gallery[0] === 5 ? "bg-black opacity-100 transition-all w-full text-center font-bold hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                           onClick={() => {
                               setGallery([1, 2])
                               setIsInterior(true)
                               setIsBuilding(false)
                               enableLiveView(false)
                           }}>Interiér</p>
                        <p className={gallery[0] === "standard" ? "bg-black opacity-100 transition-all w-full text-center font-bold hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                           onClick={() => {
                               setGallery(["standard", 1])
                               setIsInterior(false)
                               setIsBuilding(false)
                               enableLiveView(false)
                           }}>Sanita</p>
                        <p onClick={() => {
                            setGallery(["vystavba", vystavbaFotky.length])
                            setIsInterior(false)
                            setIsBuilding(true)
                        }}
                           className={gallery[0] === "vystavba" ? "bg-black opacity-100 w-full text-center font-bold transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}>Výstavba</p>
                    </>
                    }
                    {!isHidden && isInterior ?
                        <div
                            className={"absolute top-16 shadow-2xl flex items-center justify-center w-full lg:w-auto gap-4 text-white left-1 z-30 px-4 py-2 rounded-full bottomNavigation"}>
                            <p className={gallery[0] === 1 ? "bg-black opacity-100 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                               onClick={() => setGallery([1, 2])}>1i
                            </p>
                            <p className={gallery[0] === 2 ? "bg-black opacity-100 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                               onClick={() => setGallery([2, 8])}>2i
                            </p>
                            <p className={gallery[0] === 3 ? "bg-black opacity-100 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                               onClick={() => setGallery([3, 6])}>3i
                            </p>
                            <p className={gallery[0] === 4 ? "bg-black opacity-100 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                               onClick={() => setGallery([4, 8])}>4i
                            </p>
                            <p className={gallery[0] === 5 ? "bg-black opacity-100 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4" : "bg-gray-800 opacity-50 transition-all hover:opacity-80 cursor-pointer p-2 rounded-xl px-4"}
                               onClick={() => setGallery([5, 7])}>5i
                            </p>
                        </div> :
                        !isHidden && isBuilding &&
                        <div
                            className={"absolute top-16 shadow-2xl flex items-center justify-center w-full lg:w-auto gap-4 text-white left-1 z-30 px-4 py-2 rounded-full bottomNavigation"}>
                        </div>
                    }
                </div>
                {liveViewEnabled ?
                    <></>
                    :
                    isMobile ? <Carousel className={"w-full overflow-x-hidden"} slide={false}>
                            {gallery[0] === "vystavba" ?
                                vystavbaFotky.map((e, i) => {
                                    return (
                                        <Link key={i}
                                              href={`https://laurindvor.com/foto_stavba/${vystavbaFotky[vystavbaFotky.length - 1 - i].name}`}>
                                            <img loading={"lazy"}
                                                 src={`https://laurindvor.com/foto_stavba/${vystavbaFotky[vystavbaFotky.length - 1 - i].name}`}
                                                 alt={"galeria"}/>
                                        </Link>
                                    );
                                })
                                :
                                [...Array(gallery[1])].map((e, i) => {
                                    return (
                                        <div key={i} className={"overflow-x-hidden w-full h-full relative"} style={{
                                            backgroundImage: `url('/galeria/${gallery[0]}/${i}.webp')`,
                                            backgroundSize: "cover",
                                            backgroundPosition: "center center"
                                        }}>
                                        </div>
                                    );
                                })
                            }
                        </Carousel> :
                        <Carousel className={"w-full overflow-x-hidden"}
                                  slide={gallery[0] === "vystavba"}>
                            {gallery[0] === "vystavba" ?
                                vystavbaFotky.slice(0).reverse().map((e, i) => {
                                    return <img key={i} loading={"lazy"}
                                                src={`https://laurindvor.com/foto_stavba/${vystavbaFotky[vystavbaFotky.length - 1 - i].name}`}
                                                alt={"galeria"}/>
                                })
                                :
                                [...Array(gallery[1])].map((e, i) =>
                                    <div key={i} className={gallery[0] === "standard" && "w-full h-full relative"}>
                                        {gallery[0] === "standard" ?
                                            <Image key={i} src={"/galeria/" + gallery[0] + "/" + i + ".webp"}
                                                   alt={"galeria"}
                                                   className={""} layout={"fill"} objectFit={"contain"}/> :
                                            <img key={i} loading={"lazy"}
                                                 src={"/galeria/" + gallery[0] + "/" + i + ".webp"} alt={"galeria"}
                                                 className={""}/>
                                        }
                                    </div>
                                )
                            }
                        </Carousel>
                }
            </div>
        </div>
    </>
}
