<svg width="39" height="39" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_104_10)" filter="url(#filter0_d_104_10)">
<path d="M19.5 23.25C21.5554 23.25 23.5267 22.4335 24.9801 20.9801C26.4335 19.5267 27.25 17.5554 27.25 15.5C27.25 13.4446 26.4335 11.4733 24.9801 10.0199C23.5267 8.56652 21.5554 7.75 19.5 7.75C17.4446 7.75 15.4733 8.56652 14.0199 10.0199C12.5665 11.4733 11.75 13.4446 11.75 15.5C11.75 17.5554 12.5665 19.5267 14.0199 20.9801C15.4733 22.4335 17.4446 23.25 19.5 23.25ZM19.5 6.2C19.9111 6.2 20.3053 6.0367 20.596 5.74602C20.8867 5.45533 21.05 5.06109 21.05 4.65V1.55C21.05 1.13891 20.8867 0.744666 20.596 0.453984C20.3053 0.163303 19.9111 0 19.5 0C19.0889 0 18.6947 0.163303 18.404 0.453984C18.1133 0.744666 17.95 1.13891 17.95 1.55V4.65C17.95 5.06109 18.1133 5.45533 18.404 5.74602C18.6947 6.0367 19.0889 6.2 19.5 6.2ZM19.5 24.8C19.0889 24.8 18.6947 24.9633 18.404 25.254C18.1133 25.5447 17.95 25.9389 17.95 26.35V29.45C17.95 29.8611 18.1133 30.2553 18.404 30.546C18.6947 30.8367 19.0889 31 19.5 31C19.9111 31 20.3053 30.8367 20.596 30.546C20.8867 30.2553 21.05 29.8611 21.05 29.45V26.35C21.05 25.9389 20.8867 25.5447 20.596 25.254C20.3053 24.9633 19.9111 24.8 19.5 24.8ZM10.7316 8.92335C11.024 9.20569 11.4155 9.36193 11.8219 9.35839C12.2283 9.35486 12.6171 9.19185 12.9045 8.90447C13.1919 8.61709 13.3549 8.22833 13.3584 7.82192C13.3619 7.41552 13.2057 7.02398 12.9233 6.73165L10.7316 4.53995C10.4393 4.2576 10.0478 4.10137 9.64138 4.10491C9.23497 4.10844 8.84621 4.27145 8.55883 4.55883C8.27145 4.84621 8.10844 5.23497 8.10491 5.64138C8.10137 6.04778 8.2576 6.43932 8.53995 6.73165L10.7316 8.92335ZM28.2683 22.0766C27.976 21.7943 27.5845 21.6381 27.1781 21.6416C26.7717 21.6451 26.3829 21.8081 26.0955 22.0955C25.8081 22.3829 25.6451 22.7717 25.6416 23.1781C25.6381 23.5845 25.7943 23.976 26.0766 24.2683L28.2683 26.46C28.5607 26.7424 28.9522 26.8986 29.3586 26.8951C29.765 26.8916 30.1538 26.7286 30.4412 26.4412C30.7286 26.1538 30.8916 25.765 30.8951 25.3586C30.8986 24.9522 30.7424 24.5607 30.46 24.2683L28.2683 22.0766ZM10.2 15.5C10.2 15.0889 10.0367 14.6947 9.74602 14.404C9.45533 14.1133 9.06109 13.95 8.65 13.95H5.55C5.13891 13.95 4.74467 14.1133 4.45398 14.404C4.1633 14.6947 4 15.0889 4 15.5C4 15.9111 4.1633 16.3053 4.45398 16.596C4.74467 16.8867 5.13891 17.05 5.55 17.05H8.65C9.06109 17.05 9.45533 16.8867 9.74602 16.596C10.0367 16.3053 10.2 15.9111 10.2 15.5ZM33.45 13.95H30.35C29.9389 13.95 29.5447 14.1133 29.254 14.404C28.9633 14.6947 28.8 15.0889 28.8 15.5C28.8 15.9111 28.9633 16.3053 29.254 16.596C29.5447 16.8867 29.9389 17.05 30.35 17.05H33.45C33.8611 17.05 34.2553 16.8867 34.546 16.596C34.8367 16.3053 35 15.9111 35 15.5C35 15.0889 34.8367 14.6947 34.546 14.404C34.2553 14.1133 33.8611 13.95 33.45 13.95ZM10.7316 22.0766L8.53995 24.2683C8.39191 24.4113 8.27383 24.5824 8.19259 24.7715C8.11136 24.9606 8.0686 25.164 8.06681 25.3698C8.06502 25.5756 8.10424 25.7797 8.18218 25.9702C8.26011 26.1607 8.3752 26.3337 8.52074 26.4793C8.66627 26.6248 8.83933 26.7399 9.02982 26.8178C9.22031 26.8958 9.42441 26.935 9.63022 26.9332C9.83603 26.9314 10.0394 26.8886 10.2285 26.8074C10.4176 26.7262 10.5887 26.6081 10.7316 26.46L12.9233 24.2683C13.2057 23.976 13.3619 23.5845 13.3584 23.1781C13.3549 22.7717 13.1919 22.3829 12.9045 22.0955C12.6171 21.8081 12.2283 21.6451 11.8219 21.6416C11.4155 21.6381 11.024 21.7943 10.7316 22.0766ZM27.1725 9.3775C27.5836 9.37741 27.9777 9.21405 28.2683 8.92335L30.46 6.73165C30.6081 6.58867 30.7262 6.41763 30.8074 6.22853C30.8886 6.03942 30.9314 5.83603 30.9332 5.63022C30.935 5.42441 30.8958 5.22031 30.8178 5.02982C30.7399 4.83933 30.6248 4.66627 30.4793 4.52074C30.3337 4.3752 30.1607 4.26011 29.9702 4.18218C29.7797 4.10424 29.5756 4.06502 29.3698 4.06681C29.164 4.0686 28.9606 4.11136 28.7715 4.19259C28.5824 4.27383 28.4113 4.39191 28.2683 4.53995L26.0766 6.73165C25.8599 6.94842 25.7124 7.22457 25.6526 7.5252C25.5928 7.82583 25.6235 8.13743 25.7408 8.42061C25.8581 8.7038 26.0567 8.94585 26.3115 9.11617C26.5664 9.28649 26.866 9.37743 27.1725 9.3775Z" fill="#CFA200"/>
</g>
<defs>
<filter id="filter0_d_104_10" x="0" y="0" width="39" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_104_10"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_104_10" result="shape"/>
</filter>
<clipPath id="clip0_104_10">
<rect width="31" height="31" fill="white" transform="translate(4)"/>
</clipPath>
</defs>
</svg>
