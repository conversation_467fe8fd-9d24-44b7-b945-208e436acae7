import React, {useEffect, useState} from "react";
import Link from "next/link";

export default function ArticlesGrid({isMobile}) {
    const [articleData, setArticleData] = useState([])

    useEffect(() => {
        const fetchArticle = async () => {
            try {
                const response = await fetch("/api/blog/getArticlesForHome", {
                    method: 'GET',
                })
                const data = await response.json()
                setArticleData(data.data)
            } catch (error) {
                console.error(error);
            }
        }
        fetchArticle()
    }, [])

    const myLoader = (image: any) => {
        return `https://admin.laurindvor.com/files/${image}?w=400&q=75`
    }
    return (
        <div className="grid p-1 lg:p-20 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-10">
            <>
                {articleData.length > 0 ?
                    <>
                        {articleData.map(article => (
                            <Link key={article.id} href={"/aktuality/" + article.articleSlug}>
                                <div
                                    className={"w-full flex flex-col hover:shadow-xl transition-all rounded-lg cursor-pointer hover:scale-105 justify-between relative pb-4"}>
                                    <div className={"contentino pb-6"}>
                                        <div className={"min-h-52 rounded-lg max-h-52 bg-cover bg-center"}
                                             style={{backgroundImage: "url('" + myLoader(article.articleImage.split('/').pop()) + "')"}}>

                                        </div>
                                        <div className={"px-2"}>
                                            <small>{article.dateCreate.slice(0, 7)}</small>
                                            <p className={"text-xl lg:text-2xl mt-2 mb-4"}>{article.articleHeading.substring(0, 80) + "..."}</p>
                                            <div className={"mb-2 articleIntro"}
                                                 dangerouslySetInnerHTML={{__html: article.articleText.substring(0, 170) + "..."}}></div>
                                            <Link className={"text-blue-600 mt-2 underline"}
                                                  href={"/aktuality/" + article.articleSlug}>Čítať viac</Link>
                                        </div>
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </>
                    :
                    <p className={"h-screen font-bold text-xl pl-5"}>Čoskoro sa dozviete...</p>
                }
            </>

        </div>
    )
}
