<svg width="3121" height="757" viewBox="0 0 3121 757" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1371.5 112C1371.33 110.5 1371.1 106.3 1371.5 101.5L1441.5 97V80.5L1510.5 77.5L1533.5 90.5L1599.5 88L1617 97.5L1654 96L1688 115.5V134L1712.5 147V162H1728.5L1735 166.5L1734 300.5L1712 301.5V340L1328.5 363.5L1295.5 338L1296.5 140.5L1316.5 139L1317.5 114.5L1371.5 112Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M1809 45.5V31L1822 30V26L1866.5 24.5L1874 30H1880V17H1883.5V31.5L1901 39.5L1956 36.5L1977 46L2005 44L2046 63.5V80.5L2070 92V265.5L1730 290.5L1680.5 253.5V83.5L1701.5 83V61L1750 59V48L1809 45.5Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M2129.5 26.5V13L2189.5 9L2221 21L2272 18.5L2296 27.5L2319 26.5L2368.5 44V46.5H2366V63L2387.5 70V85.5L2404.5 84L2411 88L2408 211H2383.5V244.5L2069.5 265L2007.5 230V64H2032.5V42.5L2076.5 40V29.5L2129.5 26.5Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M2686.5 31.5V14.5L2649 3L2598.5 6.5L2576 0L2489 3V5L2458.5 6V25.5L2434 26.5V41L2427.5 39H2412V41V177L2621 265L2752 250.5L2761 85L2730 74.5V59.5H2732V56.5L2707 48.5L2707.5 37.5L2686.5 31.5Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M246 242L251.5 207H254.5H267L270.5 179L334 176.5V163L425 160V141L508.5 138L513.5 154L601 149.5L605 160L663 157.5L673 179H680L694 215L697 231.5H715.5L723.5 381.5H703.5L706.5 425L241.5 462.5L227 272.5V261.5H248.5L246 242Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M951 124V109L1027 104L1040.5 117.5L1118.5 116L1129 125.5L1174.5 123L1200 145.5V156.5L1222 177.5V194L1241 192.5L1244.5 197.5L1246.5 225.5V293H1239V301C1239 301.8 1246.33 306.667 1250 309L1248.5 337H1225L1227 378.5L818 404L796.5 366L794.5 279L791 272L793 231L791 227.5V223.5L793 221V169H814.5V145L871 141V128.5L951 124Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <g filter="url(#filter0_bd_1059_4133)">
        <path d="M80.5 671.5V527.5L2893 280L3028 287L3041 324.5L2960 336L2942.5 363L2900 396L2594 423L2515.5 382L2431 453.5V491L1138 615.5L80.5 671.5Z"
              fill="#D9D9D9" fill-opacity="0.01" shape-rendering="crispEdges"/>
    </g>
    <g filter="url(#filter1_d_1059_4133)">
        <rect x="325" y="506.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 325 506.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter2_d_1059_4133)">
        <rect x="881" y="457.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 881 457.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter3_d_1059_4133)">
        <rect x="1366" y="416.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 1366 416.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter4_d_1059_4133)">
        <rect x="1763" y="382.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 1763 382.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter5_d_1059_4133)">
        <rect x="2115" y="353.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 2115 353.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter6_d_1059_4133)">
        <rect x="2470" y="323.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 2470 323.293)"
              fill="#D1D1B6"/>
    </g>
    <path d="M437.949 512.345L446.469 532.574L446.766 532.557L452.814 511.456L455.912 511.271L448.382 536.363L445.317 536.546L434.818 512.532L437.949 512.345ZM468.391 535.497C466.304 535.622 464.411 535.223 462.713 534.299C461.015 533.375 459.638 532.002 458.58 530.18C457.534 528.346 456.933 526.138 456.779 523.557C456.624 520.964 456.956 518.695 457.777 516.749C458.609 514.803 459.813 513.271 461.389 512.151C462.965 511.031 464.796 510.409 466.884 510.284C468.982 510.159 470.875 510.558 472.562 511.483C474.249 512.407 475.616 513.786 476.662 515.62C477.709 517.454 478.31 519.667 478.465 522.26C478.619 524.842 478.286 527.105 477.465 529.051C476.644 530.985 475.451 532.512 473.886 533.631C472.321 534.75 470.489 535.372 468.391 535.497ZM468.226 532.729C469.709 532.64 471.027 532.187 472.18 531.368C473.343 530.538 474.232 529.371 474.848 527.868C475.475 526.364 475.725 524.552 475.598 522.431C475.47 520.3 475.006 518.525 474.204 517.107C473.413 515.677 472.391 514.624 471.138 513.949C469.895 513.263 468.532 512.964 467.049 513.053C465.566 513.142 464.243 513.601 463.08 514.431C461.916 515.251 461.021 516.418 460.395 517.933C459.768 519.436 459.519 521.254 459.646 523.385C459.773 525.506 460.237 527.275 461.038 528.693C461.84 530.112 462.867 531.164 464.121 531.85C465.374 532.524 466.742 532.818 468.226 532.729ZM482.593 534.317L481.125 509.764L484.091 509.586L485.401 531.503L496.838 530.82L496.996 533.456L482.593 534.317ZM518.038 507.556L519.506 532.11L516.573 532.286L502.072 513.803L501.808 513.819L502.961 533.1L499.995 533.277L498.527 508.723L501.394 508.552L515.996 527.061L516.226 527.047L515.072 507.734L518.038 507.556ZM523.857 531.85L522.388 507.296L537.22 506.409L537.377 509.046L525.512 509.756L526.003 517.962L537.077 517.3L537.235 519.937L526.161 520.599L526.665 529.036L538.695 528.317L538.853 530.953L523.857 531.85Z"
          fill="black"/>
    <defs>
        <filter id="filter0_bd_1059_4133" x="0.5" y="205" width="3120.5" height="551.5" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="3.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1059_4133"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="5"/>
            <feGaussianBlur stdDeviation="40"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1059_4133" result="effect2_dropShadow_1059_4133"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1059_4133" result="shape"/>
        </filter>
        <filter id="filter1_d_1059_4133" x="307.088" y="467.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1059_4133"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1059_4133" result="shape"/>
        </filter>
        <filter id="filter2_d_1059_4133" x="863.088" y="418.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1059_4133"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1059_4133" result="shape"/>
        </filter>
        <filter id="filter3_d_1059_4133" x="1348.09" y="377.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1059_4133"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1059_4133" result="shape"/>
        </filter>
        <filter id="filter4_d_1059_4133" x="1745.09" y="343.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1059_4133"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1059_4133" result="shape"/>
        </filter>
        <filter id="filter5_d_1059_4133" x="2097.09" y="314.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1059_4133"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1059_4133" result="shape"/>
        </filter>
        <filter id="filter6_d_1059_4133" x="2452.09" y="284.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1059_4133"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1059_4133" result="shape"/>
        </filter>
    </defs>
</svg>
