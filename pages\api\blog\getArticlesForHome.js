import executeQuery from "../../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const rows = await executeQuery({
            query: 'SELECT * FROM articles WHERE isOnHome = 1 ORDER BY dateCreate DESC LIMIT 4'
        })
        res.status(200).json({data: rows});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: error});
    }
}
