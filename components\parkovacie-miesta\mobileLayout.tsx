import Image from "next/image";
import Router from "next/router";

export default function MobileLayoutParking({place}) {
    if (place === undefined) {
        return null;
    }
    let placeP = JSON.parse(place)

    return (
        <>
            <section
                className={"flex flex-col items-center px-5 justify-between bg-[#E3E2E7] mt-20 lg:-mt-[6vh] mb-32"}>
                <div className={"info w-full px-1 mb-6"}>
                    <div className={"flex justify-between pt-8"}>
                        <div onClick={() => Router.back()}
                             className={"bg-black text-white flex items-center p-[0.5vw] rounded-full"}>
                            <svg className="w-[13vw] h-[13vw] text-white" aria-hidden="true"
                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                      strokeWidth="2" d="M5 12h14M5 12l4-4m-4 4 4 4"/>
                            </svg>
                        </div>
                        <div className={"bg-black text-white rounded-full px-[5vw] flex items-center gap-2"}>
                            <h1 className={"text-[4vw] mt-1 font-light"}>PARKOVACIE MIESTO: <span
                                className={"text-[8vw] m-0 font-extralight"}>{placeP["parking_number"]}</span></h1>
                        </div>
                    </div>
                    <div className={"flex mt-5 justify-between items-start gap-4"}>
                        <div className={"flex flex-col"}>
                            <div className="mb-[2vw]">
                                <small className="uppercase text-[3vw]">PLOCHA PARKOVANIA</small>
                                <p className={"mb-5 text-[7vw]"}>{placeP["plocha_parkovania"]}m²</p>
                            </div>
                            <div className="mb-[2vw] flex gap-1 flex-col text-left">
                                <small className="uppercase text-[3vw]">TYP PARKOVACIEHO MIESTA</small>
                                <p className={"mb-5 uppercase text-[4vw]"}>{placeP["section"] === "VS" ? "Vonkajšie so skladom" : "neni take"}</p>
                            </div>
                        </div>
                        <div className={"flex flex-col gap-3 text-right justify-end"}>
                            <div className="mb-[2vw] text-right">
                                <small className="uppercase text-[3vw]">PLOCHA SKLADU</small>
                                <p className="text-[7vw]">{placeP["plocha_skladu"]}-izbový</p>
                            </div>
                            <div className="mb-[2vw]">
                                <small className={"text-[3vw]"}>CELKOVÁ VÝMERA</small>
                                <p className={"text-[7vw] font-bold"}>{placeP["celkova_vymera"]}m²</p>
                            </div>
                        </div>
                    </div>
                    <div className={"text-center my-5"}>
                        <div
                            className={"bg-black flex items-center px-[2vw] justify-center gap-8 text-white mx-2 rounded-full py-[1vw]"}>
                            <small className="font-light mt-0.5 text-[4vw]">CENA S DPH</small>
                            <p className={"text-[7vw]"}>{placeP["price"].toString().replace(/\B(?=(\d{3})+(?!\d))/g, " ")} €</p>
                        </div>
                    </div>
                </div>
                <Image src={"/parkovacie-miesta/podorysy/" + placeP["parking_number"] + ".png"}
                       alt={"pôdorys parkovacieho miesta"}
                       width={300} className={placeP.section === "V2" ? "px-0 py-8" : "px-10"}
                       height={100}/>
            </section>
        </>
    )
}