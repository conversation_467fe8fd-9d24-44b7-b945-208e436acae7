import dynamic from "next/dynamic";
import executeQuery from "../../utils/dbConnection";
const DesktopLayoutParking = dynamic(() => import('../../components/parkovacie-miesta/desktopLayout'));
const MobileLayoutParking = dynamic(() => import('../../components/parkovacie-miesta/mobileLayout'));

export const getServerSideProps = (async (context: any) => {
    let park: string = "";
    if (context.query.parking) {
        const flaty = await executeQuery({
            query: 'SELECT * FROM parking WHERE id = ?',
            values: [context.query.parking]
        });
        park = JSON.stringify(flaty[0]);
    }
    return {props: {park}};
})

export default function ParkingPlace({park, isMobile}) {
    return (
        <>
            {
                isMobile ? <MobileLayoutParking place={park}/> :
                    <DesktopLayoutParking isMobile={isMobile} place={park} getter={undefined} selectedFlat={undefined}/>
            }
        </>
    )
}