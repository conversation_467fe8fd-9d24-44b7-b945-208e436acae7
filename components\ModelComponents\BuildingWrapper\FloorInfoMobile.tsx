//@ts-nocheck
import {Html} from "@react-three/drei";
import {Button, Toast} from "flowbite-react";
import {FloorInfo} from "../../../utils/interfaces/FloorInfo";
import {useEffect, useState} from "react";
import fetchFlatData from "../../../utils/fetchFlatsAvaiability";

export default function FloorInfoMobile(props: FloorInfo) {
    const [flatData, setFlatData] = useState({allFlats: 0, freeFlats: 0});

    useEffect(() => {
        fetchFlatData(props.floor, props.letter).then((r) => setFlatData(r))
    }, [props])

    return (
        <Html wrapperClass={"mobileModal"} prepend={true}>
            <Toast className={"ml-auto mr-auto"}>
                <div className="flex items-start w-full">
                    <div className="text-sm flex gap-5 px-3 justify-between font-normal">
                        <div>
                            <span
                                className="mb-1 text-lg font-semibold text-gray-900 dark:text-white">{props.floor ? props.floor + '. poschodie' : "Prízemie"} v budove <strong>{props.letter}</strong></span>
                            <div className="mb-2 text-sm font-normal">Počet voľných
                                bytov: <strong>{flatData["freeFlats"]}/{flatData["allFlats"]}</strong>
                            </div>
                        </div>
                        <div className="flex-start flex gap-2">
                            <Button onClick={props.modalOpener}><span
                                className={"text-[4vw]"}>Prejsť na poschodie</span></Button>
                        </div>
                    </div>
                    <Toast.Toggle/>
                </div>
            </Toast>
        </Html>
    );
}