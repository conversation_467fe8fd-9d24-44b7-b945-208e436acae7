import {useParams} from "next/navigation";
import executeQuery from "../../../../utils/dbConnection";
import FlatNavigation from "../../../../components/ponuka-bytov/izba/flatNavigation";
import MobileLayoutPodorys from "../../../../components/ponuka-bytov/izba/mobileLayoutPodorys";
import DesktopLayoutPodorys from "../../../../components/ponuka-bytov/izba/desktopLayoutPodorys";

export const getServerSideProps = async (context: any) => {
    let flat = "";

    async function fetchFlat(id: number): Promise<string> {
        const flaty = await executeQuery({
            query: 'SELECT * FROM byty WHERE id = ?',
            values: [id]
        });
        // @ts-ignore
        if (flaty.length === 0) {
            return "";
        }

        if (flaty[0].isVariant) {
            return fetchFlat(id - 1);
        } else {
            return JSON.stringify(flaty[0]);
        }
    }

    const [result] = await Promise.all([fetchFlat(context.query.byt)]);
    flat = result || "";

    return {props: {flat}};
};

export default function Byt({flat, isMobile}) {
    const {byt} = useParams()
    const {izba} = useParams()
    const gallery = [2, 8, 6, 8, 7];
    flat = JSON.parse(flat)
    let room = parseInt(izba[0])
    return (
        <>
            <FlatNavigation isMobile={isMobile} room={room} getter={() => {
            }}/>
            {
                isMobile ? <MobileLayoutPodorys flat={flat} room={room} gallery={gallery}/> :
                    <DesktopLayoutPodorys flat={flat} room={room} getSelectedFlat={byt}
                                          selectedFlat={flat["budova"]} gallery={gallery}
                                          getSelectedFloor={4} flatName={undefined}/>
            }
        </>

    )
}