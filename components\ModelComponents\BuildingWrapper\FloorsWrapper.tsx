//@ts-nocheck
import Floor from "./Floor";
import calculateFloors from "../../../utils/calculateFloors";
import {Euler} from "@react-three/fiber";

interface FloorsWrapperProps {
    x: number,
    z: number
    hovered: boolean,
    letter: string,
    active: boolean,
    rotation: Euler,
    geometry: [width?: number, height?: number, depth?: number, widthSegments?: number, heightSegments?: number, depthSegments?: number],
    isMobile: boolean,
    cameraRef: {
        current: {
            reset: (arg0: boolean) => void;
            setLookAt: (arg0: number, arg1: number, arg2: number, arg3: number, arg4: number, arg5: number, arg6: boolean) => void;
            camera: { zoom: number; };
            dolly: (arg0: number, arg1: boolean) => void;
        };
    };
    zoomed: boolean;
    unZoom: () => void;
    stateChanger: () => void,
    flatsAvaibility: any[]
}

export default function FloorsWrapper(props: FloorsWrapperProps) {
    return Array.from({length: 5}, (_, i) => (
        <Floor flatsAvaibility={props.flatsAvaibility} zoomed={props.zoomed} unZoom={props.unZoom} stateChanger={props.stateChanger} active={props.active} isMobile={props.isMobile} key={i} floor={i} cameraRef={props.cameraRef} rotation={props.rotation} letter={props.letter}
               geometry={props.geometry} position={[props.x, calculateFloors(i), props.z]}/>));
}