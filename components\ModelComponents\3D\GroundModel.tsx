//@ts-nocheck
import { useGLTF } from "@react-three/drei";

export function GroundModel() {
    const { nodes, materials } = useGLTF("/ground_final.glb");
    return (
        <group dispose={null}>
            <group scale={0.5} position={[-14.87, 1.19, -2.4]} rotation={[0, -1.571, 0]}>
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh002["geometry"]}
                    material={materials.PaletteMaterial001}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh002_1["geometry"]}
                    material={materials["TRAVA.002"]}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh002_2["geometry"]}
                    material={materials.Cesta}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh002_3["geometry"]}
                    material={materials.Chodniky}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh002_4["geometry"]}
                    material={materials.Concrete}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh002_5["geometry"]}
                    material={materials.DrIEvko}
                />
                <mesh
                    geometry={nodes.Mesh002_6["geometry"]}
                    material={materials.PaletteMaterial002}
                />
            </group>
        </group>
    );
}

useGLTF.preload("/ground_final.glb");