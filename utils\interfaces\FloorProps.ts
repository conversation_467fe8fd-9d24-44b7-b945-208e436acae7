import {Euler, Vector3} from "@react-three/fiber";

export interface FloorProps {
    active: boolean;
    position: Vector3;
    rotation: Euler;
    geometry: [width?: number, height?: number, depth?: number, widthSegments?: number, heightSegments?: number, depthSegments?: number];
    floor: number;
    letter: string;
    isMobile: boolean;
    cameraRef: {
        current: {
            reset: (arg0: boolean) => void;
            setLookAt: (arg0: number, arg1: number, arg2: number, arg3: number, arg4: number, arg5: number, arg6: boolean) => void;
            camera: { zoom: number; };
            dolly: (arg0: number, arg1: boolean) => void;
        };
    };
    zoomed: boolean;
    unZoom: () => void;
    stateChanger: (action: string) => void,
    flatsAvaibility: any[]
}