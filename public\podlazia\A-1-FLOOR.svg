<svg width="1423" height="666" viewBox="0 0 1423 666" fill="none" xmlns="http://www.w3.org/2000/svg">
    <style>
        .bytikWrapper, .bytik, .balkonik{transition: .2s}
        .bytikWrapper:hover .bytik{cursor: pointer; fill: #657492}
        .bytikWrapper:hover .balkonik{cursor: pointer; fill: #c1a179}
    </style>
    <script>

    </script>
    <g clip-path="url(#clip0_141_54)">
        <g filter="url(#filter0_d_141_54)">
            <path d="M600 648.907V637.746V636.471V586.725V578.908H637.586L777.908 578.907L818 578.908V586.725V636.471V640.322L818 648.907H600Z"
                  fill="#C2B09A"/>
        </g>
        <path d="M594 578.907V341.697V266.907H638.257H731.614H817V309.276V578.907H594Z" fill="#B6BFD1"/>
        <g filter="url(#filter1_d_141_54)">
            <path d="M1121 649V635.925V634.432V576.158V567H1155.14L1282.59 567L1319 567V576.158V634.432V638.943L1319 649H1121Z"
                  fill="#C2B09A"/>
        </g>
        <path d="M1097 567.116V198.638H1120.83V84H1150.75H1279.76H1397.76V149.607V567.116H1097Z" fill="#B6BFD1"/>
        <g filter="url(#filter2_d_141_54)">
            <path d="M957 648.907V637.746V636.471V586.725V578.908H983.897L1084.31 578.907L1113 578.908V586.725V636.471V640.322L1113 648.907H957Z"
                  fill="#C2B09A"/>
        </g>
        <path d="M824.999 578.907V263.907H847.499V165.907H875.751H997.576H1109V221.992V578.907H824.999Z"
              fill="#B6BFD1"/>
        <g filter="url(#filter3_d_141_54)">
            <path d="M439 648.907V637.746V636.471V586.725V578.908H409.69L300.264 578.907L269 578.908V586.725V636.471V640.322L269 648.907H439Z"
                  fill="#C2B09A"/>
        </g>
        <path d="M594 578.907V264.907H529.5V165.907H393.442H269V221.992V578.907H594Z" fill="#B6BFD1"/>
        <a href="#bytA-105" class="bytikWrapper">
            <g>
                <path class="bytik" d="M269 578.907V492.741H266.101L259 148.071V93.9072L222.618 93.91L60.2811 93.9072L13.8994 93.91V148.071V492.741H11V578.907H269Z"
                      fill="#B6BFD1"/>
            </g>
            <g>
                <path class="balkonik" d="M261 648.907V588.413V578.907H246.442H151.156H83V588.413V648.907H261Z" fill="#C2B09A"/>
            </g>
        </a>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1120.51 648.907H1110.62V579.655H1120.51V648.907Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M827.653 648.907H817.768V579.655H827.653V648.907Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M602.761 648.907H592.876V579.655H602.761V648.907Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M269.376 648.907H259.491V579.655H269.376V648.907Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M814.061 0.907227V9.81096H826.417V22.1774H835.314V0.907227H814.061Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M201.415 84.0094V73.1272H151.988V84.0094H201.415ZM343.27 84.0094V73.1272H240.956V84.0099L343.27 84.0094ZM570.387 82.0309V74.6109H585.215V82.0309H570.387ZM524.914 73.1272V84.0099H594.112V73.1272H524.914Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M1412.13 568.772V84.0094H1316.98V73.1272H1423V579.655H1315.74V568.772H1412.13Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M1178.58 73.1272V83.8858H1228.01V73.1272H1178.58ZM979.642 83.8858V73.1272H826.418V83.8858H979.642ZM983.348 73.1272V83.8858H1139.04V73.1272H983.348Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1097.03 579.655V568.773H1177.35V579.655H1097.03Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M930.214 579.655V568.773H1008.06V579.655H930.214Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M780.697 579.655V568.773H841.245V579.655H780.697Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M579.283 579.655V568.773H642.302V579.655H579.283Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M408.761 579.655V568.773H465.601V579.655H408.761Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M0 579.655V73.1272H63.0192V84.0094H10.8742V568.772H82.5432V579.655H0Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M220.938 579.655V568.773H295.079V579.655H220.938Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M1382.47 355.904V316.35H1402.24V321.294H1387.41V355.904H1382.47Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M1273.73 361.589V353.432H1279.42V355.904H1402.24V361.589H1273.73Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M1273.73 313.877V266.907H1263.35V261.222H1402.24V266.907H1279.42V313.877H1273.73Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M1181.06 266.907V93.8589H1186.74V261.222H1218.87V266.907H1181.06Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1121.74 361.589V355.904H1214.42V361.589H1121.74Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M35.5878 355.904V321.294H20.7593V316.35H40.5302V355.904H35.5878Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M143.585 313.877V266.907H20.7593V261.222H193.259V258.75H198.944V266.907H149.269V313.877H143.585Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M193.259 214.251V93.8589H198.944V214.251H193.259Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M198.944 361.589V355.904H258.256V361.589H198.944Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M20.7593 361.589V355.904H143.585V353.432H149.269V361.589H20.7593Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M435.204 558.618V355.904H443.36V361.589H440.888V558.618H435.204Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M435.204 266.907V237.242H270.611L270.612 231.556H347.224V201.891H352.908V231.556H435.204V207.576H440.888V266.907H435.204Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M435.204 168.022V165.55H440.888V168.022H435.204Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M285.441 231.556V206.835H270.613L270.61 201.891H290.383V231.556H285.441Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M591.641 321.294H576.812V355.903L571.869 355.905V316.349H591.641V321.294Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M495.257 313.877V261.222H524.913V266.907H500.941V313.877H495.257Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M487.843 361.589V355.904H495.257V353.432H500.941V355.904H591.64V361.589H487.843Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M618.825 355.904V321.294H603.997V316.349H623.767V355.904H618.825Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M726.823 355.904V353.432H732.507V361.589H603.997V355.904H726.823Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M726.822 313.877V266.907H732.506V313.877H726.822Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M786.876 361.589V355.904H816.532V361.589H786.876Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M1089.62 231.556V201.891H1109.39V206.835H1094.56V231.556H1089.62Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M828.889 361.589V355.904H928.484V353.432H934.168V355.904H989.279V361.589H828.889Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M964.813 519.063V401.143H970.497V519.063H964.813Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M928.485 313.877V266.907H853.603V261.222H934.168V313.877H928.485Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M983.596 266.907V207.576H989.279V231.556H1109.39V237.242H989.279V266.907H983.596Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M983.596 168.022V165.55H989.279V168.022H983.596Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M843.717 355.904V321.294H828.889V316.35H848.66V355.904H843.717Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M20.7593 316.35H25.7017V266.908H20.7593V316.35ZM816.532 254.546V200.16H821.475V210.049H841.246V214.993H821.475V254.546H816.532Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M500.941 266.907V271.852H586.697V316.349L591.641 316.35V266.907H500.941Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M726.823 266.907V271.852H608.963V316.35H604.02V266.907H726.823Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M928.484 266.907V271.852H833.832V316.35H828.889V266.907H928.484Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M631.182 355.904H623.768V316.349H631.182V355.904Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M571.869 355.904H564.455V316.35H571.869V355.904Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M856.074 355.904H848.66V316.35H856.074V355.904Z" fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M47.9445 355.904H40.5303V316.35H47.9445V355.904Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1382.47 355.904H1375.06V316.35H1382.47V355.904Z"
              fill="black"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M524.913 165.55H537.27V266.907H736.213V254.546H537.27V266.907H524.913V165.55ZM444.595 153.19V165.55H270.612V153.19H444.595ZM494.021 165.55V153.19H537.27V165.55H494.021ZM223.409 568.506V558.617H292.607V568.506H223.409ZM258.255 558.617H270.612V147.009H258.255V558.617ZM270.612 93.8588H258.256V97.5668H270.612V93.8588ZM340.798 83.9703V93.8588H243.427V83.9703H340.798ZM198.943 83.9703V93.8588H154.459V83.9703H198.943ZM60.5478 83.9703V93.8588H10.8735H20.759V568.506V558.617H80.0713V568.506H10.8741L10.8735 93.8588L10.8741 83.9703H60.5478ZM1225.54 83.9703V93.8588H1181.06V83.9703H1225.54ZM1319.45 93.8588V83.9703H1402.24V558.617H1412.13H1318.22V568.506H1412.13V558.617V83.9703H1402.24V93.8588H1319.45ZM829.017 266.913L816.661 266.907L816.533 558.611L828.889 558.617L829.017 266.913ZM838.774 558.617V568.506H783.169V558.617H838.774ZM1005.59 558.617V568.506H932.685V558.617H1005.59ZM1174.88 558.617V568.506H1099.5V558.617H1174.88ZM1121.74 558.617H1109.39V153.19V165.55H977.417V153.19H1121.74V558.617ZM927.99 153.19V165.55H816.532V153.19H927.99ZM603.997 19.6953H594.111V93.8588H603.997V19.6953ZM594.111 19.6953V9.80676H826.417V19.6953H594.111ZM594.111 83.9703V93.8588H524.913V83.9703H594.111ZM463.13 568.506V558.617H411.232V568.506H463.13ZM639.831 558.617V568.506H581.755V558.617H639.831ZM603.997 558.617H591.64V266.908H603.997V558.617ZM853.602 165.55H841.245V254.546H853.602V266.907H785.64V254.546H853.602V165.55ZM1121.74 147.009V153.19H1109.39V147.009H1121.74ZM1136.57 83.9703V93.8588H1121.74V83.9703H1109.39V97.5668H1121.74V83.9703H1136.57ZM1109.39 83.8468V93.8588H816.533V75.5652H826.418V93.8588V83.8468H1109.39Z"
              fill="black"/>
        <path d="M537.27 165.55H524.913V266.907H537.27M537.27 165.55V266.907M537.27 165.55V153.19H494.021V165.55H537.27ZM537.27 266.907H736.213V254.546H537.27V266.907ZM10.8735 93.8588H60.5478V83.9703H10.8741L10.8735 93.8588ZM10.8735 93.8588H20.759V568.506V558.617H80.0713V568.506H10.8741L10.8735 93.8588ZM1402.24 83.9703H1319.45V93.8588H1402.24V83.9703ZM1402.24 83.9703V558.617H1412.13M1402.24 83.9703H1412.13V558.617M1412.13 558.617H1318.22V568.506H1412.13V558.617ZM1109.39 153.19V558.617H1121.74V153.19M1109.39 153.19V165.55H977.417V153.19H1121.74M1109.39 153.19H1121.74M1109.39 153.19V147.009H1121.74V153.19M594.111 19.6953H603.997V93.8588H594.111M594.111 19.6953V93.8588M594.111 19.6953V9.80676H826.417V19.6953H594.111ZM594.111 93.8588V83.9703H524.913V93.8588H594.111ZM853.602 254.546H841.245V165.55H853.602V254.546ZM853.602 254.546V266.907H785.64V254.546H853.602ZM1121.74 83.9703V93.8588H1136.57V83.9703H1121.74ZM1121.74 83.9703H1109.39V97.5668H1121.74V83.9703ZM444.595 153.19V165.55H270.612V153.19H444.595ZM223.409 568.506V558.617H292.607V568.506H223.409ZM258.255 558.617H270.612V147.009H258.255V558.617ZM270.612 93.8588H258.256V97.5668H270.612V93.8588ZM340.798 83.9703V93.8588H243.427V83.9703H340.798ZM198.943 83.9703V93.8588H154.459V83.9703H198.943ZM1225.54 83.9703V93.8588H1181.06V83.9703H1225.54ZM829.017 266.913L816.661 266.907L816.533 558.611L828.889 558.617L829.017 266.913ZM838.774 558.617V568.506H783.169V558.617H838.774ZM1005.59 558.617V568.506H932.685V558.617H1005.59ZM1174.88 558.617V568.506H1099.5V558.617H1174.88ZM927.99 153.19V165.55H816.532V153.19H927.99ZM463.13 568.506V558.617H411.232V568.506H463.13ZM639.831 558.617V568.506H581.755V558.617H639.831ZM603.997 558.617H591.64V266.908H603.997V558.617ZM1109.39 83.8468V93.8588H816.533V75.5652H826.418V93.8588V83.8468H1109.39Z"
              stroke="black" stroke-width="0.04" stroke-miterlimit="10"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1397.3 558.617L1402.24 558.617V361.589H1397.3V558.617Z"
              fill="black" stroke="black" stroke-width="0.04" stroke-miterlimit="10"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M1397.3 316.35H1402.24V266.907H1397.3V316.35Z" fill="black"
              stroke="black" stroke-width="0.04" stroke-miterlimit="10"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M747.258 180.512H757.143V89.0434V79.1548H673.117V89.0434H757.143H747.258V180.512Z" fill="black"
              stroke="black" stroke-width="0.04" stroke-miterlimit="10"/>
        <path fill-rule="evenodd" clip-rule="evenodd"
              d="M663.232 79.1548V170.623H692.888V180.512H663.232V170.623H673.117V79.1548H663.232Z" fill="black"
              stroke="black" stroke-width="0.04" stroke-miterlimit="10"/>
    </g>
    <defs>
        <filter id="filter0_d_141_54" x="596" y="578.907" width="226" height="78" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_141_54"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_141_54" result="shape"/>
        </filter>
        <filter id="filter1_d_141_54" x="1115" y="567" width="206" height="90" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2" dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_141_54"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_141_54" result="shape"/>
        </filter>
        <filter id="filter2_d_141_54" x="951" y="578.907" width="164" height="78" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2" dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_141_54"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_141_54" result="shape"/>
        </filter>
        <filter id="filter3_d_141_54" x="267" y="578.907" width="178" height="78" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="2" dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_141_54"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_141_54" result="shape"/>
        </filter>
        <filter id="filter4_d_141_54" x="7" y="93.9072" width="266" height="493" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_141_54"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_141_54" result="shape"/>
        </filter>
        <filter id="filter5_d_141_54" x="77" y="578.907" width="186" height="78" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dx="-2" dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_141_54"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_141_54" result="shape"/>
        </filter>
        <clipPath id="clip0_141_54">
            <rect width="1423" height="665" fill="white" transform="translate(0 0.907227)"/>
        </clipPath>
    </defs>
</svg>
