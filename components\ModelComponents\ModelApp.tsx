//@ts-nocheck
import {Suspense, useEffect, useRef, useState} from "react";
import {Canvas, Vector3} from "@react-three/fiber";
import {Bounds, CameraControls, OrthographicCamera, Sky, SoftShadows} from "@react-three/drei";
import buildingConfigurations from "../../utils/buildingConfig";
import Loader3D from "./Loader3D";
import ModelControls from "./ModelControls";
import ModelsWrapper from "./ModelsWrapper";
import BuildingWrapper from "./BuildingWrapper/BuildingWrapper";

const sunriseSummer = -35.74;
const sunDistance = 550;

export default function ModelApp({flatsAvaibility}) {
    const [isMobile, setIsMobile] = useState(false)
    const [summerTime, setSummerTime] = useState(false)
    const cameraControlsRef = useRef();
    const [active, setActive] = useState(false)
    const [time, setTime] = useState(13)
    const summerColorCalc = Math.round(Math.round(-Math.pow(time - 13.65, 2) + 90) * 1.5);
    const winterColorCalc = Math.round(Math.round(-Math.pow(time - 11.5, 2) + 22) * 5);
    const [sunStrength, setSunStrength] = useState(1.2 * time)
    const [mobileModal, setMobileModal] = useState(false)
    const [shadowsEnabled, enableShadows] = useState(true)
    const [flatModalOpened, setFlatModalOpened] = useState(false)
    const [spaceEnabled, setSpaceEnabled] = useState(false)
    const [sunLocation, setSun] = useState<Vector3>([550 * (Math.cos((sunriseSummer + (time - 5) * 15) * Math.PI / 180)), (550 * 0.5) + 550 * Math.sin((sunriseSummer + (time - 5) * 15) * Math.PI / 180), sunDistance * Math.sin((sunriseSummer + (time - 5) * 15) * Math.PI / 180)]);

    useEffect(() => {
        setIsMobile(window.matchMedia("(max-width: 600px)").matches)
        if (isMobile) {
            enableShadows(false)
        }
    }, [isMobile])

    const stateChanger = (state: string, valueToSet: any) => {
        switch (state) {
            case 'cameraReset':
                setActive(!active)
                setFlatModalOpened(false)
                break;
            case 'toggleShadows':
                enableShadows(!shadowsEnabled)
                break;
            case 'setSunPosition':
                setSun(valueToSet)
                break;
            case 'setSunStrength':
                setSunStrength(valueToSet)
                break;
            case 'toggleSummerTime':
                setSummerTime(valueToSet)
                break;
            case 'toggleMobileModal':
                setMobileModal(valueToSet)
                break;
            case 'setTime':
                setTime(valueToSet)
                break;
            case 'openFlatModal':
                setFlatModalOpened(true)
                break;
            case 'toggleSpace':
                setSpaceEnabled(!spaceEnabled)
                break;
            case 'disableSpace':
                setSpaceEnabled(false)
                break;
        }
    }

    return <>
        <Suspense
            fallback={<Loader3D/>}>
            <div className="CanvasApp h-[80vh] relative">
                {!flatModalOpened &&
                    <ModelControls stateChanger={stateChanger} spaceEnabled={spaceEnabled} isMobile={isMobile}
                                   cameraRef={cameraControlsRef}
                                   summerTime={summerTime} active={active} time={time} shadowsEnabled={shadowsEnabled}/>
                }

                <Canvas shadows={shadowsEnabled} className={"Canvas"} camera={{position: [-150, 50, 150], fov: 20}}>
                    <ambientLight intensity={sunStrength / 40}/>
                    <directionalLight castShadow={shadowsEnabled} position={sunLocation}
                                      intensity={sunStrength / 4.8}
                                      shadow-mapSize={[2048, 2048]}
                                      color={"white"}
                                      shadow-bias={-0.00009}>
                        <OrthographicCamera attach="shadow-camera" zoom={isMobile ? 2 : 15}
                                            args={[-20, 10, 10, -10]}/>
                    </directionalLight>
                    <SoftShadows size={40} samples={40}/>
                    <Sky
                        rayleigh={3}
                        turbidity={10}
                        distance={400}
                        mieCoefficient={0.005}
                        mieDirectionalG={0.7}
                        sunPosition={sunLocation}
                        inclination={0}
                        azimuth={sunLocation[2]}
                    />
                    <CameraControls maxPolarAngle={1.5} minPolarAngle={Math.PI / 5} minDistance={40}
                                    maxDistance={400}
                                    ref={cameraControlsRef}/>
                    <ModelsWrapper spaceEnabled={spaceEnabled}/>
                    <Bounds>
                        {[...Array(6)].map((e, i) => (
                            <BuildingWrapper key={e} x={buildingConfigurations[i].x} z={buildingConfigurations[i].z}
                                             isMobile={isMobile} stateChanger={stateChanger}
                                             rotation={buildingConfigurations[i].rotation}
                                             geometry={buildingConfigurations[i].geometry} setActive={active}
                                             letter={buildingConfigurations[i].letter}
                                             letterPosition={buildingConfigurations[i].letterPosition}
                                             position={buildingConfigurations[i].position}
                                             cameraControlsRef={cameraControlsRef} flatModalOpened={flatModalOpened}
                                             buildingZoom={buildingConfigurations[i].buildingZoom}
                                             flatsAvaibility={flatsAvaibility} spaceIsEnabled={spaceEnabled}
                                             buildingZoomMobile={buildingConfigurations[i].buildingZoomMobile}/>
                        ))}
                    </Bounds>
                </Canvas>
            </div>
        </Suspense>
    </>
}