import Image from "next/image";
import {useEffect, useRef, useState} from "react";
import toast from "react-hot-toast";

export default function OverlayFloorChoose({getter, numberOfFlatsFloor, building, selectedFloor, isMobile}) {
    const floor = useRef(null)
    const [imageData, setImageData] = useState([])
    const [imageLoaded, setImageLoaded] = useState(false)
    const [hovered, setHovered] = useState(false)
    const [hoveredBuilding, setHoveredBuilding] = useState(null)
    const [selectedBuilding, setSelectedBuilding] = useState(null)

    useEffect(() => {
        if (!isNaN(selectedFloor) || selectedFloor !== null)
            setSelectedBuilding(parseInt(selectedFloor))
    }, [selectedFloor]);


    const handleResize = () => {
        setImageLoaded(true);
        setTimeout(() => {
            if (floor === null) {
                console.error("Image is not yet loaded");
            } else {
                setImageData([
                    floor?.current?.clientWidth ?? "0",
                    floor?.current?.clientHeight ?? "0"
                ]);
            }
        }, 200)

    }

    useEffect(() => {
        window.addEventListener("resize", handleResize);
    }, []);

    useEffect(() => {
        handleResize();
    }, [imageLoaded, numberOfFlatsFloor]);

    useEffect(() => {
        setImageLoaded(true);
        if (floor.current !== undefined) {
            setImageData([
                floor.current.clientWidth ?? "0",
                floor.current.clientHeight ?? "0"
            ]);
        }
        handleResize();
    }, [floor]);

    useEffect(() => {
        if (selectedBuilding !== "" && !isNaN(selectedBuilding)) {
            getter(selectedBuilding);
        }
    }, [selectedBuilding])

    return (
        <div className={"overlay relative"}>
            <Image ref={floor} src={isMobile ? "/pohladBudovy_E_Mobile.png" : "/pohladBudovy_E.png"}
                   className={isMobile ? "p-5" : "rounded-b-[3rem]"}
                   onLoad={() => setImageLoaded(true)} alt={"vizualizácia budov"}
                   width={isMobile ? 928 : 3000}
                   height={isMobile ? 675 : 1033}/>
            {imageLoaded ?
                <>
                    <svg width={imageData[0]}
                         height={imageData[1]} viewBox={isMobile ? "0 0 928 675" : "0 0 2200 675"}
                         className={"absolute top-0 z-20"} fill="none"
                         xmlns="http://www.w3.org/2000/svg">
                        <rect className={"cursor-pointer transition-all rounded-xl"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding(4)
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlatsFloor[3] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                setSelectedBuilding(4)
                            }
                        }}
                              fill={hovered && hoveredBuilding === 4 ? numberOfFlatsFloor[3] === 0 ? "red" : "black" : selectedBuilding === 4 ? "#a3cb47" : "transparent"}
                              x={isMobile ? "17" : "655"} y="237" width="894" height="101" fillOpacity="0.5"/>
                        <rect id={"5"} className={"cursor-pointer transition-all rounded-xl"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding(5)
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlatsFloor[4] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                setSelectedBuilding(5)
                            }
                        }}
                              fill={hovered && hoveredBuilding === 5 ? numberOfFlatsFloor[4] === 0 ? "red" : "black" : selectedBuilding === 5 ? "#a3cb47" : "transparent"}
                              x={isMobile ? "17" : "655"} y="94" width="894" height="143" fillOpacity="0.5"/>
                        <rect className={"cursor-pointer transition-all rounded-xl"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding(3)
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlatsFloor[2] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                setSelectedBuilding(3)
                            }
                        }}
                              fill={hovered && hoveredBuilding === 3 ? numberOfFlatsFloor[2] === 0 ? "red" : "black" : selectedBuilding === 3 ? "#a3cb47" : "transparent"}
                              x={isMobile ? "17" : "655"} y="338" width="894" height="92" fillOpacity="0.5"/>
                        <rect className={"cursor-pointer transition-all rounded-xl"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding(2)
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlatsFloor[1] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                setSelectedBuilding(2)
                            }
                        }}
                              fill={hovered && hoveredBuilding === 2 ? numberOfFlatsFloor[1] === 0 ? "red" : "black" : selectedBuilding === 2 ? "#a3cb47" : "transparent"}
                              x={isMobile ? "17" : "655"} y="430" width="894" height="94" fillOpacity="0.5"/>
                        <rect className={"cursor-pointer transition-all rounded-xl"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding(1)
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlatsFloor[0] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                setSelectedBuilding(1)
                            }
                        }}
                              fill={hovered && hoveredBuilding === 1 ? numberOfFlatsFloor[0] === 0 ? "red" : "black" : selectedBuilding === 1 ? "#a3cb47" : "transparent"}
                              x={isMobile ? "17" : "655"} y="524" width="894" height="87" fillOpacity="0.5"/>
                    </svg>
                    <div className={"absolute lg:top-[4vw] top-[15vw] right-3 lg:right-[14vw]"}>
                        {numberOfFlatsFloor !== undefined &&
                            <div
                                className={"flex absolute lg:relative right-0 lg:right-6 flex-col-reverse gap-[4.2vw] lg:gap-[1.7vw] mt-[2.2vw]"}>
                                {numberOfFlatsFloor.map((number: number, index: number) => (
                                    <p key={index}
                                       className={number > 0 ? hovered && hoveredBuilding === (index + 1) ? "bg-[#B4CC7A] border-2 border-white lg:text-[1.3vw] text-[3vw] font-bold rounded-xl shadow-lg opacity-100 transition-all py-[.2vw] px-0 text-center w-[15vw] lg:w-auto lg:px-[1.3vw]" : "bg-[#B4CC7A] border-2 border-transparent lg:text-[1.3vw] text-[3vw] transition-all rounded-xl shadow-lg opacity-60 py-[.2vw] text-center w-[15vw] lg:w-auto px-0 lg:px-[1.3vw]" : hovered && hoveredBuilding === (index + 1) ? "bg-transparent border border-red-600 text-center text-[1.3vw] py-[.4vw] font-bold text-red-600 mb-0.5 transition-all rounded-lg px-[1.5vw]" : "bg-transparent border border-black text-center font-light lg:text-[1.3vw] text-[3vw] py-[.4vw] opacity-60 text-black transition-all mb-0.5 rounded-lg w-[15vw] lg:w-auto px-0 lg:px-[1.3vw]"}>{number === 1 ? number + " voľný" : number > 1 && number < 5 ? number + " voľné" : number + " voľných"}</p>
                                ))}
                            </div>
                        }
                    </div>
                    {!isMobile && <div className={"absolute top-[4vw] left-[14vw]"}>
                        {numberOfFlatsFloor !== undefined &&
                            <div
                                className={"flex absolute bottom-1 lg:relative flex-col-reverse gap-[.9vw] mt-[1.8vw]"}>
                                {numberOfFlatsFloor.map((number: number, index: number) => (
                                    <div key={index}
                                         className={hovered && hoveredBuilding === (index + 1) ? "flex items-center opacity-100 transition-all justify-between gap-5" : "flex items-center transition-all opacity-10 justify-between gap-5"}>
                                        <p className={"text-[2.5vw] font-bold"}>{index + 1}</p>
                                        <div className={"flex flex-col items-start"}>
                                            <small className={"text-xs"}>Nadzemné</small>
                                            <p className={"font-bold"}>podlažie</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        }
                    </div>}
                    <h2 className={"flex items-center lg:-mt-10 justify-center gap-3"}>BUDOVA <span
                        className={"text-[7vw] lg:text-[3vw] font-bold"}>{building}</span></h2>
                </> : <></>}
        </div>
    )
}