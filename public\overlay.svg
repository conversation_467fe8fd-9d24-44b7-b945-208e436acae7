<svg width="3121" height="757" viewBox="0 0 3121 757" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M1371.5 112C1371.33 110.5 1371.1 106.3 1371.5 101.5L1441.5 97V80.5L1510.5 77.5L1533.5 90.5L1599.5 88L1617 97.5L1654 96L1688 115.5V134L1712.5 147V162H1728.5L1735 166.5L1734 300.5L1712 301.5V340L1328.5 363.5L1295.5 338L1296.5 140.5L1316.5 139L1317.5 114.5L1371.5 112Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M1809 45.5V31L1822 30V26L1866.5 24.5L1874 30H1880V17H1883.5V31.5L1901 39.5L1956 36.5L1977 46L2005 44L2046 63.5V80.5L2070 92V265.5L1730 290.5L1680.5 253.5V83.5L1701.5 83V61L1750 59V48L1809 45.5Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M2129.5 26.5V13L2189.5 9L2221 21L2272 18.5L2296 27.5L2319 26.5L2368.5 44V46.5H2366V63L2387.5 70V85.5L2404.5 84L2411 88L2408 211H2383.5V244.5L2069.5 265L2007.5 230V64H2032.5V42.5L2076.5 40V29.5L2129.5 26.5Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M2686.5 31.5V14.5L2649 3L2598.5 6.5L2576 0L2489 3V5L2458.5 6V25.5L2434 26.5V41L2427.5 39H2412V41V177L2621 265L2752 250.5L2761 85L2730 74.5V59.5H2732V56.5L2707 48.5L2707.5 37.5L2686.5 31.5Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M246 242L251.5 207H254.5H267L270.5 179L334 176.5V163L425 160V141L508.5 138L513.5 154L601 149.5L605 160L663 157.5L673 179H680L694 215L697 231.5H715.5L723.5 381.5H703.5L706.5 425L241.5 462.5L227 272.5V261.5H248.5L246 242Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <path d="M951 124V109L1027 104L1040.5 117.5L1118.5 116L1129 125.5L1174.5 123L1200 145.5V156.5L1222 177.5V194L1241 192.5L1244.5 197.5L1246.5 225.5V293H1239V301C1239 301.8 1246.33 306.667 1250 309L1248.5 337H1225L1227 378.5L818 404L796.5 366L794.5 279L791 272L793 231L791 227.5V223.5L793 221V169H814.5V145L871 141V128.5L951 124Z"
          fill="#D9D9D9" fill-opacity="0.5"/>
    <g filter="url(#filter0_bd_1057_4048)">
        <path d="M80.5 671.5V527.5L2893 280L3028 287L3041 324.5L2960 336L2942.5 363L2900 396L2594 423L2515.5 382L2431 453.5V491L1138 615.5L80.5 671.5Z"
              fill="#D9D9D9" fill-opacity="0.01" shape-rendering="crispEdges"/>
    </g>
    <g filter="url(#filter1_d_1057_4048)">
        <rect x="325" y="506.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 325 506.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter2_d_1057_4048)">
        <rect x="881" y="457.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 881 457.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter3_d_1057_4048)">
        <rect x="1366" y="416.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 1366 416.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter4_d_1057_4048)">
        <rect x="1763" y="382.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 1763 382.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter5_d_1057_4048)">
        <rect x="2115" y="353.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 2115 353.293)"
              fill="#D1D1B6"/>
    </g>
    <g filter="url(#filter6_d_1057_4048)">
        <rect x="2470" y="323.293" width="332" height="57" rx="28.5" transform="rotate(-4.36946 2470 323.293)"
              fill="#D1D1B6"/>
    </g>
    <defs>
        <filter id="filter0_bd_1057_4048" x="0.5" y="205" width="3120.5" height="551.5" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feGaussianBlur in="BackgroundImageFix" stdDeviation="3.5"/>
            <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1057_4048"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="5"/>
            <feGaussianBlur stdDeviation="40"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="effect1_backgroundBlur_1057_4048" result="effect2_dropShadow_1057_4048"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_1057_4048" result="shape"/>
        </filter>
        <filter id="filter1_d_1057_4048" x="307.088" y="467.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1057_4048"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1057_4048" result="shape"/>
        </filter>
        <filter id="filter2_d_1057_4048" x="863.088" y="418.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1057_4048"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1057_4048" result="shape"/>
        </filter>
        <filter id="filter3_d_1057_4048" x="1348.09" y="377.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1057_4048"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1057_4048" result="shape"/>
        </filter>
        <filter id="filter4_d_1057_4048" x="1745.09" y="343.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1057_4048"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1057_4048" result="shape"/>
        </filter>
        <filter id="filter5_d_1057_4048" x="2097.09" y="314.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1057_4048"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1057_4048" result="shape"/>
        </filter>
        <filter id="filter6_d_1057_4048" x="2452.09" y="284.086" width="371.203" height="117.953"
                filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="10"/>
            <feComposite in2="hardAlpha" operator="out"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1057_4048"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1057_4048" result="shape"/>
        </filter>
    </defs>
</svg>
