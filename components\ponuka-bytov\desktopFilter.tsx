import Link from "next/link";
import {useEffect, useState} from "react";
import Router from "next/router";
import {Tooltip} from "flowbite-react";

export default function TableDesktop({
                                         filteredFlats,
                                         handleChangeToSubmit,
                                         orderingVymera,
                                         is3D,
                                         orderingCena,
                                         room,
                                         filteredFlatsCount
                                     }) {
    const [redir, setRedirect] = useState()
    const [ordered, setOrdered] = useState(orderingVymera)
    const [orderingEnabled, setOrderingEnabled] = useState(orderingVymera ? true : false)
    const [orderCena, setOrderCena] = useState(orderingCena)
    const [orderCenaEnabled, setOrderCenaEnabled] = useState(orderingCena ? true : false);
    useEffect(() => {
        if (redir) {
            Router.replace("/ponuka-bytov/" + redir["rooms"] + "-izbove/" + redir["id"])
        }
        if (orderingVymera === false) {
            setOrderingEnabled(false)
        }
        if (orderingCena === false) {
            setOrderCenaEnabled(false)
        }
    }, [redir, orderingVymera, orderingCena])

    return (
        <>
            {filteredFlats && filteredFlats.length > 0 ?
                <>
                    <div className={"pt-5 pb-6"}><strong>Počet výsledkov: </strong>{filteredFlatsCount}</div>
                    <table className="w-full">
                        <thead>
                        <tr className={"w-full border-b-2 pb-0 border-black"}>
                            <th scope="col" className="px-6 pb-0">DOM</th>
                            <th scope="col" className="px-6 pb-0">PODLAŽIE</th>
                            <th scope="col" className="px-6 pb-0">ČÍSLO BYTU</th>
                            <th scope="col" className="px-6 pb-0">POČET IZIEB</th>
                            <th scope="col" className="px-6 pb-0">VÝMERA INTERIÉRU</th>
                            <th scope="col" className="px-6 pb-0">VÝMERA EXTERIÉRU</th>
                            {is3D ? <th scope="col" className="px-6 pb-0">CELKOVÁ VÝMERA</th> :
                                <th aria-label={"vymera"} scope="col"
                                    className="px-6 cursor-pointer flex gap-2 underline items-center justify-center pb-0"
                                    onClick={(e) => {
                                        handleChangeToSubmit(e)
                                        setOrdered(!ordered)
                                        setOrderingEnabled(true)
                                    }}>CELKOVÁ VÝMERA
                                    <input id={"vymera"} className={"hidden"} type={"text"}
                                           value={ordered ? "asc" : "desc"}
                                           name={"vymeraOrder"}/>
                                    {orderingEnabled ? ordered ?
                                            <svg
                                                className="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                                      strokeWidth="2" d="m8 7 4 4 4-4m-8 6 4 4 4-4"/>
                                            </svg> :
                                            <svg className="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                                      strokeWidth="2" d="m16 17-4-4-4 4m8-6-4-4-4 4"/>
                                            </svg>
                                        : <svg className="w-5 h-5 text-gray-800 dark:text-white" aria-hidden="true"
                                               xmlns="http://www.w3.org/2000/svg" fill="currentColor"
                                               viewBox="0 0 24 24">
                                            <path fillRule="evenodd"
                                                  d="M12.8 3.4a1 1 0 0 0-1.6 0l-4 6A1 1 0 0 0 8 11h8a1 1 0 0 0 .8-1.6l-4-6Zm-1.6 17.2a1 1 0 0 0 1.6 0l4-6A1 1 0 0 0 16 13H8a1 1 0 0 0-.8 1.6l4 6Z"
                                                  clipRule="evenodd"/>
                                        </svg>}
                                </th>
                            }
                            {is3D ? <th scope="col" className="px-6 pb-0">CENA S DPH</th> :
                                <th scope="col" aria-label={"cena"} className="px-6 pb-0 cursor-pointer"
                                    onClick={(e) => {
                                        handleChangeToSubmit(e)
                                        setOrderCena(!orderCena)
                                        setOrderCenaEnabled(true)
                                    }}>
                                    <div aria-label={"cena"}
                                         className={"flex gap-2 underline items-center justify-center"}>CENA S
                                        DPH <input
                                            className={"hidden"} type={"text"} id={"price"}
                                            value={orderCena ? "asc" : "desc"}
                                            name={"cenaOrder"}/>
                                        {orderCenaEnabled ? orderCena ?
                                                <svg
                                                    className="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true"
                                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" strokeLinecap="round"
                                                          strokeLinejoin="round"
                                                          strokeWidth="2" d="m8 7 4 4 4-4m-8 6 4 4 4-4"/>
                                                </svg> :
                                                <svg className="w-6 h-6 text-gray-800 dark:text-white"
                                                     aria-hidden="true"
                                                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" strokeLinecap="round"
                                                          strokeLinejoin="round"
                                                          strokeWidth="2" d="m16 17-4-4-4 4m8-6-4-4-4 4"/>
                                                </svg>
                                            : <svg className="w-5 h-5 text-gray-800 dark:text-white"
                                                   aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                                   fill="currentColor" viewBox="0 0 24 24">
                                                <path fillRule="evenodd"
                                                      d="M12.8 3.4a1 1 0 0 0-1.6 0l-4 6A1 1 0 0 0 8 11h8a1 1 0 0 0 .8-1.6l-4-6Zm-1.6 17.2a1 1 0 0 0 1.6 0l4-6A1 1 0 0 0 16 13H8a1 1 0 0 0-.8 1.6l4 6Z"
                                                      clipRule="evenodd"/>
                                            </svg>
                                        }</div>
                                </th>}
                            <th scope="col" className="px-6 pb-0">STAV</th>
                        </tr>
                        </thead>
                        <tbody>
                        {filteredFlats.map((flat: any, index: number) => {
                                if(flat["rooms"] === undefined) return null;
                                return (
                                    <tr
                                        onClick={() => {
                                            if (flat.dostupnost !== 0)
                                                setRedirect(flat)
                                        }}
                                        key={index} id={index.toString()}
                                        className={"border-b-2 hover:bg-gray-300 hover:cursor-pointer border-gray-400"}>
                                        <th className={"py-4"}>
                                            <span className={"text-4xl"}>{flat["budova"]}</span>
                                        </th>
                                        <th>
                                            <span className={"text-4xl"}>{flat["poschodie"]}</span>
                                        </th>
                                        <th>
                                            <div className={"flex flex-col gap-1 items-center justify-center"}>
                                        <span
                                            className={"bg-black text-2xl text-white px-4 py-1 rounded-full"}>{flat["flatNumber"]}</span>
                                            </div>
                                        </th>
                                        <th className={"relative"}>
                                            <span
                                                className={"text-4xl"}>{flat["rooms"] > room ? room : flat["rooms"]}i</span>

                                            {(flat["rooms"] > room) &&
                                                <span className={"text-4xl"}>/{flat["rooms"] > room ? flat["canRooms"] + 1 : flat["canRooms"]}i</span>
                                            }
                                            {(flat["canRooms"] < room) &&
                                                <span className={"text-4xl"}>/{flat["canRooms"]}i</span>
                                            }
                                        </th>
                                        <th>
                                            <span className={"text-2xl font-light"}>{flat["plocha_interier"]}m²</span>
                                        </th>
                                        <th className={"py-4"}>
                                            {flat["terasa"] > 0 &&
                                                <div className={"flex justify-between mb-1 items-center"}>
                                                    <span className={"bg-[#683107] py-1 px-3 text-white"}>terasa</span>
                                                    <span className={"text-2xl font-light"}>{flat["terasa"]} m²</span>
                                                </div>}
                                            {flat["balkon"] > 0 &&
                                                <div className={"flex justify-between mb-1 items-center"}>
                                                    <span className={"bg-[#94B34B] p-1 px-3 text-white"}>balkón</span>
                                                    <span className={"text-2xl font-light"}>{flat["balkon"]} m²</span>
                                                </div>}
                                            {flat["zahrada"] > 0 &&
                                                <div className={"flex justify-between mb-1 items-center"}>
                                                    <span className={"bg-[#7D904F] p-1 px-3 text-white"}>záhrada</span>
                                                    <span className={"text-2xl font-light"}>{flat["zahrada"]} m²</span>
                                                </div>}
                                        </th>
                                        <th>
                                            <span className={"text-2xl font-bold"}>{flat["vymera"]} m²</span>
                                        </th>
                                        <th>
                                            {flat["dostupnost"] !== 0 && <span
                                                className={"text-2xl font-light"}>{flat["price"]?.toString().replace(/.{3}/g, '$& ')} €</span>}
                                        </th>
                                        <th className={"py-4"}>
                                            <div className={"flex flex-col gap-4 justify-center items-center"}>
                                        <span
                                            className={flat.dostupnost === 2 ? "bg-[#FFAB48] text-black w-full text-xl py-1 px-4 rounded-full font-light" : flat["dostupnost"] === 0 ? "bg-red-500 text-white w-full text-xl py-1 px-4 rounded-full font-light" : flat["dostupnost"] === 4 ? "bg-gray-400 text-white w-full text-xl py-1 px-4 rounded-full font-light" : "bg-[#B4CC7A] text-black w-full text-xl py-1 px-4 rounded-full font-light"}>{flat["dostupnost"] === 1 ? "voľný" : flat["dostupnost"] === 2 ? "rezervovaný" : flat["dostupnost"] === 4 ? "mimo predaja" : "predaný"}</span>
                                                <Link style={{
                                                    pointerEvents: (flat.dostupnost === 0) ? "none" : "auto",
                                                }}
                                                      href={flat.dostupnost !== 0 ? "/ponuka-bytov/" + flat["rooms"] + "-izbove/" + flat["id"] : "#"}
                                                      className={"bg-[#657492] w-full text-white shadow-md text-xl py-1 px-4 rounded-full font-light"}>pôdorys
                                                </Link>
                                            </div>
                                        </th>
                                    </tr>
                                );
                            }
                        )}
                        </tbody>
                    </table>
                </> : <div role="alert"
                           className="p-4 mb-4 text-sm text-blue-800 rounded-lg w-full bg-blue-50 dark:bg-gray-800 dark:text-blue-400 w-100"><span
                    className="font-medium">Žiadne výsledky!</span> Vaším kritériám nevyhovuje žiaden byt... Skúste
                    to inak</div>}</>
    )
}
