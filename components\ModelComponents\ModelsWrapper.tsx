//@ts-nocheck
import {GroundModel} from "./3D/GroundModel";
import {BuildingAModel} from "./3D/BuildingAModel";
import {BuildingModel} from "./3D/BuildingModel";
import buildingConfigurations from "../../utils/buildingConfig";
import {Val} from "./3D/val";
import {Okolie} from "./3D/okolie";
import {Suspense} from "react";


export default function ModelsWrapper({spaceEnabled}) {
    return (<Suspense>
        {spaceEnabled &&
            <Okolie/>
        }
        <GroundModel/>
        <Val/>
        <BuildingAModel/>
        {buildingConfigurations.map((build, index) => (
            <BuildingModel key={index} number={index} rotation={build.rotation} position={build.position}/>
        ))}

    </Suspense>)
}