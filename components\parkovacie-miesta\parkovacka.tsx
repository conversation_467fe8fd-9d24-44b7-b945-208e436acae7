import {useEffect, useState} from "react";
import {Badge} from "flowbite-react";
import Router from "next/router";

export default function Parkovacka({getter, isFlatDetail, flatSelected}) {
    const [hovered, setHovered] = useState(false);
    const [selectedFlat, setSelectedFlat] = useState<string>(flatSelected ? flatSelected : "")
    const [hoveredSectionInfo, setHoveredSectionInfo] = useState("")
    const [hoveredSection, setHoveredSection] = useState(null)

    const handleMouseEnter = (section: number): void => {
        setHovered(true)
        // @ts-ignore
        setHoveredSectionInfo(<div className={"flex gap-4 items-center"}><Badge className={"p-1"}
                                                                                color="success">SEKCIA {section}</Badge><span> {section === 5 ? "Vonkaj<PERSON>ie parkovanie" : section === 6 ? "<PERSON><PERSON><PERSON><PERSON><PERSON> parkovanie (kry<PERSON>)" : section === 7 ? "Vonka<PERSON><PERSON><PERSON> parkovanie" : section === 8 ? "Hosťovské parkovanie (nepredajné)" : "Podzemné parkovanie"}</span>
        </div>)
        setHoveredSection(section)
    }

    useEffect(() => {
        if (selectedFlat !== "") {
            if (!isFlatDetail) {
                getter(selectedFlat);
            }
        }
    }, [selectedFlat])

    return (
        <>
            <div className={"inline-flex justify-center w-full"}>
                {hovered ?
                    <p className={"bg-purple-950 shadow-md transition-all my-2 p-2 px-3 rounded-xl text-white"}>{hoveredSectionInfo}</p>
                    :
                    <p className={"bg-purple-950 shadow-md transition-all my-2 p-2 px-3 rounded-xl text-white"}>Informácie
                        o sekcii</p>
                }
            </div>
            <div className={"relative flex justify-center"}>
                <svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" width="700"
                     viewBox="0 0 2093.3147 529.5594" className={isFlatDetail && "pt-10 px-2 w-full"}>
                    <g id="Layer_2-2" data-name="Layer 2">
                        <g id="PLOCHA">
                            <polygon
                                points="2 450.9188 47.8514 450.9188 47.8514 276.2468 6.3145 276.2468 2 284.9136 2 450.9188"
                                fill="none" stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                            <polygon
                                points="91.5194 450.9188 137.3708 450.9188 137.2791 319.921 91.5194 319.8602 91.5194 450.9188"
                                fill="none" stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                            <polygon
                                points="203.5526 182.1552 137.2835 319.921 96.0532 300.0411 162.3129 162.3149 203.5526 182.1552"
                                fill="none" stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                            <polygon
                                points="61.3091 267.9772 20.1 247.821 58.4352 169.4453 99.664 189.5088 61.3091 267.9772"
                                fill="none" stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                            <rect x="500.8342" y="50.1789" width="900.4805" height="20.6674" fill="none"
                                  stroke="#b6bfd1"
                                  strokeMiterlimit="10" strokeWidth="4"/>
                            <rect x="1420.8342" y="25.1789" width="550.4805" height="45.6674" fill="none"
                                  stroke="#b6bfd1"
                                  strokeMiterlimit="10" strokeWidth="4"/>
                            <rect x="2042.8342" y="126.1789" width="48.4805" height="263.6674" fill="none"
                                  stroke="#b6bfd1"
                                  strokeMiterlimit="10" strokeWidth="4"/>
                            <g id="Layer_79" data-name="Layer 79">
                                <g>
                                    <path d="M288.3594,253.7715c-2.5346,5.3213-4.2961,9.0291-4.2961,9.0291l8.9839,4.392"
                                          fill="none"
                                          stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <polyline points="299.6936 270.442 314.528 277.6942 355.165 318.3311" fill="none"
                                              stroke="#b6bfd1"
                                              strokeDasharray="0 0 0 0 0 0 18.4954 7.3982" strokeMiterlimit="10"
                                              strokeWidth="4"/>
                                    <polyline points="357.7806 320.9467 364.8517 328.0178 364.8517 338.0178" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <polyline points="364.8517 349.386 364.8517 359.386 374.8517 359.3842" fill="none"
                                              stroke="#b6bfd1"
                                              strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="382.7888" y1="359.3828" x2="712.1787" y2="359.3226" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 19.8428 7.9371" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="716.1472 359.3219 726.1472 359.3201 726.1472 349.3201" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="726.1472" y1="340.7541" x2="726.1472" y2="195.1318" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 21.415 8.566" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="726.1472 190.8488 726.1472 180.8488 716.1472 180.8497" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="707.934" y1="180.8504" x2="338.3382" y2="180.8817" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 20.5331 8.2132" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <path d="M334.2316,180.882l-10,.0009s-1.8795,3.7074-4.5143,8.9189" fill="none"
                                          stroke="#b6bfd1"
                                          strokeMiterlimit="10" strokeWidth="4"/>
                                    <path
                                        d="M315.7014,197.7531c-4.1428,8.212-8.711,17.3001-11.0018,21.9666-3.0595,6.2324-9.5366,19.7792-14.4233,30.0292"
                                        fill="none" stroke="#b6bfd1" strokeDasharray="0 0 0 0 0 0 22.2626 8.905"
                                        strokeMiterlimit="10" strokeWidth="4"/>
                                </g>
                                <g>
                                    <polyline points="973.9903 349.386 973.9903 359.386 963.9903 359.386" fill="none"
                                              stroke="#b6bfd1"
                                              strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="956.1377" y1="359.386" x2="740.1896" y2="359.386" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 19.6316 7.8527" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="736.2633 359.386 726.2633 359.386 726.2633 349.386" fill="none"
                                              stroke="#b6bfd1"
                                              strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="726.2633" y1="340.8165" x2="726.2633" y2="195.1336" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 21.424 8.5696" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="726.2633 190.8488 726.2633 180.8488 736.2633 180.8488" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="744.1159" y1="180.8488" x2="960.064" y2="180.8488" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 19.6316 7.8527" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="963.9903 180.8488 973.9903 180.8488 973.9903 190.8488" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="973.9903" y1="199.4184" x2="973.9903" y2="345.1013" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 21.424 8.5696" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                </g>
                                <g>
                                    <polyline points="1245.3009 190.8318 1245.3009 180.8318 1235.3009 180.8318"
                                              fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="1227.5684" y1="180.8318" x2="987.8567" y2="180.8318" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 19.3316 7.7326" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="983.9903 180.8318 973.9903 180.8318 973.9903 190.8318" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="973.9903" y1="199.4014" x2="973.9903" y2="345.0843" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 21.424 8.5696" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="973.9903 349.369 973.9903 359.369 983.9903 359.369" fill="none"
                                              stroke="#b6bfd1"
                                              strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="991.723" y1="359.369" x2="1231.4346" y2="359.369" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 19.3316 7.7326" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="1235.3009 359.369 1245.3009 359.369 1245.3009 349.369" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="1245.3009" y1="340.7995" x2="1245.3009" y2="195.1166" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 21.424 8.5696" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                </g>
                                <g>
                                    <polyline points="1740.4067 349.369 1740.4067 359.369 1730.4067 359.369" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="1722.5538" y1="359.369" x2="1259.2274" y2="359.369" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 19.6325 7.853" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="1255.3009 359.369 1245.3009 359.369 1245.3009 349.369" fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="1245.3009" y1="340.8013" x2="1245.3009" y2="195.1497" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 21.4194 8.5677" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="1245.3009 190.8658 1245.3009 180.8658 1255.3009 180.8658"
                                              fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="1263.1539" y1="180.8658" x2="1726.4802" y2="180.8658" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 19.6325 7.853" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                    <polyline points="1730.4067 180.8658 1740.4067 180.8658 1740.4067 190.8658"
                                              fill="none"
                                              stroke="#b6bfd1" strokeMiterlimit="10" strokeWidth="4"/>
                                    <line x1="1740.4067" y1="199.4336" x2="1740.4067" y2="345.0852" fill="none"
                                          stroke="#b6bfd1"
                                          strokeDasharray="0 0 0 0 0 0 21.4194 8.5677" strokeMiterlimit="10"
                                          strokeWidth="4"/>
                                </g>
                            </g>
                        </g>
                        <g id={"rectangle"}>
                            {selectedFlat === "1" || selectedFlat === "2" || selectedFlat === "3" || selectedFlat === "4" ?
                                <polygon
                                    points="1766.6688 270.1174 1841.6688 326.222 1841.6688 214.0129 1766.6688 270.1174"
                                    strokeWidth="0"/> : <></>}
                            <path id={"section2"} className={"plochaP"} onMouseEnter={() => handleMouseEnter(1)}
                                  onMouseLeave={() => setHovered(false)} onClick={() => {
                                if (isFlatDetail) {
                                    Router.replace("/parkovacie-miesta?section=1").then();
                                } else {
                                    setSelectedFlat("1");
                                }
                            }}
                                  d="M284.1794,262.7836l30.4647,14.8935,50.3236,50.3236v31.3683l361.2956-.066v-178.4713l-401.9156.034s-14.7428,29.081-19.5321,38.8369c-5.2626,10.72-20.6362,43.0809-20.6362,43.0809Z"
                                  fill={selectedFlat === "1" ? "black" : hovered ? selectedFlat === "1" || hoveredSection === 1 ? "#a4b1d3" : "transparent" : "transparent"}/>
                            <rect id={"section1"} className={"plochaP"} onMouseEnter={() => handleMouseEnter(2)}
                                  onMouseLeave={() => setHovered(false)} onClick={() => {
                                if (isFlatDetail) {
                                    Router.replace("/parkovacie-miesta?section=2").then();
                                } else {
                                    setSelectedFlat("2");
                                }
                            }}
                                  x="726.2633" y="180.8488" width="247.7271" height="178.5372"
                                  fill={selectedFlat === "2" ? "black" : hovered ? selectedFlat === "2" || hoveredSection === 2 ? "#a4b1d3" : "transparent" : "transparent"}/>
                            <rect id={"section3"} className={"plochaP"} onMouseEnter={() => handleMouseEnter(3)}
                                  onMouseLeave={() => setHovered(false)}
                                  onClick={() => {
                                      if (isFlatDetail) {
                                          Router.replace("/parkovacie-miesta?section=3").then();
                                      } else {
                                          setSelectedFlat("3");
                                      }
                                  }} x="973.9904" y="180.8318" width="271.3106"
                                  height="178.5372"
                                  fill={selectedFlat === "3" ? "black" : hovered ? selectedFlat === "3" || hoveredSection === 3 ? "#a4b1d3" : "transparent" : "transparent"}/>
                            <rect id={"section4"} className={"plochaP"} onMouseEnter={() => handleMouseEnter(4)}
                                  onMouseLeave={() => setHovered(false)}
                                  onClick={() => {
                                      if (isFlatDetail) {
                                          Router.replace("/parkovacie-miesta?section=4").then();
                                      } else {
                                          setSelectedFlat("4");
                                      }
                                  }} x="1245.3009" y="180.8658" width="495.1058"
                                  height="178.5032"
                                  fill={selectedFlat === "4" ? "black" : hovered ? selectedFlat === "4" || hoveredSection === 4 ? "#a4b1d3" : "transparent" : "transparent"}/>
                            <g id={"section5"} className={"plochaP"} onMouseEnter={() => handleMouseEnter(5)}
                               onMouseLeave={() => setHovered(false)} onClick={() => {
                                if (isFlatDetail) {
                                    Router.replace("/parkovacie-miesta?section=V").then();
                                } else {
                                    setSelectedFlat("V");
                                }
                            }}>
                                {selectedFlat === "V" &&
                                    <polygon
                                        points="156.5225 143.3894 239.0669 99.1271 137.3708 51.7055 156.5225 143.3894"/>}
                                <polygon
                                    fill={selectedFlat === "V" ? "black" : hovered ? selectedFlat === "V" || hoveredSection === 5 ? "#a4b1d3" : "transparent" : "transparent"}
                                    points="2 450.9188 47.8514 450.9188 47.8514 276.2468 6.3145 276.2468 2 284.9136 2 450.9188"/>
                                <polygon
                                    fill={selectedFlat === "V" ? "black" : hovered ? selectedFlat === "V" || hoveredSection === 5 ? "#a4b1d3" : "transparent" : "transparent"}
                                    points="91.5194 450.9188 137.3708 450.9188 137.2791 319.921 91.5194 319.8602 91.5194 450.9188"/>
                                <polygon
                                    fill={selectedFlat === "V" ? "black" : hovered ? selectedFlat === "V" || hoveredSection === 5 ? "#a4b1d3" : "transparent" : "transparent"}
                                    points="203.5526 182.1552 137.2835 319.921 96.0532 300.0411 162.3129 162.3149 203.5526 182.1552"/>
                                <polygon
                                    fill={selectedFlat === "V" ? "black" : hovered ? selectedFlat === "V" || hoveredSection === 5 ? "#a4b1d3" : "transparent" : "transparent"}
                                    points="61.3091 267.9772 20.1 247.821 58.4352 169.4453 99.664 189.5088 61.3091 267.9772"/>
                            </g>
                            {selectedFlat === "V2" &&
                                <polygon
                                    points="2038.1227 258.0127 1963.1227 314.1172 1963.1227 201.9081 2038.1227 258.0127"/>}
                            <rect x="2042.8342" y="126.1789" width="48.4805" height="263.6674" id={"section6"}
                                  className={"plochaP"} onMouseEnter={() => handleMouseEnter(6)}
                                  onMouseLeave={() => setHovered(false)} onClick={() => {
                                if (isFlatDetail) {
                                    Router.replace("/parkovacie-miesta?section=V2").then();
                                } else {
                                    setSelectedFlat("V2");
                                }
                            }}
                                  fill={selectedFlat === "V2" ? "black" : hovered ? selectedFlat === "V2" || hoveredSection === 6 ? "#a4b1d3" : "transparent" : "transparent"}/>
                            {selectedFlat === "7" &&
                                <path xmlns="http://www.w3.org/2000/svg" d="M1699 71L1734.51 125.75H1663.49L1699 71Z"
                                      fill="black"/>}
                            <rect x="1420.8342" y="25.1789" width="550.4805" height="45.6674" id={"section7"}
                                  className={"plochaP"} onMouseEnter={() => handleMouseEnter(7)}
                                  onMouseLeave={() => setHovered(false)} onClick={() => {
                                if (isFlatDetail) {
                                    Router.replace("/parkovacie-miesta?section=7").then();
                                } else {
                                    setSelectedFlat("7");
                                }
                            }}
                                  fill={selectedFlat === "7" ? "black" : hovered ? selectedFlat === "7" || hoveredSection === 7 ? "#a4b1d3" : "transparent" : "transparent"}/>

                            <rect x="500.8342" y="50.1789" width="900.4805" height="20.6674" id={"section8"}
                                  className={"plochaP"} onMouseEnter={() => handleMouseEnter(8)}
                                  onMouseLeave={() => setHovered(false)}
                                  fill={selectedFlat === "8" ? "black" : hovered ? selectedFlat === "8" || hoveredSection === 8 ? "#a4b1d3" : "transparent" : "transparent"}/>
                        </g>
                        <g id="budovy">
                            <g>
                                <polygon className={"plochaP"} onMouseEnter={() => handleMouseEnter(1)}
                                         onMouseLeave={() => setHovered(false)} onClick={() => {
                                    if (isFlatDetail) {
                                        Router.replace("/parkovacie-miesta?section=1").then();
                                    } else {
                                        setSelectedFlat("1");
                                    }
                                }}
                                         points="612.5543 307.0981 612.5543 295.0457 599.4536 295.0457 599.4536 271.258 569.0605 271.2577 569.0605 295.0454 569.0605 307.0978 476.5718 307.0981 476.5718 396.5301 705.7412 396.5301 705.7412 307.0981 612.5543 307.0981"
                                         fill={hovered && selectedFlat === "1" ? "#a4b1d3" : "#b6bfd1"} opacity=".5"
                                         strokeWidth="0"/>
                                <polygon className={"plochaP"} onMouseEnter={() => handleMouseEnter(2)}
                                         onMouseLeave={() => setHovered(false)} onClick={() => {
                                    if (isFlatDetail) {
                                        Router.replace("/parkovacie-miesta?section=2").then();
                                    } else {
                                        setSelectedFlat("2");
                                    }
                                }}
                                         points="871.1297 307.0981 871.1297 295.0457 858.029 295.0457 858.029 271.258 827.6359 271.2577 827.6359 295.0454 827.6359 307.0978 735.1472 307.0981 735.1472 396.5301 964.3166 396.5301 964.3166 307.0981 871.1297 307.0981"
                                         fill={hovered && selectedFlat === "2" ? "#a4b1d3" : "#b6bfd1"} opacity=".5"
                                         strokeWidth="0"/>
                                <polygon className={"plochaP"} onMouseEnter={() => handleMouseEnter(3)}
                                         onMouseLeave={() => setHovered(false)} onClick={() => {
                                    if (isFlatDetail) {
                                        Router.replace("/parkovacie-miesta?section=3").then();
                                    } else {
                                        setSelectedFlat("3");
                                    }
                                }}
                                         points="1129.7052 307.0981 1129.7052 295.0457 1116.6044 295.0457 1116.6044 271.258 1086.2113 271.2577 1086.2113 295.0454 1086.2113 307.0978 993.7227 307.0981 993.7227 396.5301 1222.8921 396.5301 1222.8921 307.0981 1129.7052 307.0981"
                                         fill={hovered && selectedFlat === "3" ? "#a4b1d3" : "#b6bfd1"} opacity=".5"
                                         strokeWidth="0"/>
                                <polygon className={"plochaP"} onMouseEnter={() => handleMouseEnter(4)}
                                         onMouseLeave={() => setHovered(false)} onClick={() => {
                                    if (isFlatDetail) {
                                        Router.replace("/parkovacie-miesta?section=4").then();
                                    } else {
                                        setSelectedFlat("4");
                                    }
                                }}
                                         points="1415.871 210.1825 1415.871 198.1302 1402.7701 198.1302 1402.7701 174.3425 1372.3771 174.3422 1372.3771 198.1299 1372.3771 210.1822 1279.8885 210.1825 1279.8885 299.6146 1509.0579 299.6146 1509.0579 210.1825 1415.871 210.1825"
                                         fill={hovered && selectedFlat === "4" ? "#a4b1d3" : "#b6bfd1"} opacity=".5"
                                         strokeWidth="0"/>
                                <polygon className={"plochaP"} onMouseEnter={() => handleMouseEnter(4)}
                                         onMouseLeave={() => setHovered(false)} onClick={() => {
                                    if (isFlatDetail) {
                                        Router.replace("/parkovacie-miesta?section=4").then();
                                    } else {
                                        setSelectedFlat("4");
                                    }
                                }}
                                         points="1603.5514 210.1825 1603.5514 198.1302 1616.6521 198.1302 1616.6521 174.3425 1647.0452 174.3422 1647.0452 198.1299 1647.0452 210.1822 1739.5339 210.1825 1739.5339 299.6146 1510.3645 299.6146 1510.3645 210.1825 1603.5514 210.1825"
                                         fill={hovered && selectedFlat === "4" ? "#a4b1d3" : "#b6bfd1"} opacity=".5"
                                         strokeWidth="0"/>
                                <polygon
                                    points="1960.5632 307.5463 1960.5632 268.8998 1948.5109 268.8998 1948.5109 164.5769 1859.0789 164.5769 1859.0789 416.0175 1948.5109 416.0175 1948.5109 339.8582 1975.1483 339.8582 1975.1483 307.5463 1960.5632 307.5463"
                                    fill="#b6bfd1" opacity=".5" strokeWidth="0"/>
                                <path
                                    d="M568.3758,527.0594v-87.5h48.1499v17h-29.9248v23.625h29.9248v16.625h-29.9248v30.25h-18.2251Z"
                                    fill="#fff" strokeWidth="0"/>
                                <path
                                    d="M826.1072,527.0594v-87.5h48.1499v16.25h-29.9248v19.875h29.9248v15.875h-29.9248v19.25h29.9248v16.25h-48.1499Z"
                                    fill="#fff" strokeWidth="0"/>
                                <path
                                    d="M1075.851,527.0594v-87.5h29.7002c12.2236,0,21.9741,4.209,29.25,12.625,7.2739,8.418,10.9126,18.793,10.9126,31.125s-3.6196,22.709-10.8564,31.125c-7.2388,8.418-17.0068,12.625-29.3062,12.625h-29.7002ZM1094.076,510.0594h9.7876c7.3491,0,13.0308-2.6035,17.0435-7.8125,4.0117-5.207,6.019-11.5195,6.019-18.9375s-2.0073-13.7285-6.019-18.9375c-4.0127-5.207-9.6943-7.8125-17.0435-7.8125h-9.7876v53.5Z"
                                    fill="#fff" strokeWidth="0"/>
                                <path
                                    d="M1396.8607,529.5594c-12.751,0-22.9888-4.3125-30.7124-12.9375-7.7256-8.625-11.5874-19.7285-11.5874-33.3125s3.8618-24.6875,11.5874-33.3125c7.7236-8.625,17.9614-12.9375,30.7124-12.9375,11.3994,0,20.6807,3.1055,27.8438,9.3125,7.1616,6.209,11.2676,14.5215,12.3188,24.9375h-19.3501c-1.0513-5-3.375-8.9375-6.9751-11.8125-3.5996-2.875-8.1753-4.3125-13.7246-4.3125-7.3516,0-13.0889,2.5215-17.2129,7.5625-4.1255,5.043-6.1875,11.8965-6.1875,20.5625s2.062,15.5215,6.1875,20.5625c4.124,5.043,9.8613,7.5625,17.2129,7.5625,5.5493,0,10.125-1.4375,13.7246-4.3125,3.6001-2.875,5.9238-6.8125,6.9751-11.8125h19.3501c-1.0513,10.418-5.1572,18.7305-12.3188,24.9375-7.1631,6.209-16.4443,9.3125-27.8438,9.3125Z"
                                    fill="#fff" strokeWidth="0"/>
                                <path
                                    d="M1640.1932,482.3094c4.7256.5,8.6064,2.5625,11.6445,6.1875,3.0371,3.625,4.5557,8.3965,4.5557,14.3125,0,7.5-2.25,13.418-6.75,17.75-4.5,4.334-10.6504,6.5-18.4502,6.5h-33.6367v-87.5h32.3994c7.7998,0,13.9678,2.0215,18.5068,6.0625,4.5371,4.043,6.8057,9.6055,6.8057,16.6875,0,5.584-1.3496,10.0625-4.0498,13.4375s-6.375,5.5625-11.0254,6.5625ZM1625.9061,454.1844h-10.125v21.25h10.125c3.4492,0,6.168-.9375,8.1562-2.8125,1.9863-1.875,2.9814-4.4785,2.9814-7.8125,0-3.332-.9756-5.9375-2.9248-7.8125-1.9512-1.875-4.6885-2.8125-8.2129-2.8125ZM1627.1434,511.6844c3.375,0,6.0361-1.0195,7.9873-3.0625,1.9492-2.041,2.9258-4.8535,2.9258-8.4375,0-3.25-.9951-5.8535-2.9814-7.8125-1.9883-1.957-4.6318-2.9375-7.9316-2.9375h-11.3623v22.25h11.3623Z"
                                    fill="#fff" strokeWidth="0"/>
                                <path
                                    d="M1922.7645,527.0594l-2.7002-10.625h-27.9004l-2.7002,10.625h-19.2373l26.6631-87.5h18.3369l26.7754,87.5h-19.2373ZM1896.2137,500.5594h19.8008l-9.9004-38.875-9.9004,38.875Z"
                                    fill="#fff" strokeWidth="0"/>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>
        </>
    )
}