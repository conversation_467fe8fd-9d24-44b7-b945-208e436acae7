//@ts-nocheck
import { useGLTF } from "@react-three/drei";

export function Ivy(props) {
    const { nodes, materials } = useGLTF("/ivy.glb");
    return (
        <group dispose={null}>
            <mesh
                {...props}
                castShadow
                receiveShadow
                geometry={nodes.Ivy1["geometry"]}
                material={materials.material}
            />
        </group>
    );
}

useGLTF.preload("/ivy.glb");

