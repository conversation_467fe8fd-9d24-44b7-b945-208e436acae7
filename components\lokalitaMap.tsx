import locations from "../utils/lokalitaData";
import {useState} from "react";

export default function LokalitaMap(props: {
    isOnHomePage?: boolean,
    locationHandler: (section: any) => void,
    isMobile?: boolean
}) {
    const [hoveredSectionInfo, setHoveredSectionInfo] = useState("")
    const [hoveredSection, setHoveredSection] = useState(null)
    const [hovered, setHovered] = useState(false);


    const handleClickSection = (section: number): void => {
        let hoveredSectionI;
        for (let i = 0; i < locations.length; i++) {
            if (locations[i].id === section) {
                // @ts-ignore
                setHoveredSectionInfo(locations[i]);
                hoveredSectionI = locations[i];
            }
        }
        props.locationHandler(hoveredSectionI);
    }

    const handleMouseEnter = (section: number) => {
        setHovered(true)
        setHoveredSection(section)
    }

    return (
        <>
            <div
                className={props.isMobile ? "overflow-visible lokalitka w-full absolute top-20" : "overflow-visible lokalitka w-full absolute top-0"}>
                <svg version="1.1" id="Layer_2_00000179635034819015116690000014293093843195744169_"
                     xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                     viewBox="0 0 753.4 435.8">
                    <g id="Layer_3">
                        <g>
                            <path className="st1" d="M334.2,183.3c0-8.7-7-15.7-15.7-15.7s-15.7,7-15.7,15.7c0,7.8,5.7,14.3,13.1,15.5c0,0,0.1,0,0.1,0.1l2.4,2.8
			l0,0c0.1,0.1,0.1,0.1,0.2,0.1s0.2,0,0.2-0.1l2.4-2.8l0.1-0.1c0.1,0,0.2-0.1,0.2-0.1C328.9,197.2,334.2,190.9,334.2,183.3z"/>
                            <path className="st2" d="M317,178.3c0.4,0.2,0.7,0.3,1.1,0.2c0.1,0,0.2,0,0.3,0.2c0,0.2,0,0.3-0.2,0.3c-0.5,0.1-1,0-1.4-0.3
			c-0.1-0.1-0.1-0.1-0.2,0c-0.2,0.2-0.5,0.4-0.7,0.5c-0.1,0.1-0.1,0.1,0,0.2c0.3,0.2,0.6,0.3,0.9,0.3c1.5,0.1,2.8-0.4,3.8-1.5
			c1.3-1.5,2-3.2,2.1-5.1c0-0.3,0-0.7,0-1c-0.8,1.5-1.8,2.7-2.9,3.9c0.3,0.1,0.6-0.1,0.8-0.1c0.3-0.2,0.6-0.4,0.9-0.6
			c0.1-0.1,0.2-0.2,0.3,0c0.1,0.1,0,0.3,0,0.3c-0.1,0.1-0.3,0.2-0.4,0.4c-0.4,0.3-0.9,0.6-1.5,0.5c-0.4-0.1-0.7,0.1-0.9,0.4
			c0,0.1-0.1,0.1-0.1,0.1c-0.1,0.2-0.3,0.3-0.5,0.5c0.1,0.1,0.2,0.1,0.3,0.2c0.5,0.1,1,0.1,1.4-0.2c0.1-0.1,0.2-0.1,0.3,0.1
			c0.1,0.2,0,0.3-0.1,0.3c-0.7,0.3-1.4,0.4-2.1,0c0-0.1-0.1-0.1-0.1,0C317.5,177.9,317.3,178.1,317,178.3L317,178.3z M322.2,171.8
			c-0.4,0.1-0.7,0.2-1,0.2c-0.7,0.1-1.4,0.2-2.1,0.3c-1.9,0.5-3.9,2.1-4.1,4.5c0,0.6,0,1.2,0.3,1.8c0.1,0.3,0.2,0.3,0.5,0.1
			c0-0.1,0.1-0.1,0.2-0.2c0.1-0.1,0.1-0.1,0-0.3c-0.4-0.9-0.4-1.7-0.1-2.6c0-0.2,0.1-0.3,0.3-0.2c0.1,0.1,0.1,0.2,0.1,0.4
			c-0.1,0.2-0.1,0.5-0.1,0.7c0,0.6,0,1.1,0.2,1.5c0,0.1,0.1,0.1,0.2,0.1c0.2-0.2,0.4-0.4,0.6-0.6c0-0.1,0.1-0.1,0-0.2
			c-0.3-0.8-0.3-1.6-0.1-2.4c0.1-0.4,0.2-0.8,0.4-1.2c0-0.1,0.1-0.2,0.3-0.1c0.1,0.1,0.1,0.2,0,0.3c-0.3,0.7-0.5,1.5-0.4,2.2
			c0,0.3,0,0.6,0.2,0.9c0.3-0.3,0.6-0.6,0.9-0.9c0.1-0.1,0.1-0.2,0.1-0.4v-0.4c0-1,0.4-1.9,1-2.6c0-0.1,0.1-0.1,0.1-0.1
			c0.1-0.1,0.1,0,0.2,0.1c0,0.1,0.1,0.1,0,0.2c0,0.1,0,0.1-0.1,0.2c-0.5,0.6-0.8,1.2-0.9,2c0,0.2,0,0.4,0,0.7
			C320.2,174.7,321.3,173.4,322.2,171.8L322.2,171.8z M322.7,170.9c0.1,0.1,0.1,0.2,0.2,0.3c0.5,2.5,0,4.7-1.6,6.7
			c-1,1.3-2.4,2-4,2.1c-0.4,0.1-0.9,0-1.3-0.2c-0.2-0.1-0.4-0.2-0.5-0.4c-0.1-0.1-0.1-0.1-0.2-0.1c-0.3,0.2-0.6,0.3-0.9,0.4
			c-0.1,0.1-0.2,0.1-0.3-0.1c0-0.2,0-0.3,0.2-0.3c0.3-0.1,0.6-0.3,0.8-0.4c-0.7-1.1-0.7-2.2-0.3-3.4c0.6-1.9,2-3.1,3.9-3.7
			c0.7-0.3,1.5-0.4,2.3-0.4c0.1,0,0.3-0.1,0.5-0.1c0.4-0.1,0.9-0.2,1.1-0.6C322.6,170.9,322.7,170.9,322.7,170.9L322.7,170.9z"/>
                            <g>
                                <path className="st3" d="M306.6,186.8c0,0.2,0,0.3,0.2,0.4c0.1,0,0.1,0,0.2,0c0.2-0.2,0.3-0.3,0.4-0.6c0-0.1,0-0.2-0.1-0.2
				C307,186.3,306.6,186.5,306.6,186.8L306.6,186.8z M311.9,184.8c0-0.2,0-0.3-0.2-0.3c-0.1,0-0.3,0-0.3,0.2c-0.3,0.5-0.4,1-0.3,1.6
				c0,0,0,0.1,0.1,0.2c0.1,0,0.1,0,0.2-0.1c0,0,0.1-0.1,0.1-0.2C311.8,185.7,311.9,185.2,311.9,184.8L311.9,184.8z M321.1,189v-0.2
				c0-0.1-0.1-0.2-0.2-0.2s-0.2,0-0.3,0.1c0,0,0,0.1-0.1,0.1c-0.2,0.5-0.3,1-0.3,1.6c0,0.1,0,0.3,0.1,0.3s0.2-0.1,0.3-0.2
				C320.9,190,321,189.5,321.1,189L321.1,189z M308.7,182.7c0.4-0.4,0.7-0.8,0.9-1.3c0.1-0.2,0.1-0.3,0.1-0.6c0-0.1,0-0.2-0.2-0.3
				c-0.1,0-0.2,0-0.3,0.1c-0.1,0.1-0.3,0.3-0.3,0.4c-0.1,0.2-0.2,0.5-0.2,0.7C308.7,182,308.6,182.3,308.7,182.7L308.7,182.7z
				 M310.7,186.6c-0.6-0.5-0.6-1.6-0.1-2.1c0.3-0.3,0.8-0.4,1.2-0.3c0,0,0.1,0,0.2,0c0.3-0.2,0.6-0.2,0.9,0c0.1,0.1,0.2,0.2,0.1,0.3
				c-0.1,0.6-0.2,1.2-0.2,1.8c0,0.1,0,0.2,0,0.3s0.1,0.1,0.2,0.1l0.1-0.1c0.2-0.3,0.4-0.7,0.4-1s0-0.6,0.1-0.9
				c0.1-0.3,0.4-0.5,0.8-0.4c0.2,0,0.2,0.2,0.2,0.3c-0.1,0.4-0.1,0.7-0.2,1.1c0,0.3,0,0.5,0,0.8c0,0.1,0,0.3,0.1,0.3
				s0.2-0.1,0.2-0.2c0.2-0.3,0.3-0.8,0.4-1.2c0-0.1,0-0.2,0.1-0.3c0-0.5,0.4-0.7,0.8-0.8l0,0c0.3,0,0.3,0,0.3,0.3
				c-0.1,0.5-0.2,1-0.2,1.6c0,0.2,0,0.3,0,0.5s0.2,0.2,0.3,0.1c0.1-0.1,0.2-0.2,0.3-0.3c0.1-0.3,0.2-0.6,0.3-0.8v-0.1
				c-0.2-0.2-0.2-0.7,0-0.9c0.1-0.1,0.3-0.2,0.5-0.1l0.1,0.1c0,0.1,0,0.2,0,0.3c0.1-0.2,0.1-0.3,0.2-0.5c0.2-0.4,0.4-0.5,0.8-0.4
				c0.3,0.1,0.5,0.4,0.5,0.8c-0.1,0.6-0.1,1.1-0.1,1.6v0.5c0,0.1,0.1,0.2,0.2,0.1c0,0,0.1-0.1,0.1-0.2c0.2-0.4,0.4-0.7,0.4-1.2
				c0-0.2,0-0.5,0-0.7c0,0,0-0.1,0.1-0.2c0.2-0.2,0.4-0.3,0.7-0.3c0.1,0,0.2,0,0.2,0.2c0,0.7-0.1,1.4-0.1,2.1v0.3
				c0,0.1,0.1,0.1,0.2,0.1c0.1-0.1,0.2-0.2,0.2-0.3c0.3-0.5,0.6-1,0.5-1.6c0-0.2,0-0.3,0.2-0.5c0.2-0.1,0.5-0.3,0.7-0.2
				c0.1,0,0.2,0.1,0.1,0.2c0,0.3-0.1,0.7-0.1,1c0-0.1,0.1-0.3,0.1-0.4c0.1-0.3,0.2-0.6,0.4-0.8c0.2-0.3,0.5-0.4,0.8-0.3
				c0.3,0.1,0.4,0.3,0.4,0.7c0,0.6-0.1,1.1-0.1,1.7c0,0.2,0,0.5,0,0.7c0,0.1,0,0.2,0.1,0.2s0.1,0,0.2-0.1s0.1-0.2,0.2-0.3
				c0.2-0.5,0.4-1,0.5-1.6c0-0.1,0-0.2,0.1-0.2s0.1,0.1,0.1,0.2c-0.1,0.6-0.3,1.1-0.6,1.6c-0.1,0.1-0.1,0.2-0.3,0.3
				c-0.2,0.2-0.6,0.3-0.9,0.2c-0.2-0.1-0.3-0.2-0.3-0.4s0-0.4,0-0.6c0-0.7,0-1.3,0.1-2c0-0.1,0-0.2,0-0.2c-0.1,0-0.1,0.1-0.1,0.2
				c-0.3,0.6-0.5,1.1-0.6,1.7c0,0.1-0.1,0.3-0.2,0.4v0.1c-0.1,1.3-0.3,2.6-0.3,3.9c0,0.2,0,0.4,0,0.7c0,0,0,0.1,0.1,0.2
				c0.1,0,0.1,0,0.1-0.1c0.3-0.5,0.5-1,0.5-1.6c0-0.3,0-0.6,0.1-0.9c0-0.1,0-0.1,0.1-0.2c0.2-0.2,0.4-0.3,0.7-0.3
				c0.2,0,0.3,0,0.2,0.2c0,0.3-0.1,0.6-0.1,0.9c0,0.4-0.1,0.8,0,1.2c0,0.1,0,0.1,0,0.2c0,0.1,0,0.2,0.2,0.2c0.1,0,0.2-0.1,0.3-0.2
				c0.3-0.4,0.3-0.8,0.4-1.3s0.1-0.9,0.1-1.4V188c0-0.1,0.1-0.1,0.2-0.1s0.1,0.1,0.1,0.1c0,0.8-0.1,1.6-0.3,2.4
				c-0.1,0.2-0.3,0.5-0.4,0.7c-0.4,0.5-1,0.5-1.3-0.1c-0.1-0.1-0.1-0.3-0.1-0.4c-0.1,0-0.1,0.1-0.1,0.1c-0.1,0.2-0.2,0.4-0.3,0.6
				c-0.2,0.3-0.4,0.3-0.7,0.3c-0.3-0.1-0.5-0.2-0.5-0.6v-1l0,0c0,0.1-0.1,0.3-0.2,0.4c-0.1,0.2-0.2,0.3-0.3,0.4
				c-0.3,0.3-0.6,0.2-0.9,0c-0.2-0.2-0.3-0.5-0.3-0.8s0-0.7,0.2-1c0.2-0.5,0.7-0.7,1.2-0.7c0.1,0,0.2,0,0.3,0.1c0.1,0,0.1,0,0.1,0
				c0-0.6,0.1-1.2,0.1-1.8c-0.1,0-0.1,0.1-0.1,0.1c-0.2,0.3-0.3,0.5-0.6,0.6c-0.6,0.2-0.9,0-0.9-0.7c-0.1,0.1-0.2,0.3-0.3,0.4
				c-0.2,0.3-0.5,0.4-0.8,0.3c-0.3,0-0.4-0.2-0.5-0.4c0-0.1,0-0.3,0-0.4c0-0.6,0-1.2,0.1-1.8c0-0.1,0-0.3,0-0.4
				c-0.1,0-0.1,0.1-0.1,0.2c-0.1,0.2-0.2,0.4-0.3,0.6c0,0.1-0.1,0.3-0.3,0.3c-0.1,0-0.1,0.1-0.1,0.2c-0.1,0.3-0.2,0.7-0.3,1
				c-0.2,0.3-0.3,0.5-0.6,0.5c-0.6,0-0.9-0.3-1-0.9c0,0,0-0.1,0-0.2c0,0,0,0.1,0,0.2c0,0.2-0.1,0.4-0.3,0.6
				c-0.2,0.3-0.3,0.4-0.7,0.4c-0.3,0-0.6-0.2-0.7-0.5c0,0,0-0.1,0-0.2c-0.3,0.5-0.6,0.8-1.2,0.6c-0.5-0.2-0.6-0.6-0.6-1.1l0,0
				c-0.3,0.8-1,1.1-1.8,1.1c-0.6,0-1.1-0.1-1.6-0.3c-0.2,0-0.2,0-0.3,0.1c-0.2,0.4-0.5,0.7-1,0.8c-0.3,0-0.5,0-0.7-0.1
				c-0.3-0.2-0.4-0.4-0.3-0.7c0-0.2,0.3-0.4,0.6-0.5c0.2,0,0.3,0,0.6,0c0.1,0,0.2,0,0.3-0.2c-0.1-0.1,0-0.2,0-0.3c0-0.7,0-1.3,0-2
				c-0.2,0.1-0.4,0.2-0.6,0.3c-0.2,0-0.4,0.1-0.7,0c0.1-0.1,0.2-0.1,0.3-0.1c0.3,0,0.7-0.2,1-0.4c0.1,0,0.1-0.1,0.1-0.2
				c0-0.5,0.1-1,0.3-1.5c0.1-0.4,0.3-0.8,0.7-1.1c0.3-0.3,0.6-0.4,1-0.3c0.3,0,0.5,0.3,0.5,0.6s0,0.5-0.2,0.7c-0.3,0.5-0.6,1-1,1.3
				c-0.2,0.1-0.2,0.3-0.2,0.5v1.7c0,0.4,0,0.7-0.2,1.1c0,0.1,0,0.2,0.1,0.3c0.6,0.2,1.2,0.4,1.8,0.3
				C310.5,186.7,310.5,186.6,310.7,186.6C310.6,186.6,310.7,186.6,310.7,186.6L310.7,186.6z"/>
                                <path className="st3" d="M327,189.5L327,189.5c0-0.3,0-0.5-0.2-0.7c0-0.1-0.1-0.1-0.2-0.1s-0.1,0.1-0.1,0.2c0,0.3,0.2,0.7,0.4,0.8
				C327,189.7,327,189.6,327,189.5L327,189.5L327,189.5z M326.2,188.8c-0.1,0-0.1,0.1-0.1,0.1c-0.2,0.5-0.3,1.1-0.1,1.6
				c0.1,0.3,0.3,0.3,0.6,0.1c0.2-0.2,0.3-0.5,0.4-0.7c0-0.1,0-0.1-0.1-0.1c-0.3-0.2-0.5-0.4-0.6-0.8
				C326.2,189,326.2,188.9,326.2,188.8L326.2,188.8z M327.2,189.7c0.2-0.1,0.4-0.2,0.6-0.3v-0.1v-0.5c0-0.2,0.2-0.3,0.3-0.3
				c0.3,0,0.3,0,0.3,0.3v0.2c0.1-0.2,0.2-0.4,0.3-0.6c0.2-0.3,0.3-0.3,0.6-0.3c0.3,0.1,0.5,0.3,0.5,0.6v0.4c0,0.6-0.1,1.1-0.1,1.6
				c0,0.1,0,0.2,0,0.3s0.1,0.2,0.2,0c0.1-0.1,0.2-0.2,0.3-0.3c0.2-0.4,0.3-0.8,0.4-1.3l0,0c0,0,0-0.1,0.1-0.1s0.1,0.1,0.1,0.2
				c-0.1,0.4-0.2,0.9-0.4,1.3c-0.1,0.2-0.2,0.3-0.3,0.5c-0.3,0.3-0.6,0.3-0.9,0.2c-0.2,0-0.3-0.2-0.3-0.4c0-0.1,0-0.3,0-0.5
				c0-0.7,0-1.3,0.1-1.9v-0.3c-0.1,0-0.1,0.1-0.1,0.2c-0.1,0.2-0.2,0.4-0.3,0.7c-0.1,0.2-0.3,0.3-0.5,0.2c-0.1,0-0.1,0-0.2,0
				c-0.1,0.1-0.3,0.2-0.5,0.3c-0.1,0-0.1,0.1-0.1,0.2c0,0.2-0.1,0.4-0.2,0.7c-0.2,0.3-0.5,0.5-0.9,0.5s-0.7-0.1-0.9-0.5
				c-0.3-0.6-0.2-1.8,0.3-2.3c0.3-0.3,0.6-0.3,1-0.3c0.4,0.1,0.5,0.3,0.6,0.7C327.2,189.1,327.2,189.4,327.2,189.7L327.2,189.7z"/>
                                <path className="st3" d="M320.7,183.3c0,0.4-0.3,0.7-0.6,0.7c-0.1,0-0.1,0-0.2-0.1s-0.2-0.6-0.1-0.7c0.1-0.2,0.4-0.3,0.6-0.3
				C320.7,183,320.7,183.1,320.7,183.3L320.7,183.3z"/>
                            </g>
                        </g>
                        <g id={"tesco2"} onMouseEnter={() => handleMouseEnter(16)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 16 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(16)}>
                            <circle className="st3" cx="623.6" cy="209.2" r="8.7"/>
                            <g>
                                <path className="st4" d="M628.2,207.2l-0.8,3.5c-0.1,0.3-0.2,0.5-0.4,0.7s-0.5,0.3-0.7,0.3h-5.2v0.4c0,0.2,0.2,0.4,0.4,0.4h6
				c0.2,0,0.4,0.2,0.4,0.4s-0.2,0.4-0.4,0.4H627c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.2,0-0.4,0.1-0.6H623c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.3,0.1-0.6,0.3-0.9c-0.2-0.2-0.4-0.5-0.4-0.8l-0.7-6.8h-0.8c-0.2,0-0.4-0.2-0.4-0.4
				s0.2-0.4,0.4-0.4h0.8c0.2,0,0.4,0.1,0.5,0.2c0.1,0.1,0.2,0.3,0.3,0.5v0.5h6.6l0,0c0.4,0,0.7,0.2,0.9,0.4
				C628.2,206.4,628.3,206.8,628.2,207.2L628.2,207.2L628.2,207.2z M623.6,201.8c-2,0-3.9,0.8-5.3,2.2c-1.4,1.4-2.2,3.3-2.2,5.3
				s0.8,3.9,2.2,5.3c1.4,1.4,3.3,2.2,5.3,2.2s3.9-0.8,5.3-2.2c1.4-1.4,2.2-3.3,2.2-5.3s-0.8-3.9-2.2-5.3
				C627.4,202.6,625.5,201.8,623.6,201.8L623.6,201.8L623.6,201.8z"/>
                                <path className="st4" d="M623.6,217.5c-2.2,0-4.3-0.9-5.8-2.4s-2.4-3.6-2.4-5.8s0.9-4.3,2.4-5.8s3.6-2.4,5.8-2.4s4.3,0.9,5.8,2.4
				s2.4,3.6,2.4,5.8s-0.9,4.3-2.4,5.8S625.7,217.5,623.6,217.5L623.6,217.5L623.6,217.5z M623.6,199.8c-2.2,0-4.3,0.8-6,2.1
				c-1.7,1.4-2.8,3.3-3.3,5.4c-0.4,2.1-0.1,4.4,0.9,6.3c1,1.9,2.7,3.4,4.7,4.3c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6
				c0.1,0.1,0.2,0.1,0.3,0.1s0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.7-2.3,4.7-4.3c1-1.9,1.4-4.1,0.9-6.3c-0.4-2.1-1.6-4.1-3.3-5.4
				S625.7,199.8,623.6,199.8L623.6,199.8L623.6,199.8z"/>
                                <path className="st4" d="M627.4,206.7c-0.1-0.1-0.2-0.1-0.3-0.1h-6.5l0.4,4.3h5.3c0.2,0,0.3-0.1,0.4-0.3l0.8-3.5
				C627.5,206.9,627.5,206.7,627.4,206.7L627.4,206.7z"/>
                                <path className="st4"
                                      d="M626.3,213.8c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S626.3,213.4,626.3,213.8"/>
                                <path className="st4"
                                      d="M622.4,213.8c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S622.4,213.4,622.4,213.8"/>
                            </g>
                        </g>
                        <g id={"terno"} onMouseEnter={() => handleMouseEnter(11)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 11 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(11)}>
                            <circle className="st3" cx="521.9" cy="96.4" r="8.7"/>
                            <g>
                                <path className="st4" d="M526.6,94.3l-0.8,3.5c-0.1,0.3-0.2,0.5-0.4,0.7s-0.5,0.3-0.7,0.3h-5.2v0.4c0,0.2,0.2,0.4,0.4,0.4h6
				c0.2,0,0.4,0.2,0.4,0.4c0,0.2-0.2,0.4-0.4,0.4h-0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.2,0-0.4,0.1-0.6h-1.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.3,0.1-0.6,0.3-0.9c-0.2-0.2-0.4-0.5-0.4-0.8l-0.7-6.8H517c-0.2,0-0.4-0.2-0.4-0.4
				c0-0.2,0.2-0.4,0.4-0.4h0.8c0.2,0,0.4,0.1,0.5,0.2s0.2,0.3,0.3,0.5v0.5h6.6l0,0c0.4,0,0.7,0.2,0.9,0.4
				C526.6,93.6,526.7,94,526.6,94.3L526.6,94.3L526.6,94.3z M521.9,89c-2,0-3.9,0.8-5.3,2.2s-2.2,3.3-2.2,5.3s0.8,3.9,2.2,5.3
				s3.3,2.2,5.3,2.2s3.9-0.8,5.3-2.2c1.4-1.4,2.2-3.3,2.2-5.3s-0.8-3.9-2.2-5.3C525.8,89.8,523.9,89,521.9,89L521.9,89L521.9,89z"/>
                                <path className="st4" d="M521.9,104.7c-2.2,0-4.3-0.9-5.8-2.4s-2.4-3.6-2.4-5.8s0.9-4.3,2.4-5.8s3.6-2.4,5.8-2.4s4.3,0.9,5.8,2.4
				s2.4,3.6,2.4,5.8c0,2.2-0.9,4.3-2.4,5.8S524.1,104.7,521.9,104.7L521.9,104.7L521.9,104.7z M521.9,87c-2.2,0-4.3,0.8-6,2.1
				c-1.7,1.4-2.8,3.3-3.3,5.4c-0.4,2.1-0.1,4.4,0.9,6.3s2.7,3.4,4.7,4.3c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.7-2.3,4.7-4.3c1-1.9,1.4-4.1,0.9-6.3c-0.4-2.1-1.6-4.1-3.3-5.4S524.1,87,521.9,87
				L521.9,87L521.9,87z"/>
                                <path className="st4" d="M525.7,93.8c-0.1-0.1-0.2-0.1-0.3-0.1h-6.5l0.4,4.3h5.3c0.2,0,0.3-0.1,0.4-0.3l0.8-3.5
				C525.8,94,525.8,93.9,525.7,93.8L525.7,93.8z"/>
                                <path className="st4"
                                      d="M524.6,100.9c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S524.6,100.6,524.6,100.9"/>
                                <path className="st4"
                                      d="M520.7,100.9c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S520.7,100.6,520.7,100.9"/>
                            </g>
                        </g>
                        <g id={"billa"} onMouseEnter={() => handleMouseEnter(10)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 10 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(10)}>
                            <circle className="st3" cx="501.7" cy="246.8" r="8.7"/>
                            <g>
                                <path className="st4" d="M506.3,244.7l-0.8,3.5c-0.1,0.3-0.2,0.5-0.4,0.7c-0.2,0.2-0.5,0.3-0.7,0.3h-5.2v0.4c0,0.2,0.2,0.4,0.4,0.4h6
				c0.2,0,0.4,0.2,0.4,0.4s-0.2,0.4-0.4,0.4h-0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.2,0-0.4,0.1-0.6H501c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.3,0.1-0.6,0.3-0.9c-0.2-0.2-0.4-0.5-0.4-0.8l-0.7-6.8H497c-0.2,0-0.4-0.2-0.4-0.4s0.2-0.4,0.4-0.4
				h0.8c0.2,0,0.4,0.1,0.5,0.2c0.1,0.1,0.2,0.3,0.3,0.5v0.5h6.6l0,0c0.4,0,0.7,0.2,0.9,0.4C506.3,244,506.4,244.3,506.3,244.7
				L506.3,244.7L506.3,244.7z M501.7,239.3c-2,0-3.9,0.8-5.3,2.2s-2.2,3.3-2.2,5.3s0.8,3.9,2.2,5.3c1.4,1.4,3.3,2.2,5.3,2.2
				s3.9-0.8,5.3-2.2s2.2-3.3,2.2-5.3s-0.8-3.9-2.2-5.3C505.5,240.1,503.6,239.3,501.7,239.3L501.7,239.3L501.7,239.3z"/>
                                <path className="st4" d="M501.7,255c-2.2,0-4.3-0.9-5.8-2.4s-2.4-3.6-2.4-5.8s0.9-4.3,2.4-5.8s3.6-2.4,5.8-2.4s4.3,0.9,5.8,2.4
				s2.4,3.6,2.4,5.8s-0.9,4.3-2.4,5.8S503.8,255,501.7,255L501.7,255L501.7,255z M501.7,237.4c-2.2,0-4.3,0.8-6,2.1
				c-1.7,1.4-2.8,3.3-3.3,5.4c-0.4,2.1-0.1,4.4,0.9,6.3c1,1.9,2.7,3.4,4.7,4.3c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6
				c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.7-2.3,4.7-4.3c1-1.9,1.4-4.1,0.9-6.3
				c-0.4-2.1-1.6-4.1-3.3-5.4S503.8,237.4,501.7,237.4L501.7,237.4L501.7,237.4z"/>
                                <path className="st4" d="M505.5,244.2c-0.1-0.1-0.2-0.1-0.3-0.1h-6.5l0.4,4.3h5.3c0.2,0,0.3-0.1,0.4-0.3l0.8-3.5
				C505.6,244.4,505.6,244.3,505.5,244.2L505.5,244.2z"/>
                                <path className="st4"
                                      d="M504.4,251.3c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S504.4,250.9,504.4,251.3"/>
                                <path className="st4"
                                      d="M500.5,251.3c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S500.5,250.9,500.5,251.3"/>
                            </g>
                        </g>
                        <g id={"lidl"} onMouseEnter={() => handleMouseEnter(9)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 9 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(9)}>
                            <circle className="st3" cx="466.3" cy="211.8" r="8.7"/>
                            <g>
                                <path className="st4" d="M471,209.7l-0.8,3.5c-0.1,0.3-0.2,0.5-0.4,0.7c-0.2,0.2-0.5,0.3-0.7,0.3h-5.2v0.4c0,0.2,0.2,0.4,0.4,0.4h6
				c0.2,0,0.4,0.2,0.4,0.4s-0.2,0.4-0.4,0.4h-0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.2,0-0.4,0.1-0.6h-1.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.3,0.1-0.6,0.3-0.9c-0.2-0.2-0.4-0.5-0.4-0.8l-0.7-6.8h-0.8c-0.2,0-0.4-0.2-0.4-0.4
				s0.2-0.4,0.4-0.4h0.8c0.2,0,0.4,0.1,0.5,0.2c0.1,0.1,0.2,0.3,0.3,0.5v0.5h6.6l0,0c0.4,0,0.7,0.2,0.9,0.4
				C471,209,471,209.4,471,209.7L471,209.7L471,209.7z M466.3,204.4c-2,0-3.9,0.8-5.3,2.2s-2.2,3.3-2.2,5.3s0.8,3.9,2.2,5.3
				c1.4,1.4,3.3,2.2,5.3,2.2s3.9-0.8,5.3-2.2s2.2-3.3,2.2-5.3s-0.8-3.9-2.2-5.3C470.2,205.2,468.3,204.4,466.3,204.4L466.3,204.4
				L466.3,204.4z"/>
                                <path className="st4" d="M466.3,220.1c-2.2,0-4.3-0.9-5.8-2.4s-2.4-3.6-2.4-5.8s0.9-4.3,2.4-5.8s3.6-2.4,5.8-2.4s4.3,0.9,5.8,2.4
				s2.4,3.6,2.4,5.8s-0.9,4.3-2.4,5.8S468.5,220.1,466.3,220.1L466.3,220.1L466.3,220.1z M466.3,202.4c-2.2,0-4.3,0.8-6,2.1
				c-1.7,1.4-2.8,3.3-3.3,5.4c-0.4,2.1-0.1,4.4,0.9,6.3c1,1.9,2.7,3.4,4.7,4.3c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6
				c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.7-2.3,4.7-4.3c1-1.9,1.4-4.1,0.9-6.3
				c-0.4-2.1-1.6-4.1-3.3-5.4C470.6,203.2,468.5,202.4,466.3,202.4L466.3,202.4L466.3,202.4z"/>
                                <path className="st4" d="M470.1,209.2c-0.1-0.1-0.2-0.1-0.3-0.1h-6.5l0.4,4.3h5.3c0.2,0,0.3-0.1,0.4-0.3l0.8-3.5
				C470.2,209.5,470.2,209.3,470.1,209.2L470.1,209.2z"/>
                                <path className="st4"
                                      d="M469,216.3c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S469,216,469,216.3"/>
                                <path className="st4"
                                      d="M465.1,216.3c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S465.1,216,465.1,216.3"/>
                            </g>
                        </g>
                        <g id={"tescoexpresss"} onMouseEnter={() => handleMouseEnter(8)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 8 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(8)}>
                            <circle className="st3" cx="439.5" cy="168.2" r="8.7"/>
                            <g>
                                <path className="st4" d="M444.2,166.1l-0.8,3.5c-0.1,0.3-0.2,0.5-0.4,0.7c-0.2,0.2-0.5,0.3-0.7,0.3h-5.2v0.4c0,0.2,0.2,0.4,0.4,0.4h6
				c0.2,0,0.4,0.2,0.4,0.4s-0.2,0.4-0.4,0.4H443c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.2,0-0.4,0.1-0.6H439c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.3,0.1-0.6,0.3-0.9c-0.2-0.2-0.4-0.5-0.4-0.8l-0.7-6.8h-0.8c-0.2,0-0.4-0.2-0.4-0.4
				s0.2-0.4,0.4-0.4h0.8c0.2,0,0.4,0.1,0.5,0.2c0.1,0.1,0.2,0.3,0.3,0.5v0.5h6.6l0,0c0.4,0,0.7,0.2,0.9,0.4
				C444.2,165.4,444.3,165.8,444.2,166.1L444.2,166.1L444.2,166.1z M439.5,160.8c-2,0-3.9,0.8-5.3,2.2s-2.2,3.3-2.2,5.3
				s0.8,3.9,2.2,5.3c1.4,1.4,3.3,2.2,5.3,2.2s3.9-0.8,5.3-2.2s2.2-3.3,2.2-5.3s-0.8-3.9-2.2-5.3
				C443.4,161.6,441.5,160.8,439.5,160.8L439.5,160.8L439.5,160.8z"/>
                                <path className="st4" d="M439.5,176.4c-2.2,0-4.3-0.9-5.8-2.4s-2.4-3.6-2.4-5.8s0.9-4.3,2.4-5.8s3.6-2.4,5.8-2.4s4.3,0.9,5.8,2.4
				s2.4,3.6,2.4,5.8s-0.9,4.3-2.4,5.8S441.7,176.4,439.5,176.4L439.5,176.4L439.5,176.4z M439.5,158.8c-2.2,0-4.3,0.8-6,2.1
				c-1.7,1.4-2.8,3.3-3.3,5.4c-0.4,2.1-0.1,4.4,0.9,6.3c1,1.9,2.7,3.4,4.7,4.3c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6
				c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.7-2.3,4.7-4.3c1-1.9,1.4-4.1,0.9-6.3
				c-0.4-2.1-1.6-4.1-3.3-5.4C443.8,159.6,441.7,158.8,439.5,158.8L439.5,158.8L439.5,158.8z"/>
                                <path className="st4" d="M443.4,165.6c-0.1-0.1-0.2-0.1-0.3-0.1h-6.5l0.4,4.3h5.3c0.2,0,0.3-0.1,0.4-0.3l0.8-3.5
				C443.5,165.8,443.4,165.7,443.4,165.6L443.4,165.6z"/>
                                <path className="st4"
                                      d="M442.3,172.7c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S442.3,172.4,442.3,172.7"/>
                                <path className="st4"
                                      d="M438.4,172.7c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S438.4,172.4,438.4,172.7"/>
                            </g>
                        </g>
                        <g id={"vecierka"} onMouseEnter={() => handleMouseEnter(4)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 4 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(4)}>
                            <circle className="st3" cx="374" cy="145.7" r="8.7"/>
                            <g>
                                <path className="st4" d="M378.7,143.6l-0.8,3.5c-0.1,0.3-0.2,0.5-0.4,0.7c-0.2,0.2-0.5,0.3-0.7,0.3h-5.2v0.4c0,0.2,0.2,0.4,0.4,0.4h6
				c0.2,0,0.4,0.2,0.4,0.4s-0.2,0.4-0.4,0.4h-0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.2,0-0.4,0.1-0.6h-1.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.3,0.1-0.6,0.3-0.9c-0.2-0.2-0.4-0.5-0.4-0.8l-0.7-6.8h-0.8c-0.2,0-0.4-0.2-0.4-0.4
				s0.2-0.4,0.4-0.4h0.8c0.2,0,0.4,0.1,0.5,0.2c0.1,0.1,0.2,0.3,0.3,0.5v0.5h6.6l0,0c0.4,0,0.7,0.2,0.9,0.4
				C378.7,142.9,378.8,143.2,378.7,143.6L378.7,143.6L378.7,143.6z M374,138.2c-2,0-3.9,0.8-5.3,2.2s-2.2,3.3-2.2,5.3
				s0.8,3.9,2.2,5.3c1.4,1.4,3.3,2.2,5.3,2.2s3.9-0.8,5.3-2.2s2.2-3.3,2.2-5.3s-0.8-3.9-2.2-5.3C377.9,139,376,138.2,374,138.2
				L374,138.2L374,138.2z"/>
                                <path className="st4" d="M374,153.9c-2.2,0-4.3-0.9-5.8-2.4s-2.4-3.6-2.4-5.8s0.9-4.3,2.4-5.8s3.6-2.4,5.8-2.4s4.3,0.9,5.8,2.4
				s2.4,3.6,2.4,5.8s-0.9,4.3-2.4,5.8S376.2,153.9,374,153.9L374,153.9L374,153.9z M374,136.3c-2.2,0-4.3,0.8-6,2.1
				c-1.7,1.4-2.8,3.3-3.3,5.4c-0.4,2.1-0.1,4.4,0.9,6.3c1,1.9,2.7,3.4,4.7,4.3c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6
				c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.7-2.3,4.7-4.3c1-1.9,1.4-4.1,0.9-6.3
				c-0.4-2.1-1.6-4.1-3.3-5.4C378.3,137,376.2,136.3,374,136.3L374,136.3L374,136.3z"/>
                                <path className="st4" d="M377.8,143.1c-0.1-0.1-0.2-0.1-0.3-0.1H371l0.4,4.3h5.3c0.2,0,0.3-0.1,0.4-0.3l0.8-3.5
				C377.9,143.3,377.9,143.2,377.8,143.1L377.8,143.1z"/>
                                <path className="st4"
                                      d="M376.7,150.2c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S376.7,149.9,376.7,150.2"/>
                                <path className="st4"
                                      d="M372.8,150.2c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S372.8,149.9,372.8,150.2"/>
                            </g>
                        </g>
                        <g id={"kaufland"} onMouseEnter={() => handleMouseEnter(3)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 3 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(3)}>
                            <circle className="st3" cx="337.8" cy="329.1" r="8.7"/>
                            <g>
                                <path className="st4" d="M342.5,327l-0.8,3.5c-0.1,0.3-0.2,0.5-0.4,0.7c-0.2,0.2-0.5,0.3-0.7,0.3h-5.2v0.4c0,0.2,0.2,0.4,0.4,0.4h6
				c0.2,0,0.4,0.2,0.4,0.4c0,0.2-0.2,0.4-0.4,0.4h-0.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.2,0-0.4,0.1-0.6h-1.5c0.1,0.2,0.1,0.4,0.1,0.6c0,0.5-0.3,0.9-0.7,1.2c-0.4,0.2-0.9,0.2-1.4,0
				c-0.4-0.2-0.7-0.7-0.7-1.2c0-0.3,0.1-0.6,0.3-0.9c-0.2-0.2-0.4-0.5-0.4-0.8l-0.7-6.8h-0.8c-0.2,0-0.4-0.2-0.4-0.4
				c0-0.2,0.2-0.4,0.4-0.4h0.8c0.2,0,0.4,0.1,0.5,0.2s0.2,0.3,0.3,0.5v0.5h6.6l0,0c0.4,0,0.7,0.2,0.9,0.4
				C342.5,326.3,342.6,326.7,342.5,327L342.5,327L342.5,327z M337.8,321.6c-2,0-3.9,0.8-5.3,2.2s-2.2,3.3-2.2,5.3s0.8,3.9,2.2,5.3
				c1.4,1.4,3.3,2.2,5.3,2.2s3.9-0.8,5.3-2.2c1.4-1.4,2.2-3.3,2.2-5.3s-0.8-3.9-2.2-5.3C341.7,322.4,339.8,321.6,337.8,321.6
				L337.8,321.6L337.8,321.6z"/>
                                <path className="st4" d="M337.8,337.3c-2.2,0-4.3-0.9-5.8-2.4s-2.4-3.6-2.4-5.8s0.9-4.3,2.4-5.8s3.6-2.4,5.8-2.4s4.3,0.9,5.8,2.4
				s2.4,3.6,2.4,5.8c0,2.2-0.9,4.3-2.4,5.8S340,337.3,337.8,337.3L337.8,337.3L337.8,337.3z M337.8,319.7c-2.2,0-4.3,0.8-6,2.1
				c-1.7,1.4-2.8,3.3-3.3,5.4c-0.4,2.1-0.1,4.4,0.9,6.3s2.7,3.4,4.7,4.3c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6c0.1,0.1,0.2,0.1,0.3,0.1
				c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.7-2.3,4.7-4.3c1-1.9,1.4-4.1,0.9-6.3c-0.4-2.1-1.6-4.1-3.3-5.4
				C342.1,320.4,340,319.7,337.8,319.7L337.8,319.7L337.8,319.7z"/>
                                <path className="st4" d="M341.6,326.5c-0.1-0.1-0.2-0.1-0.3-0.1h-6.5l0.4,4.3h5.3c0.2,0,0.3-0.1,0.4-0.3l0.8-3.5
				C341.7,326.7,341.7,326.6,341.6,326.5L341.6,326.5z"/>
                                <path className="st4"
                                      d="M340.5,333.6c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S340.5,333.3,340.5,333.6"/>
                                <path className="st4"
                                      d="M336.6,333.6c0,0.3-0.3,0.6-0.6,0.6s-0.6-0.3-0.6-0.6s0.3-0.6,0.6-0.6S336.6,333.3,336.6,333.6"/>
                            </g>
                        </g>
                        <g id={"rajcianska3"} onMouseEnter={() => handleMouseEnter(17)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 17 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(17)}>
                            <circle className="st3" cx="574.6" cy="176.5" r="8.7"/>
                            <g>
                                <path className="st5" d="M569.3,175l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9L569.3,175z"/>
                                <path className="st5" d="M574.6,177.7c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4c0.1,0.1,0.2,0.2,0.4,0.3
				c0.7,0.2,1.4,0.3,2.2,0.3c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.3,0.1-0.4v-1.8l-2.4,0.8l0,0
				C574.8,177.7,574.7,177.7,574.6,177.7L574.6,177.7z"/>
                                <path className="st5" d="M580.2,175.8l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4s-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2c0,0.3-0.1,0.6-0.3,0.9
				c-0.2,0.3-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4c-0.8,0-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6s-0.3-0.6-0.3-0.9v-2
				l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6c0.1-0.2,0.2-0.3,0.4-0.4l5.3-1.9c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9
				c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C580.5,175.5,580.4,175.7,580.2,175.8L580.2,175.8L580.2,175.8z M574.6,169.4
				c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1
				c1.3-1.3,2.1-3.2,2.1-5.1c0-1.9-0.8-3.7-2.1-5.1C578.3,170.1,576.5,169.4,574.6,169.4L574.6,169.4L574.6,169.4z"/>
                                <path className="st5" d="M574.6,184.5c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C578.7,183.6,576.7,184.5,574.6,184.5z M574.6,167.5c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.9,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2
				C578.8,168.2,576.7,167.5,574.6,167.5L574.6,167.5z"/>
                            </g>
                        </g>
                        <g id={"frederico"} onMouseEnter={() => handleMouseEnter(14)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 14 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(14)}>
                            <circle className="st3" cx="579.9" cy="282.6" r="8.7"/>
                            <g>
                                <path className="st5" d="M574.6,281.1l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9L574.6,281.1z"/>
                                <path className="st5" d="M579.9,283.7c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4s0.2,0.2,0.4,0.3c0.7,0.2,1.4,0.3,2.2,0.3
				c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.3,0.1-0.4v-1.8l-2.4,0.8l0,0C580.1,283.7,580,283.7,579.9,283.7
				L579.9,283.7z"/>
                                <path className="st5" d="M585.4,281.8l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4s-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2c0,0.3-0.1,0.6-0.3,0.9
				s-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4c-0.8,0-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6c-0.2-0.3-0.3-0.6-0.3-0.9v-2
				l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6s0.2-0.3,0.4-0.4l5.3-1.9c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9
				c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C585.8,281.5,585.6,281.7,585.4,281.8L585.4,281.8L585.4,281.8z M579.9,275.4
				c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1
				c1.3-1.3,2.1-3.2,2.1-5.1s-0.8-3.7-2.1-5.1C583.6,276.1,581.8,275.4,579.9,275.4L579.9,275.4L579.9,275.4z"/>
                                <path className="st5" d="M579.9,290.5c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6S582,290.5,579.9,290.5z M579.9,273.5c-2.1,0-4.1,0.7-5.8,2.1c-1.6,1.3-2.7,3.2-3.1,5.2
				c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1s0.2,0,0.3-0.1l3-3.5
				l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1s1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2C584,274.2,582,273.5,579.9,273.5L579.9,273.5z"/>
                            </g>
                        </g>
                        <g id={"podzahradna"} onMouseEnter={() => handleMouseEnter(12)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 12 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(12)}>
                            <ellipse className="st3" cx="515.1" cy="326.5" rx="8.7" ry="8.7"/>
                            <g>
                                <path className="st5"
                                      d="M509.8,325l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9L509.8,325L509.8,325z"/>
                                <path className="st5" d="M515.1,327.6c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4s0.2,0.2,0.4,0.3c0.7,0.2,1.4,0.3,2.2,0.3
				c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.3,0.1-0.4v-1.8l-2.4,0.8l0,0C515.3,327.6,515.2,327.6,515.1,327.6
				L515.1,327.6z"/>
                                <path className="st5" d="M520.6,325.7l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4s-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2c0,0.3-0.1,0.6-0.3,0.9
				s-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4c-0.8,0-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6c-0.2-0.3-0.3-0.6-0.3-0.9v-2
				l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6s0.2-0.3,0.4-0.4l5.3-1.9c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9
				c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C521,325.5,520.8,325.6,520.6,325.7L520.6,325.7L520.6,325.7z M515.1,319.3
				c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1
				c1.3-1.3,2.1-3.2,2.1-5.1s-0.8-3.7-2.1-5.1C518.8,320.1,517,319.3,515.1,319.3L515.1,319.3L515.1,319.3z"/>
                                <path className="st5" d="M515.1,334.4c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C519.2,333.6,517.2,334.4,515.1,334.4z M515.1,317.5c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1s1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2
				C519.2,318.2,517.2,317.5,515.1,317.5L515.1,317.5z"/>
                            </g>
                        </g>
                        <g id={"namorna"} onMouseEnter={() => handleMouseEnter(18)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 18 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(18)}>
                            <circle className="st3" cx="356" cy="286.7" r="8.7"/>
                            <g>
                                <path className="st5" d="M350.7,285.2l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9L350.7,285.2z"/>
                                <path className="st5" d="M356,287.8c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4s0.2,0.2,0.4,0.3c0.7,0.2,1.4,0.3,2.2,0.3
				c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.3,0.1-0.4v-1.8l-2.4,0.8l0,0C356.2,287.8,356.1,287.8,356,287.8
				L356,287.8z"/>
                                <path className="st5" d="M361.5,285.9l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4c-0.2,0-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2
				c0,0.3-0.1,0.6-0.3,0.9c-0.2,0.3-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4s-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6
				c-0.2-0.3-0.3-0.6-0.3-0.9v-2l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6s0.2-0.3,0.4-0.4l5.3-1.9
				c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C361.9,285.7,361.7,285.8,361.5,285.9L361.5,285.9
				L361.5,285.9z M356,279.5c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1
				s3.7-0.8,5.1-2.1c1.3-1.3,2.1-3.2,2.1-5.1s-0.8-3.7-2.1-5.1C359.7,280.3,357.9,279.5,356,279.5L356,279.5L356,279.5z"/>
                                <path className="st5" d="M356,294.6c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C360.1,293.8,358.1,294.6,356,294.6L356,294.6z M356,277.6c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1s1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2C360.1,278.4,358.1,277.6,356,277.6
				L356,277.6z"/>
                            </g>
                        </g>
                        <g id={"1"} onMouseEnter={() => handleMouseEnter(1)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 1 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(1)}>
                            <circle className="st3" cx="333.3" cy="207.2" r="8.7"/>
                            <g>
                                <path className="st6" d="M328,205.6l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9L328,205.6z"/>
                                <path className="st6"
                                      d="M333.3,208.3c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4c0.1,0.1,0.2,0.2,0.4,0.3 c0.7,0.2,1.4,0.3,2.2,0.3c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3s0.1-0.3,0.1-0.4v-1.8l-2.4,0.8l0,0 C333.5,208.3,333.4,208.3,333.3,208.3L333.3,208.3z"/>
                                <path className="st6" d="M338.8,206.4l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4c-0.2,0-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2
				c0,0.3-0.1,0.6-0.3,0.9c-0.2,0.3-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4s-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6
				s-0.3-0.6-0.3-0.9v-2l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6c0.1-0.2,0.2-0.3,0.4-0.4l5.3-1.9
				c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C339.2,206.1,339,206.3,338.8,206.4L338.8,206.4
				L338.8,206.4z M333.3,200c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1
				s3.7-0.8,5.1-2.1c1.3-1.3,2.1-3.2,2.1-5.1c0-1.9-0.8-3.7-2.1-5.1S335.2,200,333.3,200L333.3,200L333.3,200z"/>
                                <path className="st6" d="M333.3,215.1c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C337.4,214.2,335.4,215.1,333.3,215.1L333.3,215.1z M333.3,198.1c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.9,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2
				C337.4,198.8,335.4,198.1,333.3,198.1L333.3,198.1z"/>
                            </g>
                        </g>
                        <g id={"estonska3"} onMouseEnter={() => handleMouseEnter(7)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 7 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(7)}>
                            <circle className="st3" cx="437.3" cy="124.5" r="8.7"/>
                            <g>
                                <path className="st6"
                                      d="M432.1,122.9l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9C437.4,121.1,432.1,122.9,432.1,122.9z"/>
                                <path className="st6" d="M437.3,125.6c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4s0.2,0.2,0.4,0.3c0.7,0.2,1.4,0.3,2.2,0.3
				c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.3,0.1-0.4v-1.8l-2.4,0.8l0,0C437.5,125.6,437.4,125.6,437.3,125.6
				L437.3,125.6z"/>
                                <path className="st6" d="M442.9,123.7l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4c-0.2,0-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2
				c0,0.3-0.1,0.6-0.3,0.9c-0.2,0.3-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4s-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6
				c-0.2-0.3-0.3-0.6-0.3-0.9v-2l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6s0.2-0.3,0.4-0.4l5.3-1.9
				c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C443.2,123.4,443.1,123.6,442.9,123.7L442.9,123.7
				L442.9,123.7z M437.3,117.3c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1
				s3.7-0.8,5.1-2.1c1.3-1.3,2.1-3.2,2.1-5.1s-0.8-3.7-2.1-5.1S439.3,117.3,437.3,117.3L437.3,117.3L437.3,117.3z"/>
                                <path className="st6" d="M437.3,132.4c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C441.5,131.6,439.5,132.4,437.3,132.4L437.3,132.4z M437.3,115.4c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.9,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2
				C441.5,116.1,439.4,115.4,437.3,115.4z"/>
                            </g>
                        </g>
                        <g id={"estonska7"} onMouseEnter={() => handleMouseEnter(6)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 6 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(6)}>
                            <circle className="st3" cx="414" cy="109.8" r="8.7"/>
                            <g>
                                <path className="st6"
                                      d="M408.7,108.3l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9C414,106.5,408.7,108.3,408.7,108.3z"/>
                                <path className="st6" d="M414,111c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4s0.2,0.2,0.4,0.3c0.7,0.2,1.4,0.3,2.2,0.3
				c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.3,0.1-0.4v-1.8l-2.4,0.8l0,0C414.2,111,414.1,111,414,111L414,111
				z"/>
                                <path className="st6" d="M419.5,109l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4c-0.2,0-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2
				c0,0.3-0.1,0.6-0.3,0.9c-0.2,0.3-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4s-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6
				c-0.2-0.3-0.3-0.6-0.3-0.9v-2l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6s0.2-0.3,0.4-0.4l5.3-1.9
				c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C419.9,108.8,419.7,109,419.5,109L419.5,109
				L419.5,109z M414,102.7c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1
				s3.7-0.8,5.1-2.1c1.3-1.3,2.1-3.2,2.1-5.1s-0.8-3.7-2.1-5.1S415.9,102.7,414,102.7L414,102.7L414,102.7z"/>
                                <path className="st6" d="M414,117.8c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C418.1,116.9,416.1,117.8,414,117.8L414,117.8z M414,100.8c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1s1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2C418.1,101.5,416.1,100.8,414,100.8z
				"/>
                            </g>
                        </g>
                        <g id={"sovicka"} onMouseEnter={() => handleMouseEnter(5)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 5 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(5)}>
                            <circle className="st3" cx="388.1" cy="100.8" r="8.7"/>
                            <g>
                                <path className="st6"
                                      d="M382.8,99.3l5.3,1.9l5.3-1.9l0,0l0,0l-5.3-1.9C388.1,97.4,382.8,99.3,382.8,99.3z"/>
                                <path className="st6" d="M388.1,101.9c-0.1,0-0.2,0-0.3,0l-2.4-0.9v1.8c0,0.2,0,0.3,0.1,0.4s0.2,0.2,0.4,0.3c0.7,0.2,1.4,0.3,2.2,0.3
				c0.7,0,1.5-0.1,2.2-0.3c0.1-0.1,0.3-0.2,0.4-0.3c0.1-0.1,0.1-0.3,0.1-0.4V101l-2.4,0.8l0,0C388.3,101.9,388.2,101.9,388.1,101.9
				L388.1,101.9z"/>
                                <path className="st6" d="M393.6,100l-0.6,0.2v2.1c0,0.2-0.2,0.4-0.4,0.4c-0.2,0-0.4-0.2-0.4-0.4v-1.8l-0.8,0.3v2
				c0,0.3-0.1,0.6-0.3,0.9c-0.2,0.3-0.4,0.4-0.7,0.6c-0.8,0.3-1.6,0.4-2.4,0.4s-1.6-0.1-2.4-0.4c-0.3-0.1-0.5-0.3-0.7-0.6
				c-0.2-0.3-0.3-0.6-0.3-0.9v-2l-2.1-0.8c-0.2-0.1-0.4-0.2-0.4-0.4c-0.1-0.2-0.1-0.4,0-0.6s0.2-0.3,0.4-0.4l5.3-1.9
				c0.2-0.1,0.4-0.1,0.5,0l5.3,1.9c0.2,0.1,0.4,0.2,0.4,0.4c0.1,0.2,0.1,0.4,0,0.6C394,99.8,393.8,99.9,393.6,100L393.6,100
				L393.6,100z M388.1,93.6c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1
				s3.7-0.8,5.1-2.1c1.3-1.3,2.1-3.2,2.1-5.1s-0.8-3.7-2.1-5.1S390,93.6,388.1,93.6L388.1,93.6L388.1,93.6z"/>
                                <path className="st6" d="M388.1,108.7c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C392.2,107.9,390.2,108.7,388.1,108.7L388.1,108.7z M388.1,91.7c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1s1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2C392.2,92.5,390.2,91.7,388.1,91.7z"
                                />
                            </g>
                        </g>
                        <g>
                            <circle className="st3" cx="368.1" cy="66.4" r="11"/>
                            <g>
                                <rect x="371.6" y="69.8" className="st7" width="1" height="1.5"/>
                                <path className="st7" d="M368.1,56.8c-2.3,0-4.4,0.8-6.1,2.3s-2.8,3.5-3.2,5.7c-0.3,2.2,0.1,4.5,1.3,6.4h2.5v-1.5H361
				c-0.2,0-0.4-0.1-0.6-0.2c-0.1-0.1-0.2-0.3-0.2-0.6c0-0.2,0.1-0.4,0.2-0.6l0.7-0.7c-0.3-0.1-0.5-0.3-0.6-0.5
				c-0.1-0.3,0-0.6,0.1-0.8l1-1.2c-0.2,0-0.4-0.1-0.5-0.2s-0.2-0.3-0.2-0.5s0.1-0.4,0.2-0.5l2.5-2.8c0.2-0.2,0.4-0.3,0.7-0.3
				s0.5,0.1,0.7,0.3l0.4,0.4l1.9-2.1c0.2-0.2,0.5-0.3,0.8-0.3s0.6,0.1,0.8,0.3l1.9,2.1l0.4-0.4c0.2-0.2,0.4-0.3,0.7-0.3
				c0.3,0,0.5,0.1,0.7,0.3l2.5,2.8c0.1,0.1,0.2,0.3,0.2,0.5s-0.1,0.4-0.2,0.5s-0.3,0.2-0.5,0.2l1,1.2c0.2,0.2,0.2,0.5,0.1,0.8
				s-0.3,0.5-0.6,0.5l0.7,0.7c0.1,0.1,0.2,0.3,0.2,0.6s-0.1,0.4-0.2,0.6c-0.1,0.1-0.3,0.2-0.6,0.2h-1.7v1.5h2.5
				c1.2-1.9,1.7-4.2,1.3-6.4c-0.3-2.2-1.5-4.3-3.2-5.7C372.6,57.6,370.4,56.8,368.1,56.8L368.1,56.8L368.1,56.8z"/>
                                <rect x="363.6" y="69.8" className="st7" width="1" height="1.5"/>
                                <path className="st7" d="M368.1,76.7c-2.8,0-5.4-1.1-7.4-3.1s-3.1-4.6-3.1-7.4s1.1-5.4,3.1-7.4s4.6-3.1,7.4-3.1s5.4,1.1,7.4,3.1
				s3.1,4.6,3.1,7.4s-1.1,5.4-3.1,7.4S370.9,76.7,368.1,76.7L368.1,76.7z M368.1,54.4c-2.8,0-5.4,1-7.6,2.7
				c-2.1,1.8-3.6,4.2-4.1,6.9s-0.1,5.5,1.2,8c1.3,2.4,3.4,4.3,6,5.4c0.1,0,0.1,0.1,0.2,0.1l4,4.6l0,0c0.1,0.1,0.2,0.2,0.4,0.2
				c0.1,0,0.3-0.1,0.4-0.2l4-4.6c0.1-0.1,0.1-0.1,0.2-0.1c2.6-1.1,4.7-3,6-5.4s1.7-5.2,1.2-8c-0.5-2.7-2-5.1-4.1-6.9
				C373.6,55.3,370.9,54.4,368.1,54.4L368.1,54.4L368.1,54.4z"/>
                                <path className="st7" d="M361.5,68.8h1.7c0-0.2,0.1-0.3,0.2-0.5l1-1l0,0c-0.2,0-0.4-0.1-0.6-0.2s-0.3-0.4-0.3-0.6s0.1-0.4,0.2-0.6
				l1.3-1.6h-0.1c-0.2,0-0.4-0.1-0.6-0.2c-0.2-0.2-0.2-0.4-0.3-0.6c0-0.2,0.1-0.4,0.2-0.6l0.5-0.6l-0.4-0.5l-2.2,2.4h0.5
				c0.2,0,0.4,0.1,0.4,0.3c0.1,0.2,0.1,0.4-0.1,0.5l-1.4,1.6h0.6c0.2,0,0.4,0.1,0.5,0.3s0,0.4-0.1,0.5L361.5,68.8L361.5,68.8z"/>
                                <path className="st7" d="M371.6,74.2h-1c-0.3,0-0.5-0.2-0.5-0.5s0.2-0.5,0.5-0.5h1c0.3,0,0.5,0.2,0.5,0.5S371.9,74.2,371.6,74.2
				 M368.6,74.7h-1c-0.3,0-0.5-0.2-0.5-0.5s0.2-0.5,0.5-0.5h1l0,0c0.3,0,0.5,0.2,0.5,0.5S368.9,74.7,368.6,74.7 M365.6,74.2h-1
				c-0.3,0-0.5-0.2-0.5-0.5s0.2-0.5,0.5-0.5h1c0.3,0,0.5,0.2,0.5,0.5S365.9,74.2,365.6,74.2 M360.8,72.2c1.2,1.5,2.8,2.5,4.6,3.1
				c1.8,0.5,3.7,0.5,5.5,0s3.4-1.6,4.6-3.1H360.8L360.8,72.2z"/>
                                <path className="st7" d="M373.5,64.3h0.5l-2.2-2.4l-0.4,0.5l0.5,0.6c0.1,0.2,0.2,0.4,0.2,0.6s-0.1,0.4-0.3,0.6
				c-0.2,0.2-0.4,0.2-0.6,0.2h-0.1l1.3,1.6c0.1,0.2,0.2,0.4,0.2,0.6s-0.1,0.4-0.3,0.6c-0.2,0.1-0.4,0.2-0.6,0.2l0,0l1,1
				c0.1,0.1,0.2,0.3,0.2,0.5h1.7l-1.2-1.2c-0.1-0.1-0.2-0.4-0.1-0.5c0.1-0.2,0.3-0.3,0.5-0.3h0.6l-1.4-1.6c-0.1-0.1-0.2-0.4-0.1-0.5
				C373.2,64.4,373.3,64.3,373.5,64.3L373.5,64.3L373.5,64.3z"/>
                                <rect x="365.6" y="69.8" className="st7" width="1" height="1.5"/>
                                <rect x="369.6" y="69.8" className="st7" width="1" height="1.5"/>
                                <rect x="367.6" y="69.8" className="st7" width="1" height="1.5"/>
                                <path className="st7" d="M368.2,60.1L368.2,60.1l-2.9,3.2h0.9c0.2,0,0.4,0.1,0.5,0.3s0.1,0.4-0.1,0.5l-1.8,2.2h0.9
				c0.2,0,0.4,0.1,0.5,0.3c0.1,0.2,0,0.4-0.1,0.5l-1.6,1.6h7.5l-1.6-1.6c-0.1-0.1-0.2-0.4-0.1-0.5c0.1-0.2,0.3-0.3,0.5-0.3h0.9
				l-1.8-2.2c-0.1-0.1-0.2-0.4-0.1-0.5c0.1-0.2,0.3-0.3,0.5-0.3h0.9L368.2,60.1L368.2,60.1z"/>
                            </g>
                        </g>
                        <g id={"zdravotne_stredisko"} onMouseEnter={() => handleMouseEnter(20)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 20 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(20)}>
                            <circle className="st3" cx="518.8" cy="68.2" r="9"/>
                            <g>
                                <path className="st8" d="M523,69.7h-2.7v2.7c0,0.3-0.1,0.6-0.3,0.8c-0.2,0.2-0.5,0.3-0.8,0.3h-0.8c-0.3,0-0.6-0.1-0.8-0.3
				c-0.2-0.2-0.3-0.5-0.3-0.8v-2.7h-2.7c-0.3,0-0.6-0.1-0.8-0.3c-0.2-0.2-0.3-0.5-0.3-0.8v-0.8c0-0.3,0.1-0.6,0.3-0.8
				c0.2-0.2,0.5-0.3,0.8-0.3h2.7V64c0-0.3,0.1-0.6,0.3-0.8s0.5-0.3,0.8-0.3h0.8c0.3,0,0.6,0.1,0.8,0.3s0.3,0.5,0.3,0.8v2.7h2.7
				c0.3,0,0.6,0.1,0.8,0.3c0.2,0.2,0.3,0.5,0.3,0.8v0.8c0,0.3-0.1,0.6-0.3,0.8S523.3,69.7,523,69.7 M518.8,60.8
				c-1.9,0-3.8,0.8-5.2,2.1c-1.4,1.4-2.1,3.2-2.1,5.2s0.8,3.8,2.1,5.2c1.4,1.4,3.2,2.1,5.2,2.1s3.8-0.8,5.2-2.1
				c1.4-1.4,2.1-3.2,2.1-5.2c0-1.9-0.8-3.8-2.1-5.2C522.6,61.6,520.7,60.8,518.8,60.8L518.8,60.8L518.8,60.8z"/>
                                <path className="st8" d="M523,67.4h-3.1c-0.2,0-0.4-0.2-0.4-0.4v-3.1c0-0.1,0-0.2-0.1-0.3s-0.2-0.1-0.3-0.1h-0.8
				c-0.2,0-0.4,0.2-0.4,0.4V67c0,0.1,0,0.2-0.1,0.3c-0.1,0.1-0.2,0.1-0.3,0.1h-3.1c-0.2,0-0.4,0.2-0.4,0.4v0.8c0,0.1,0,0.2,0.1,0.3
				c0.1,0.1,0.2,0.1,0.3,0.1h3.1c0.1,0,0.2,0,0.3,0.1s0.1,0.2,0.1,0.3v3.1c0,0.1,0,0.2,0.1,0.3c0.1,0.1,0.2,0.1,0.3,0.1h0.8
				c0.1,0,0.2,0,0.3-0.1s0.1-0.2,0.1-0.3v-3.1c0-0.2,0.2-0.4,0.4-0.4h3.1c0.1,0,0.2,0,0.3-0.1s0.1-0.2,0.1-0.3v-0.8
				c0-0.1,0-0.2-0.1-0.3C523.2,67.4,523.1,67.4,523,67.4"/>
                                <path className="st8" d="M518.8,76.3c-2.1,0-4.2-0.9-5.7-2.4s-2.4-3.6-2.4-5.7s0.9-4.2,2.4-5.7c1.5-1.5,3.6-2.4,5.7-2.4
				s4.2,0.9,5.7,2.4c1.5,1.5,2.4,3.6,2.4,5.7s-0.9,4.2-2.4,5.7S520.9,76.3,518.8,76.3L518.8,76.3z M518.8,58.9
				c-2.1,0-4.2,0.7-5.9,2.1c-1.7,1.4-2.8,3.3-3.2,5.4c-0.4,2.1-0.1,4.3,0.9,6.2s2.7,3.4,4.6,4.2c0.1,0,0.1,0.1,0.1,0.1l3.1,3.5l0,0
				c0.1,0.1,0.2,0.1,0.3,0.1s0.2,0,0.3-0.1l3.1-3.5l0.1-0.1c2-0.8,3.6-2.3,4.6-4.2s1.3-4.1,0.9-6.2c-0.4-2.1-1.6-4-3.2-5.4
				C523,59.6,520.9,58.9,518.8,58.9L518.8,58.9z"/>
                            </g>
                        </g>
                        <g id={"omv"} onMouseEnter={() => handleMouseEnter(0)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 0 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(0)}>
                            <g>
                                <circle className="st3" cx="351.6" cy="207" r="8.7"/>
                                <path className="st9" d="M351.6,214.9c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C355.7,214.1,353.7,214.9,351.6,214.9L351.6,214.9z M351.6,197.9c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.9,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2
				C355.7,198.6,353.7,197.9,351.6,197.9L351.6,197.9z"/>
                                <path className="st9" d="M356.6,201.9c-1.3-1.3-3.2-2.1-5.1-2.1l0,0c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1
				s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1s2.1-3.2,2.1-5.1C358.7,205.1,358,203.2,356.6,201.9L356.6,201.9z"/>
                            </g>
                            <g>
                                <path className="st3" d="M352.9,211.2h-4.6c-0.2,0-0.4-0.2-0.4-0.4v-6.9c0-0.6,0.5-1.2,1.2-1.2h3c0.6,0,1.2,0.5,1.2,1.2v6.9
				C353.2,211,353,211.2,352.9,211.2z M348.6,210.5h3.9V204c0-0.3-0.2-0.5-0.5-0.5h-3c-0.3,0-0.5,0.2-0.5,0.5L348.6,210.5
				L348.6,210.5z"/>
                                <path className="st3"
                                      d="M352.9,206h-4.6c-0.2,0-0.4-0.2-0.4-0.4s0.2-0.4,0.4-0.4h4.6c0.2,0,0.4,0.2,0.4,0.4S353,206,352.9,206z"/>
                                <path className="st3" d="M355.4,211.2c-0.6,0-1-0.5-1-1V208c0-0.2-0.1-0.3-0.3-0.3h-1.2c-0.2,0-0.4-0.2-0.4-0.4s0.2-0.4,0.4-0.4h1.2
				c0.6,0,1,0.5,1,1v2.1c0,0.2,0.1,0.3,0.3,0.3s0.3-0.1,0.3-0.3v-4.5l-1.3-1.3c-0.1-0.1-0.1-0.4,0-0.5s0.4-0.1,0.5,0l1.4,1.4
				c0.1,0.1,0.1,0.2,0.1,0.2v4.7C356.4,210.7,355.9,211.2,355.4,211.2L355.4,211.2z"/>
                                <path className="st3" d="M356.1,207c-0.8,0-1.4-0.6-1.4-1.4c0-0.4,0.2-0.8,0.5-1.1c0.1-0.1,0.4-0.1,0.5,0s0.1,0.4,0,0.5
				c-0.2,0.1-0.2,0.3-0.2,0.5c0,0.4,0.3,0.7,0.7,0.7c0.2,0,0.4,0.2,0.4,0.4S356.2,207,356.1,207L356.1,207z"/>
                            </g>
                        </g>
                        <g id={"shell"} onMouseEnter={() => handleMouseEnter(22)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 22 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(22)}>
                            <g>
                                <circle className="st3" cx="347" cy="155.1" r="8.7"/>
                                <path className="st9" d="M347,163c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6S349.1,163,347,163L347,163z M347,146c-2.1,0-4.1,0.7-5.8,2.1c-1.6,1.3-2.7,3.2-3.1,5.2
				c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1s0.2,0,0.3-0.1l3-3.5
				l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.9,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2C351.1,146.8,349.1,146,347,146L347,146z"/>
                                <path className="st9" d="M352,150c-1.3-1.3-3.2-2.1-5.1-2.1l0,0c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1
				c1.3,1.3,3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1s2.1-3.2,2.1-5.1S353.4,151.4,352,150L352,150z"/>
                            </g>
                            <g>
                                <path className="st3" d="M348.2,159.3h-4.6c-0.2,0-0.4-0.2-0.4-0.4V152c0-0.6,0.5-1.2,1.2-1.2h3c0.6,0,1.2,0.5,1.2,1.2v6.9
				C348.6,159.2,348.4,159.3,348.2,159.3z M344,158.6h3.9v-6.5c0-0.3-0.2-0.5-0.5-0.5h-3c-0.3,0-0.5,0.2-0.5,0.5L344,158.6
				L344,158.6z"/>
                                <path className="st3" d="M348.2,154.2h-4.6c-0.2,0-0.4-0.2-0.4-0.4s0.2-0.4,0.4-0.4h4.6c0.2,0,0.4,0.2,0.4,0.4
				S348.4,154.2,348.2,154.2z"/>
                                <path className="st3" d="M350.8,159.3c-0.6,0-1-0.5-1-1v-2.1c0-0.2-0.1-0.3-0.3-0.3h-1.2c-0.2,0-0.4-0.2-0.4-0.4s0.2-0.4,0.4-0.4h1.2
				c0.6,0,1,0.5,1,1v2.1c0,0.2,0.1,0.3,0.3,0.3s0.3-0.1,0.3-0.3v-4.5l-1.3-1.3c-0.1-0.1-0.1-0.4,0-0.5s0.4-0.1,0.5,0l1.4,1.4
				c0.1,0.1,0.1,0.2,0.1,0.2v4.7C351.8,158.9,351.3,159.3,350.8,159.3L350.8,159.3z"/>
                                <path className="st3" d="M351.5,155.1c-0.8,0-1.4-0.6-1.4-1.4c0-0.4,0.2-0.8,0.5-1.1c0.1-0.1,0.4-0.1,0.5,0s0.1,0.4,0,0.5
				c-0.2,0.1-0.2,0.3-0.2,0.5c0,0.4,0.3,0.7,0.7,0.7c0.2,0,0.4,0.2,0.4,0.4S351.6,155.1,351.5,155.1L351.5,155.1z"/>
                            </g>
                        </g>
                        <g id={"busStop"} onMouseEnter={() => handleMouseEnter(2)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 2 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(2)}>
                            <g>
                                <circle className="st3" cx="367.1" cy="209.7" r="8.7"/>
                                <path className="st10"
                                      d="M367.1,217.6c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3 s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6S369.2,217.6,367.1,217.6L367.1,217.6z M367.1,200.6c-2.1,0-4.1,0.7-5.8,2.1 c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6c1,1.9,2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5c0.1,0.1,0.2,0.1,0.3,0.1 s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.9,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2 C371.2,201.4,369.2,200.6,367.1,200.6z"/>
                                <path className="st10"
                                      d="M372.1,204.6c-1.3-1.3-3.2-2.1-5.1-2.1l0,0c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1 s0.8,3.7,2.1,5.1c1.3,1.3,3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1s2.1-3.2,2.1-5.1S373.5,206,372.1,204.6L372.1,204.6z"/>
                            </g>
                            <g>
                                <path className="st11"
                                      d="M365.4,204.9h3.4c0.9,0,1.6,0.7,1.6,1.6v6.1c0,0.3-0.2,0.6-0.6,0.6h-5.5c-0.3,0-0.6-0.2-0.6-0.6v-6.1 C363.8,205.6,364.5,204.9,365.4,204.9L365.4,204.9z"/>
                                <polyline className="st11"
                                          points="362.5,208.1 362.5,206.7 371.7,206.7 371.7,208.1 			"/>
                                <path className="st3"
                                      d="M365.6,210.7c-0.3,0-0.6,0.3-0.6,0.6s0.3,0.6,0.6,0.6s0.6-0.3,0.6-0.6S365.9,210.7,365.6,210.7L365.6,210.7z"/>
                                <path className="st3"
                                      d="M368.6,210.7c-0.3,0-0.6,0.3-0.6,0.6s0.3,0.6,0.6,0.6s0.6-0.3,0.6-0.6S368.9,210.7,368.6,210.7L368.6,210.7z"/>
                                <line className="st11" x1="363.8" y1="210" x2="370.4" y2="210"/>
                                <line className="st12" x1="365.1" y1="214.1" x2="365.1" y2="213.4"/>
                                <line className="st12" x1="369.1" y1="214.1" x2="369.1" y2="213.4"/>
                            </g>
                        </g>
                        <g id={"cyklo3"} onMouseEnter={() => handleMouseEnter(21)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 21 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(21)}>
                            <path className="st3"
                                  d="M288.6,189.8c0,4.9-4,8.9-8.9,8.9s-8.9-4-8.9-8.9s4-8.9,8.9-8.9S288.6,185,288.6,189.8"/>
                            <path className="st13" d="M279.7,180.6c-2.1,0-4.2,0.7-5.9,2.1c-1.7,1.4-2.8,3.3-3.2,5.4c-0.4,2.1-0.1,4.3,0.9,6.2s2.7,3.4,4.6,4.2
			c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1c2-0.8,3.6-2.3,4.6-4.2
			s1.3-4.1,0.9-6.2c-0.4-2.1-1.6-4-3.2-5.4C284,181.3,281.9,180.6,279.7,180.6 M279.7,198c-2.2,0-4.2-0.9-5.7-2.4s-2.4-3.6-2.4-5.7
			s0.9-4.2,2.4-5.7s3.6-2.4,5.7-2.4s4.2,0.9,5.7,2.4s2.4,3.6,2.4,5.7c0,2.2-0.9,4.2-2.4,5.7C283.9,197.1,281.9,198,279.7,198"/>
                            <path className="st13" d="M284.9,184.7c-1.4-1.4-3.2-2.2-5.2-2.2l0,0c-1.9,0-3.8,0.8-5.2,2.2c-1.4,1.4-2.2,3.2-2.2,5.2
			s0.8,3.8,2.2,5.2c1.4,1.4,3.2,2.2,5.2,2.2s3.8-0.8,5.2-2.2c1.4-1.4,2.2-3.2,2.2-5.2S286.3,186,284.9,184.7"/>
                            <path className="st3" d="M283.3,192.6c-1,0-1.8-0.8-1.8-1.7h1.4c0,0.2,0.2,0.4,0.4,0.4c0.2,0,0.4-0.2,0.4-0.4s-0.2-0.4-0.4-0.4
			l-0.8-1.2c0.2-0.1,0.5-0.2,0.8-0.2c1,0,1.8,0.8,1.8,1.8S284.3,192.6,283.3,192.6 M280.8,190.6c-0.1-0.2-0.2-0.3-0.4-0.4l0.7-2.4
			l0.8,1.1c-0.5,0.4-0.9,1-0.9,1.6L280.8,190.6L280.8,190.6z M277.1,187.6h3.7l-0.8,2.5c-0.1,0-0.1,0-0.2,0.1l-2.8-2.3L277.1,187.6
			L277.1,187.6z M282.2,189.4l0.8,1.1h-1.4C281.6,190.1,281.8,189.7,282.2,189.4 M277.7,190.8c0,1-0.8,1.8-1.8,1.8s-1.8-0.8-1.8-1.8
			s0.8-1.8,1.8-1.8c0.1,0,0.2,0,0.3,0l-0.4,1.4c-0.2,0-0.4,0.2-0.4,0.4s0.2,0.4,0.4,0.4c0.2,0,0.4-0.2,0.4-0.4
			c0-0.1-0.1-0.2-0.1-0.3l0.4-1.4C277.3,189.4,277.7,190.1,277.7,190.8 M283.3,188.5c-0.4,0-0.8,0.1-1.1,0.3l-0.9-1.4l0.2-0.5h0.9
			c0.1,0,0.1,0,0.1-0.1v-0.5c0-0.1,0-0.1-0.1-0.1h-2.1c-0.1,0-0.2,0.1-0.2,0.2l1,0.4l0,0l-0.1,0.5h-3.7l0.2-0.8
			c0-0.1,0.1-0.2,0.1-0.2h0.7v-0.4h-0.7c-0.2,0-0.4,0.2-0.5,0.5l-0.6,2.2c-0.2,0-0.3-0.1-0.5-0.1c-1.3,0-2.4,1.1-2.4,2.4
			s1.1,2.4,2.4,2.4s2.4-1.1,2.4-2.4c0-1-0.6-1.9-1.5-2.2l0.1-0.4l2.6,2.2c-0.1,0.1-0.1,0.3-0.1,0.4c0,0.4,0.3,0.8,0.8,0.8
			c0.4,0,0.7-0.3,0.7-0.7h0.1c0,1.3,1.1,2.3,2.4,2.3s2.4-1.1,2.4-2.4S284.6,188.5,283.3,188.5"/>
                        </g>
                        <g id={"cyklo2"} onMouseEnter={() => handleMouseEnter(21)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 21 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(21)}>
                            <path className="st3"
                                  d="M356.1,122.4c0,4.9-4,8.9-8.9,8.9s-8.9-4-8.9-8.9s4-8.9,8.9-8.9S356.1,117.5,356.1,122.4"/>
                            <path className="st13" d="M347.3,113.1c-2.1,0-4.2,0.7-5.9,2.1c-1.7,1.4-2.8,3.3-3.2,5.4c-0.4,2.1-0.1,4.3,0.9,6.2
			c1,1.9,2.7,3.4,4.6,4.2c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1
			c2-0.8,3.6-2.3,4.6-4.2c1-1.9,1.3-4.1,0.9-6.2s-1.6-4-3.2-5.4C351.5,113.8,349.4,113.1,347.3,113.1 M347.3,130.5
			c-2.2,0-4.2-0.9-5.7-2.4s-2.4-3.6-2.4-5.7s0.9-4.2,2.4-5.7s3.6-2.4,5.7-2.4s4.2,0.9,5.7,2.4s2.4,3.6,2.4,5.7
			c0,2.2-0.9,4.2-2.4,5.7S349.4,130.5,347.3,130.5"/>
                            <path className="st13" d="M352.5,117.2c-1.4-1.4-3.2-2.2-5.2-2.2l0,0c-1.9,0-3.8,0.8-5.2,2.2c-1.4,1.4-2.2,3.2-2.2,5.2
			s0.8,3.8,2.2,5.2c1.4,1.4,3.2,2.2,5.2,2.2s3.8-0.8,5.2-2.2c1.4-1.4,2.2-3.2,2.2-5.2C354.6,120.4,353.9,118.5,352.5,117.2"/>
                            <path className="st3" d="M350.9,125.2c-1,0-1.8-0.8-1.8-1.7h1.4c0,0.2,0.2,0.4,0.4,0.4c0.2,0,0.4-0.2,0.4-0.4c0-0.2-0.2-0.4-0.4-0.4
			l-0.8-1.2c0.2-0.1,0.5-0.2,0.8-0.2c1,0,1.8,0.8,1.8,1.8S351.9,125.2,350.9,125.2 M348.4,123.1c-0.1-0.2-0.2-0.3-0.4-0.4l0.7-2.4
			l0.8,1.1c-0.5,0.4-0.9,1-0.9,1.6C348.5,123.1,348.4,123.1,348.4,123.1z M344.6,120.1h3.7l-0.8,2.5c-0.1,0-0.1,0-0.2,0.1l-2.8-2.3
			L344.6,120.1L344.6,120.1z M349.8,121.9l0.8,1.1h-1.4C349.2,122.6,349.4,122.2,349.8,121.9 M345.3,123.3c0,1-0.8,1.8-1.8,1.8
			s-1.8-0.8-1.8-1.8s0.8-1.8,1.8-1.8c0.1,0,0.2,0,0.3,0l-0.4,1.4c-0.2,0-0.4,0.2-0.4,0.4c0,0.2,0.2,0.4,0.4,0.4
			c0.2,0,0.4-0.2,0.4-0.4c0-0.1-0.1-0.2-0.1-0.3l0.4-1.4C344.8,121.9,345.3,122.6,345.3,123.3 M350.9,121c-0.4,0-0.8,0.1-1.1,0.3
			l-0.9-1.4l0.2-0.5h0.9c0.1,0,0.1,0,0.1-0.1v-0.5c0-0.1,0-0.1-0.1-0.1h-2.1c-0.1,0-0.2,0.1-0.2,0.2l1,0.4l0,0l-0.1,0.5h-3.7
			l0.2-0.8c0-0.1,0.1-0.2,0.1-0.2h0.7v-0.4h-0.7c-0.2,0-0.4,0.2-0.5,0.5L344,121c-0.2,0-0.3-0.1-0.5-0.1c-1.3,0-2.4,1.1-2.4,2.4
			s1.1,2.4,2.4,2.4s2.4-1.1,2.4-2.4c0-1-0.6-1.9-1.5-2.2l0.1-0.4l2.6,2.2c-0.1,0.1-0.1,0.3-0.1,0.4c0,0.4,0.3,0.8,0.8,0.8
			c0.4,0,0.7-0.3,0.7-0.7h0.1c0,1.3,1.1,2.3,2.4,2.3s2.4-1.1,2.4-2.4S352.2,121,350.9,121"/>
                        </g>
                        <g id={"cyklo1"} onMouseEnter={() => handleMouseEnter(21)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 21 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(21)}>
                            <path className="st3"
                                  d="M421.7,69.7c0,4.9-4,8.9-8.9,8.9s-8.9-4-8.9-8.9s4-8.9,8.9-8.9S421.7,64.8,421.7,69.7"/>
                            <path className="st13" d="M412.9,60.4c-2.1,0-4.2,0.7-5.9,2.1c-1.7,1.4-2.8,3.3-3.2,5.4c-0.4,2.1-0.1,4.3,0.9,6.2
			c1,1.9,2.7,3.4,4.6,4.2c0.1,0,0.1,0.1,0.1,0.1l3.1,3.6c0.1,0.1,0.2,0.1,0.3,0.1c0.1,0,0.2,0,0.3-0.1l3.1-3.6l0.1-0.1
			c2-0.8,3.6-2.3,4.6-4.2c1-1.9,1.3-4.1,0.9-6.2s-1.6-4-3.2-5.4C417.1,61.2,415,60.4,412.9,60.4 M412.9,77.8c-2.2,0-4.2-0.9-5.7-2.4
			s-2.4-3.6-2.4-5.7s0.9-4.2,2.4-5.7s3.6-2.4,5.7-2.4s4.2,0.9,5.7,2.4s2.4,3.6,2.4,5.7c0,2.2-0.9,4.2-2.4,5.7
			C417.1,77,415,77.8,412.9,77.8"/>
                            <path className="st13" d="M418.1,64.5c-1.4-1.4-3.2-2.2-5.2-2.2l0,0c-1.9,0-3.8,0.8-5.2,2.2c-1.4,1.4-2.2,3.2-2.2,5.2s0.8,3.8,2.2,5.2
			c1.4,1.4,3.2,2.2,5.2,2.2s3.8-0.8,5.2-2.2c1.4-1.4,2.2-3.2,2.2-5.2C420.2,67.8,419.4,65.9,418.1,64.5"/>
                            <path className="st3" d="M416.4,72.5c-1,0-1.8-0.8-1.8-1.7h1.4c0,0.2,0.2,0.4,0.4,0.4c0.2,0,0.4-0.2,0.4-0.4c0-0.2-0.2-0.4-0.4-0.4
			l-0.8-1.2c0.2-0.1,0.5-0.2,0.8-0.2c1,0,1.8,0.8,1.8,1.8S417.4,72.5,416.4,72.5 M414,70.4c-0.1-0.2-0.2-0.3-0.4-0.4l0.7-2.4
			l0.8,1.1c-0.5,0.4-0.9,1-0.9,1.6C414.1,70.4,414,70.4,414,70.4z M410.2,67.4h3.7l-0.8,2.5c-0.1,0-0.1,0-0.2,0.1l-2.8-2.3
			L410.2,67.4L410.2,67.4z M415.3,69.3l0.8,1.1h-1.4C414.7,70,415,69.6,415.3,69.3 M410.9,70.7c0,1-0.8,1.8-1.8,1.8
			s-1.8-0.8-1.8-1.8s0.8-1.8,1.8-1.8c0.1,0,0.2,0,0.3,0l-0.4,1.4c-0.2,0-0.4,0.2-0.4,0.4c0,0.2,0.2,0.4,0.4,0.4
			c0.2,0,0.4-0.2,0.4-0.4c0-0.1-0.1-0.2-0.1-0.3l0.4-1.4C410.4,69.3,410.9,69.9,410.9,70.7 M416.4,68.3c-0.4,0-0.8,0.1-1.1,0.3
			l-0.9-1.4l0.2-0.5h0.9c0.1,0,0.1,0,0.1-0.1v-0.5c0-0.1,0-0.1-0.1-0.1h-2.1c-0.1,0-0.2,0.1-0.2,0.2l1,0.4l0,0l-0.1,0.5h-3.7
			l0.2-0.8c0-0.1,0.1-0.2,0.1-0.2h0.7v-0.4h-0.7c-0.2,0-0.4,0.2-0.5,0.5l-0.6,2.2c-0.2,0-0.3-0.1-0.5-0.1c-1.3,0-2.4,1.1-2.4,2.4
			s1.1,2.4,2.4,2.4s2.4-1.1,2.4-2.4c0-1-0.6-1.9-1.5-2.2l0.1-0.4l2.6,2.2c-0.1,0.1-0.1,0.3-0.1,0.4c0,0.4,0.3,0.8,0.8,0.8
			c0.4,0,0.7-0.3,0.7-0.7h0.1c0,1.3,1.1,2.3,2.4,2.3s2.4-1.1,2.4-2.4S417.8,68.3,416.4,68.3"/>
                        </g>
                        <g id={"baseball"} onMouseEnter={() => handleMouseEnter(13)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 13 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(13)}>
                            <circle className="st3" cx="533.4" cy="318.5" r="8.5"/>
                            <g>
                                <path className="st14" d="M539.1,319c-0.2-0.1-0.3-0.3-0.5-0.4c-0.5-0.4-0.8-0.7-1.1-0.8c0.4,1.4,0.6,2.8,0.5,4.2
				C538.6,321.1,539,320.1,539.1,319L539.1,319z"/>
                                <path className="st14" d="M535.7,315.9c-0.1-0.2-0.2-0.4-0.4-0.5c-0.4,0.2-0.9,0.3-1.4,0.3s-1-0.1-1.5-0.1c-1.2-0.2-2.4-0.2-3.6,0
				c-1.1,1.6-1.3,3.5-0.6,5.3C529.3,319.4,531.7,317.3,535.7,315.9L535.7,315.9L535.7,315.9z"/>
                                <path className="st14" d="M537.2,317c0.7-0.1,1.3,0.4,1.9,0.9l0,0c-0.1-0.7-0.3-1.4-0.7-2c-0.5,0.1-1,0.2-1.5,0.4
				C536.9,316.6,537.1,316.8,537.2,317z"/>
                                <path className="st14" d="M532.5,314.8c1,0.1,1.7,0.2,2.3,0c-0.6-0.7-1.3-1.2-2.1-1.7c-1.2,0.1-2.4,0.7-3.3,1.6
				C530.5,314.6,531.5,314.6,532.5,314.8"/>
                                <path className="st14" d="M536.4,313.9c0,0.4-0.2,0.8-0.5,1c0.2,0.2,0.3,0.5,0.5,0.7c0.5-0.2,1-0.3,1.5-0.4
				C537.5,314.7,537,314.2,536.4,313.9"/>
                                <path className="st14"
                                      d="M535.4,314.3c0.2-0.2,0.3-0.5,0.2-0.8c-0.5-0.2-1-0.4-1.6-0.4C534.6,313.4,535,313.9,535.4,314.3"/>
                                <path className="st14" d="M531.7,324.1c1.9-1.4,2.6-3,3.1-4.4c0.4-1,0.8-1.9,1.6-2.4c-0.1-0.2-0.2-0.5-0.4-0.7
				c-4.2,1.5-6.5,3.7-7.6,5C529.2,322.7,530.3,323.6,531.7,324.1z"/>
                                <path className="st14" d="M533.4,326.6c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C537.5,325.8,535.5,326.6,533.4,326.6L533.4,326.6z M533.4,309.6c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6s2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5l0,0c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.8,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2
				C537.6,310.4,535.5,309.6,533.4,309.6L533.4,309.6z"/>
                                <path className="st14" d="M537.1,323c0.2-1.6,0.1-3.3-0.4-4.9c-0.5,0.4-0.8,1.1-1.2,2c-0.5,1.3-1.2,2.8-2.9,4.3
				C534.2,324.5,535.9,324.1,537.1,323L537.1,323z"/>
                                <path className="st14" d="M533.4,325.1c-1.7,0-3.3-0.7-4.5-1.9s-1.9-2.8-1.9-4.5s0.7-3.3,1.9-4.5s2.8-1.9,4.5-1.9s3.3,0.7,4.5,1.9
				c1.2,1.2,1.9,2.8,1.9,4.5s-0.7,3.3-1.9,4.5C536.8,324.4,535.1,325.1,533.4,325.1L533.4,325.1z M533.4,311.5
				c-1.9,0-3.7,0.8-5.1,2.1c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1s3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1c1.3-1.3,2.1-3.2,2.1-5.1
				s-0.8-3.7-2.1-5.1C537.1,312.3,535.3,311.5,533.4,311.5L533.4,311.5L533.4,311.5z"/>
                            </g>
                        </g>
                        <g id={"latoricka"} onMouseEnter={() => handleMouseEnter(15)}
                           onMouseLeave={() => setHovered(false)}
                           className={hovered && hoveredSection === 15 ? "cursor-pointer Pin transition-all" : "cursor-pointer transition-all"}
                           onClick={() => handleClickSection(15)}>
                            <circle className="st3" cx="593.4" cy="262.9" r="8.5"/>
                            <g>
                                <path className="st14" d="M599,263.5c-0.2-0.1-0.3-0.3-0.5-0.4c-0.5-0.4-0.8-0.7-1.1-0.8c0.4,1.4,0.6,2.8,0.5,4.2
				C598.6,265.6,598.9,264.5,599,263.5L599,263.5z"/>
                                <path className="st14" d="M595.6,260.4c-0.1-0.2-0.2-0.4-0.4-0.5c-0.4,0.2-0.9,0.3-1.4,0.3s-1-0.1-1.5-0.1c-1.2-0.2-2.4-0.2-3.6,0
				c-1.1,1.6-1.3,3.5-0.6,5.3C589.3,263.9,591.6,261.8,595.6,260.4L595.6,260.4L595.6,260.4z"/>
                                <path className="st14" d="M597.1,261.5c0.7-0.1,1.3,0.4,1.9,0.9l0,0c-0.1-0.7-0.3-1.4-0.7-2c-0.5,0.1-1,0.2-1.5,0.4
				C596.9,261,597,261.3,597.1,261.5z"/>
                                <path className="st14" d="M592.4,259.2c1,0.1,1.7,0.2,2.3,0c-0.6-0.7-1.3-1.2-2.1-1.7c-1.2,0.1-2.4,0.7-3.3,1.6
				C590.4,259,591.4,259.1,592.4,259.2"/>
                                <path className="st14" d="M596.3,258.3c0,0.4-0.2,0.8-0.5,1c0.2,0.2,0.3,0.5,0.5,0.7c0.5-0.2,1-0.3,1.5-0.4
				C597.4,259.2,596.9,258.7,596.3,258.3"/>
                                <path className="st14"
                                      d="M595.4,258.8c0.2-0.2,0.3-0.5,0.2-0.8c-0.5-0.2-1-0.4-1.6-0.4C594.5,257.9,595,258.3,595.4,258.8"/>
                                <path className="st14" d="M591.6,268.5c1.9-1.4,2.6-3,3.1-4.4c0.4-1,0.8-1.9,1.6-2.4c-0.1-0.2-0.2-0.5-0.4-0.7
				c-4.2,1.5-6.5,3.7-7.6,5C589.2,267.2,590.3,268.1,591.6,268.5z"/>
                                <path className="st14" d="M593.3,271.1c-2.1,0-4.1-0.8-5.6-2.3s-2.3-3.5-2.3-5.6s0.8-4.1,2.3-5.6s3.5-2.3,5.6-2.3s4.1,0.8,5.6,2.3
				s2.3,3.5,2.3,5.6s-0.8,4.1-2.3,5.6C597.5,270.2,595.5,271.1,593.3,271.1L593.3,271.1z M593.3,254.1c-2.1,0-4.1,0.7-5.8,2.1
				c-1.6,1.3-2.7,3.2-3.1,5.2c-0.4,2.1-0.1,4.2,0.9,6s2.6,3.3,4.5,4.1c0.1,0,0.1,0.1,0.1,0.1l3,3.5l0,0c0.1,0.1,0.2,0.1,0.3,0.1
				s0.2,0,0.3-0.1l3-3.5l0.1-0.1c1.9-0.8,3.5-2.2,4.5-4.1c1-1.8,1.3-4,0.9-6c-0.4-2.1-1.5-3.9-3.1-5.2
				C597.5,254.8,595.4,254.1,593.3,254.1L593.3,254.1z"/>
                                <path className="st14" d="M597.1,267.4c0.2-1.6,0.1-3.3-0.4-4.9c-0.5,0.4-0.8,1.1-1.2,2c-0.5,1.3-1.2,2.8-2.9,4.3
				C594.2,269,595.8,268.5,597.1,267.4L597.1,267.4z"/>
                                <path className="st14" d="M593.3,269.6c-1.7,0-3.3-0.7-4.5-1.9s-1.9-2.8-1.9-4.5s0.7-3.3,1.9-4.5s2.8-1.9,4.5-1.9s3.3,0.7,4.5,1.9
				c1.2,1.2,1.9,2.8,1.9,4.5s-0.7,3.3-1.9,4.5C596.7,268.9,595.1,269.6,593.3,269.6L593.3,269.6z M593.3,256c-1.9,0-3.7,0.8-5.1,2.1
				c-1.3,1.3-2.1,3.2-2.1,5.1s0.8,3.7,2.1,5.1s3.2,2.1,5.1,2.1s3.7-0.8,5.1-2.1c1.3-1.3,2.1-3.2,2.1-5.1s-0.8-3.7-2.1-5.1
				S595.3,256,593.3,256L593.3,256L593.3,256z"/>
                            </g>
                        </g>
                    </g>
                </svg>
            </div>
        </>
    )
}