-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1deb1
-- https://www.phpmyadmin.net/
--
-- Hostiteľ: localhost:3306
-- Čas generovania: Po 05.Feb 2024, 15:54
-- Verzia serveru: 10.11.4-MariaDB-1~deb12u1
-- Verzia PHP: 8.2.7

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Databáza: `laurindvor`
--

-- --------------------------------------------------------

--
-- <PERSON>truktúra tabuľky pre tabuľku `byty`
--

CREATE TABLE `byty` (
  `id` smallint(6) NOT NULL,
  `x` smallint(6) NOT NULL,
  `y` smallint(6) NOT NULL,
  `height` decimal(6,1) NOT NULL,
  `width` decimal(6,2) NOT NULL,
  `poschodie` tinyint(4) NOT NULL,
  `flatNumber` text NOT NULL,
  `budova` char(2) NOT NULL,
  `rooms` int(11) NOT NULL,
  `dostupnost` tinyint(4) NOT NULL,
  `vymera` decimal(7,2) NOT NULL,
  `plocha_interier` decimal(7,2) NOT NULL,
  `balkon` decimal(7,2) NOT NULL,
  `terasa` decimal(7,2) NOT NULL,
  `zahrada` decimal(7,2) NOT NULL,
  `exterier_spolu` decimal(7,2) NOT NULL,
  `price` mediumint(9) NOT NULL,
  `isL` tinyint(1) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Sťahujem dáta pre tabuľku `byty`
--

INSERT INTO `byty` (`id`, `x`, `y`, `height`, `width`, `poschodie`, `flatNumber`, `budova`, `rooms`, `dostupnost`, `vymera`, `plocha_interier`, `balkon`, `terasa`, `zahrada`, `exterier_spolu`, `price`, `isL`) VALUES
(1, 10, 90, 35.0, 15.50, 0, '1.01', 'A', 2, 1, 89.98, 51.40, 0.00, 6.15, 32.43, 38.58, 237900, 0),
(2, 270, 160, 31.0, 20.20, 0, '1.02', 'A', 2, 1, 70.80, 42.00, 0.00, 5.10, 28.80, 28.80, 179000, 0),
(3, 600, 270, 24.0, 14.00, 0, '1.03', 'A', 1, 1, 65.29, 43.79, 0.00, 8.26, 0.00, 21.50, 154000, 0),
(4, 830, 160, 31.0, 18.00, 0, '1.04', 'A', 2, 1, 58.85, 46.85, 0.00, 6.58, 5.42, 12.00, 180000, 0),
(5, 1120, 90, 35.0, 19.00, 0, '1.05', 'A', 2, 1, 32.86, 24.60, 0.00, 6.90, 14.60, 8.26, 198000, 0),
(6, 1080, 90, 36.0, 18.00, 1, '2.06', 'A', 2, 1, 52.44, 43.59, 10.06, 0.00, 0.00, 8.85, 218900, 0),
(7, 820, 160, 31.0, 17.00, 1, '2.07', 'A', 2, 1, 61.26, 51.20, 8.81, 0.00, 0.00, 10.06, 180900, 0),
(8, 600, 270, 24.0, 14.00, 1, '2.08', 'A', 1, 1, 50.81, 42.00, 6.56, 0.00, 0.00, 8.81, 119900, 0),
(9, 290, 160, 31.0, 20.00, 1, '2.09', 'A', 2, 1, 55.72, 46.85, 8.87, 0.00, 0.00, 8.87, 200900, 0),
(10, 70, 90, 36.0, 14.00, 1, '2.10', 'A', 2, 1, 31.16, 24.60, 8.85, 0.00, 0.00, 6.56, 186900, 0),
(11, 1080, 90, 36.0, 18.00, 2, '3.11', 'A', 2, 1, 53.33, 43.59, 10.96, 0.00, 0.00, 9.74, 222900, 0),
(12, 820, 160, 31.0, 17.00, 2, '3.12', 'A', 2, 1, 62.16, 51.20, 8.81, 0.00, 0.00, 10.96, 182900, 0),
(13, 600, 270, 24.0, 14.00, 2, '3.13', 'A', 1, 1, 50.81, 42.00, 6.56, 0.00, 0.00, 8.81, 122900, 0),
(14, 290, 160, 31.0, 20.00, 2, '3.14', 'A', 2, 1, 55.72, 46.85, 8.87, 0.00, 0.00, 8.87, 202900, 0),
(15, 70, 90, 36.0, 14.00, 2, '3.15', 'A', 2, 1, 31.16, 24.60, 9.74, 0.00, 0.00, 6.56, 189900, 0),
(16, 1080, 90, 36.0, 18.00, 3, '4.16', 'A', 2, 1, 62.16, 51.20, 10.96, 0.00, 0.00, 10.96, 224900, 0),
(17, 820, 160, 31.0, 17.00, 3, '4.17', 'A', 2, 1, 53.33, 43.59, 8.81, 0.00, 0.00, 9.74, 184900, 0),
(18, 600, 270, 24.0, 14.00, 3, '4.18', 'A', 1, 1, 50.81, 42.00, 6.56, 0.00, 0.00, 8.81, 123900, 0),
(19, 290, 160, 31.0, 20.00, 3, '4.19', 'A', 2, 1, 55.72, 46.85, 8.87, 0.00, 0.00, 8.87, 204900, 0),
(20, 70, 90, 36.0, 14.00, 3, '4.20', 'A', 2, 1, 31.16, 24.60, 9.74, 0.00, 0.00, 6.56, 192900, 0),
(21, 720, 110, 35.0, 44.00, 4, '5.21', 'A', 2, 1, 106.08, 47.26, 58.82, 0.00, 0.00, 58.82, 249900, 0),
(22, 1, 110, 35.0, 44.00, 4, '5.22', 'A', 2, 1, 105.35, 46.94, 58.41, 0.00, 0.00, 58.41, 249900, 0),
(23, 840, 280, 23.0, 22.00, 0, '1.01', 'B', 4, 1, 150.19, 89.75, 0.00, 6.56, 53.88, 60.44, 363900, 0),
(24, 585, 640, 17.0, 10.00, 0, '1.02', 'B', 1, 1, 42.07, 24.39, 0.00, 6.56, 52.56, 17.68, 133900, 0),
(25, 15, 280, 23.0, 22.00, 0, '1.03', 'B', 4, 1, 149.74, 90.62, 0.00, 6.60, 11.08, 59.12, 133900, 0),
(26, 800, 280, 23.0, 21.00, 1, '2.04', 'B', 4, 1, 106.77, 89.55, 17.22, 0.00, 0.00, 17.22, 335900, 0),
(27, 575, 650, 16.0, 9.00, 1, '2.05', 'B', 1, 1, 30.99, 24.39, 6.60, 0.00, 0.00, 6.60, 125900, 0),
(28, 20, 270, 23.0, 21.00, 1, '2.06', 'B', 4, 1, 106.10, 90.42, 15.68, 0.00, 0.00, 15.68, 336900, 0),
(29, 800, 280, 23.0, 21.00, 2, '3.07', 'B', 3, 1, 96.91, 81.49, 15.42, 0.00, 0.00, 15.42, 315900, 0),
(30, 485, 650, 16.0, 15.00, 2, '3.08', 'B', 2, 1, 53.72, 42.11, 11.61, 0.00, 0.00, 11.61, 196900, 0),
(31, 5, 280, 23.0, 21.00, 2, '3.09', 'B', 3, 1, 95.51, 82.36, 13.15, 0.00, 0.00, 13.15, 316900, 0),
(32, 800, 260, 23.0, 21.00, 3, '4.10', 'B', 3, 1, 96.91, 81.49, 15.42, 0.00, 0.00, 15.42, 318900, 0),
(33, 485, 650, 16.0, 15.00, 3, '4.11', 'B', 2, 1, 53.72, 42.11, 11.61, 0.00, 0.00, 11.61, 198900, 0),
(34, 5, 280, 23.0, 21.00, 3, '4.12', 'B', 3, 1, 95.51, 82.36, 13.15, 0.00, 0.00, 13.15, 319900, 0),
(35, 0, 340, 20.0, 55.00, 4, '5.13', 'B', 4, 1, 200.71, 93.02, 0.00, 107.69, 0.00, 107.69, 449900, 0),
(36, 840, 250, 24.0, 22.00, 0, '1.01', 'C', 4, 1, 150.19, 89.75, 0.00, 6.56, 52.56, 60.44, 363900, 0),
(37, 585, 625, 17.0, 10.00, 0, '1.02', 'C', 1, 1, 42.07, 24.39, 0.00, 6.56, 11.08, 17.68, 133900, 0),
(38, 23, 250, 24.0, 22.00, 0, '1.03', 'C', 4, 1, 149.74, 90.62, 0.00, 6.60, 53.88, 59.12, 365900, 0),
(39, 858, 260, 23.0, 21.00, 1, '2.04', 'C', 4, 1, 106.77, 89.55, 15.68, 0.00, 0.00, 15.68, 335900, 0),
(40, 625, 640, 16.0, 9.00, 1, '2.05', 'C', 1, 1, 30.99, 24.39, 6.60, 0.00, 0.00, 6.60, 125900, 0),
(41, 60, 255, 23.0, 22.00, 1, '2.06', 'C', 4, 1, 106.10, 90.42, 17.22, 0.00, 0.00, 17.22, 336900, 0),
(42, 860, 280, 23.0, 21.00, 2, '3.07', 'C', 3, 1, 95.51, 82.36, 13.15, 0.00, 0.00, 13.15, 316900, 0),
(43, 555, 640, 16.0, 15.00, 2, '3.08', 'C', 2, 1, 53.72, 42.11, 11.61, 0.00, 0.00, 11.61, 196900, 0),
(44, 70, 250, 23.0, 22.00, 2, '3.09', 'C', 3, 1, 96.91, 81.49, 15.42, 0.00, 0.00, 15.42, 315900, 0),
(45, 860, 280, 23.0, 21.00, 3, '4.10', 'C', 3, 1, 95.51, 82.36, 13.15, 0.00, 0.00, 13.15, 319900, 0),
(46, 555, 640, 16.0, 15.00, 3, '4.11', 'C', 2, 1, 53.72, 42.11, 11.61, 0.00, 0.00, 11.61, 198900, 0),
(47, 70, 250, 23.0, 22.00, 3, '4.12', 'C', 3, 1, 96.91, 81.49, 15.42, 0.00, 0.00, 15.42, 318900, 0),
(48, 0, 290, 21.0, 56.00, 4, '5.13', 'C', 4, 1, 200.71, 93.02, 0.00, 107.69, 0.00, 107.69, 449900, 0),
(49, 870, 250, 580.0, 530.00, 0, '1.01', 'D', 3, 1, 125.61, 66.32, 0.00, 5.55, 53.74, 59.29, 289500, 1),
(50, 710, 620, 17.0, 14.00, 0, '1.02', 'D', 2, 1, 76.15, 65.52, 0.00, 5.44, 36.01, 40.20, 194900, 0),
(51, 350, 620, 17.0, 14.00, 0, '1.03', 'D', 2, 1, 77.40, 35.95, 0.00, 4.08, 36.12, 41.45, 194800, 0),
(52, 20, 240, 580.0, 530.00, 0, '1.04', 'D', 3, 1, 126.51, 35.95, 0.00, 6.92, 54.07, 60.99, 289900, 1),
(53, 855, 280, 575.0, 490.00, 1, '2.05', 'D', 3, 1, 76.98, 66.03, 10.95, 0.00, 0.00, 10.95, 253900, 1),
(54, 710, 650, 15.0, 13.00, 1, '2.06', 'D', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 169900, 0),
(55, 380, 650, 15.0, 13.00, 1, '2.07', 'D', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 169900, 0),
(56, 80, 260, 575.0, 490.00, 1, '2.08', 'D', 3, 1, 77.58, 65.23, 12.35, 0.00, 0.00, 12.35, 250900, 1),
(57, 855, 280, 575.0, 490.00, 2, '3.09', 'D', 3, 1, 77.71, 66.03, 11.68, 0.00, 0.00, 11.68, 256900, 1),
(58, 710, 650, 15.0, 13.00, 2, '3.10', 'D', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 171900, 0),
(59, 380, 650, 15.0, 13.00, 2, '3.11', 'D', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 171900, 0),
(60, 80, 260, 575.0, 490.00, 2, '3.12', 'D', 3, 1, 78.31, 65.23, 11.68, 0.00, 0.00, 11.68, 254900, 1),
(61, 855, 280, 575.0, 490.00, 3, '4.13', 'D', 3, 1, 77.71, 66.03, 11.68, 0.00, 0.00, 11.68, 259900, 1),
(62, 710, 650, 15.0, 13.00, 3, '4.14', 'D', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 173900, 0),
(63, 380, 650, 15.0, 13.00, 3, '4.15', 'D', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 173900, 0),
(64, 80, 260, 575.0, 490.00, 3, '4.16', 'D', 3, 1, 78.31, 65.23, 11.68, 0.00, 0.00, 11.68, 256900, 1),
(65, 0, 290, 21.0, 56.00, 4, '5.17', 'D', 4, 1, 200.71, 93.02, 0.00, 107.69, 0.00, 107.69, 449900, 0),
(66, 870, 250, 580.0, 530.00, 0, '1.01', 'E', 3, 1, 126.17, 66.32, 0.00, 5.55, 54.30, 59.85, 291900, 1),
(67, 710, 650, 16.0, 14.00, 0, '1.02', 'E', 2, 1, 77.97, 35.95, 0.00, 5.44, 36.58, 42.02, 199800, 0),
(68, 355, 650, 16.0, 14.00, 0, '1.03', 'E', 2, 1, 76.72, 35.95, 0.00, 4.08, 36.69, 42.13, 199900, 0),
(69, 20, 240, 580.0, 530.00, 0, '1.04', 'E', 3, 1, 127.07, 65.52, 0.00, 6.92, 54.63, 60.18, 289900, 1),
(70, 855, 260, 575.0, 490.00, 1, '2.05', 'E', 3, 1, 76.98, 66.03, 10.95, 0.00, 0.00, 10.95, 255900, 1),
(71, 710, 650, 15.0, 13.00, 1, '2.06', 'E', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 169900, 0),
(72, 380, 650, 15.0, 13.00, 1, '2.07', 'E', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 169900, 0),
(74, 80, 260, 575.0, 490.00, 1, '2.08', 'E', 3, 1, 77.58, 65.23, 12.35, 0.00, 0.00, 12.35, 252900, 1),
(75, 855, 60, 575.0, 490.00, 2, '3.09', 'E', 3, 1, 77.71, 66.03, 11.68, 0.00, 0.00, 11.68, 259900, 1),
(76, 710, 200, 6.0, 20.00, 2, '3.10', 'E', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 169900, 0),
(77, 380, 200, 6.0, 20.00, 2, '3.11', 'E', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 169900, 0),
(78, 80, 60, 575.0, 490.00, 2, '3.12', 'E', 3, 1, 78.31, 65.23, 13.08, 0.00, 0.00, 13.08, 259900, 1),
(79, 855, 260, 575.0, 490.00, 3, '4.13', 'E', 3, 1, 77.71, 66.03, 11.68, 0.00, 0.00, 11.68, 262900, 1),
(80, 710, 650, 15.0, 13.00, 3, '4.14', 'E', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 171900, 0),
(81, 380, 650, 15.0, 13.00, 3, '4.15', 'E', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 171900, 0),
(82, 80, 260, 575.0, 490.00, 3, '4.16', 'E', 3, 1, 78.31, 65.23, 13.08, 0.00, 0.00, 13.08, 262900, 1),
(83, 0, 290, 21.0, 28.00, 4, '5.18', 'E', 2, 1, 97.20, 43.56, 0.00, 53.64, 0.00, 53.64, 249900, 0),
(84, 710, 300, 21.0, 28.00, 4, '5.18', 'E', 2, 1, 99.87, 46.23, 0.00, 53.64, 0.00, 53.64, 259900, 0),
(85, 870, 250, 580.0, 530.00, 0, '1.01', 'F', 3, 1, 126.17, 66.32, 0.00, 5.55, 54.30, 59.85, 289900, 1),
(86, 710, 650, 16.0, 14.00, 0, '1.02', 'F', 2, 1, 77.97, 35.95, 0.00, 5.44, 36.58, 42.02, 184800, 0),
(87, 355, 650, 16.0, 14.00, 0, '1.03', 'F', 2, 1, 76.72, 35.95, 0.00, 4.08, 36.69, 42.13, 184800, 0),
(88, 20, 240, 580.0, 530.00, 0, '1.04', 'F', 3, 1, 127.07, 65.52, 0.00, 6.92, 54.63, 60.18, 287900, 1),
(89, 855, 260, 575.0, 490.00, 1, '2.05', 'F', 3, 1, 76.98, 66.03, 10.95, 0.00, 0.00, 10.95, 245900, 1),
(90, 710, 650, 15.0, 13.00, 1, '2.06', 'F', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 164900, 0),
(91, 380, 650, 15.0, 13.00, 1, '2.07', 'F', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 164900, 0),
(92, 80, 260, 575.0, 490.00, 1, '2.08', 'F', 3, 1, 77.58, 65.23, 12.35, 0.00, 0.00, 12.35, 246900, 1),
(93, 855, 260, 575.0, 490.00, 2, '3.09', 'F', 3, 1, 77.71, 66.03, 11.68, 0.00, 0.00, 11.68, 249900, 1),
(94, 710, 650, 15.0, 13.00, 2, '3.10', 'F', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 167900, 0),
(95, 380, 650, 15.0, 13.00, 2, '3.11', 'F', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 167900, 0),
(96, 80, 260, 575.0, 490.00, 2, '3.12', 'F', 3, 1, 78.31, 65.23, 13.08, 0.00, 0.00, 13.08, 249900, 1),
(97, 855, 260, 575.0, 490.00, 3, '4.13', 'F', 3, 1, 77.71, 66.03, 11.68, 0.00, 0.00, 11.68, 251900, 1),
(98, 710, 650, 15.0, 13.00, 3, '4.14', 'F', 2, 1, 45.74, 35.95, 9.79, 0.00, 0.00, 9.79, 169900, 0),
(99, 380, 650, 15.0, 13.00, 3, '4.15', 'F', 2, 1, 44.41, 35.95, 8.46, 0.00, 0.00, 8.46, 169900, 0),
(100, 80, 260, 575.0, 490.00, 3, '4.16', 'F', 3, 1, 78.31, 65.23, 13.08, 0.00, 0.00, 13.08, 252900, 1),
(101, 0, 290, 21.0, 28.00, 4, '5.18', 'F', 2, 1, 97.20, 43.56, 0.00, 53.64, 0.00, 53.64, 249900, 0),
(102, 710, 300, 21.0, 28.00, 4, '5.18', 'F', 2, 1, 99.87, 46.23, 0.00, 53.64, 0.00, 53.64, 254900, 0);

--
-- Kľúče pre exportované tabuľky
--

--
-- Indexy pre tabuľku `byty`
--
ALTER TABLE `byty`
  ADD PRIMARY KEY (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
