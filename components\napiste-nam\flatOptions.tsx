import Image from "next/image";
import {useState} from "react";

interface FlatOptions {
    isMobile?: boolean,
    isOpen?: boolean,
    selectedFlat: object
}

export default function FlatOptions(props: FlatOptions) {
    const [isOpen, setIsOpen] = useState(false)

    if (props.selectedFlat === undefined) {
        return null;
    }

    return (
        <div className={"form-group hidden lg:w-1/4 flex order-last lg:order-first flex-col gap-6"}>
            {props.isMobile ? <div className={isOpen ? "bg-[#657492] text-biely" : "text-biely"}>
                <div
                    className={"klikatelneInfo mx-5 mb-10 bg-[#AEBC8D] text-center py-2 px-4 text-black flex justify-between"}
                    onClick={() => setIsOpen(!isOpen)}>
                    <Image src={props.isOpen ? "/sipka_hore.svg" : "/sipka_dole.svg"} width={28}
                           height={28} alt={"sipka"}/>
                    {isOpen ? <p>Kliknite - žiadne informácie pre nás (ak nechete, nemusíte
                            vypĺňať)</p> :
                        <p>Kliknite - voliteľné informácie pre vás (ak nechete, nemusíte
                            vypĺňať)</p>}
                    <Image src={isOpen ? "/sipka_hore.svg" : "/sipka_dole.svg"} width={28}
                           height={28} alt={"sipka"}/>
                </div>
                {isOpen && <div className={"py-6 text-2xl px-8 pb-10"}>
                    <section className={"mb-4"}>
                        <p className={"font-bold mb-2"}>Zaujíma ma byt:</p>
                        <div className="flex items-center mb-1">
                            <input id="1" type="checkbox" value="1" checked={props.selectedFlat["rooms"] === 1}
                                   name={"pocetIzieb"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="1"
                                   className="ms-2 font-normal">1-izbový</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="2" type="checkbox" value="2" checked={props.selectedFlat["rooms"] === 2}
                                   name={"pocetIzieb"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="2"
                                   className="ms-2 font-normal">2-izbový</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="3" type="checkbox" value="3" checked={props.selectedFlat["rooms"] === 3}
                                   name={"pocetIzieb"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="3"
                                   className="ms-2 font-normal">3-izbový</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="4" type="checkbox" value="4" checked={props.selectedFlat["rooms"] === 4}
                                   name={"pocetIzieb"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="4"
                                   className="ms-2 font-normal">4-izbový</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="4" type="checkbox" value="5" checked={props.selectedFlat["rooms"] === 5}
                                   name={"pocetIzieb"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="4"
                                   className="ms-2 font-normal">5-izbový</label>
                        </div>
                    </section>
                    <section className={"mb-4"}>
                        <p className={"font-bold mb-2"}>Preferujem:</p>
                        <div className="flex items-center mb-1">
                            <input id="1" type="checkbox" value="1" name={"poschodie"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="1"
                                   className="ms-2 font-normal">prízemie</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="2" type="checkbox" value="2" name={"poschodie"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="2"
                                   className="ms-2 font-normal">1. poschodie</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="3" type="checkbox" value="3" name={"poschodie"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="3"
                                   className="ms-2 font-normal">2. poschodie</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="4" type="checkbox" value="4" name={"poschodie"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="4"
                                   className="ms-2 font-normal">3. poschodie</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="5" type="checkbox" value="5" name={"poschodie"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="5"
                                   className="ms-2 font-normal">4. poschodie</label>
                        </div>
                    </section>
                    <section className={"mb-4"}>
                        <p>Páči sa mi dom:</p>
                        <div className="flex items-center mb-1">
                            <input id="A" type="checkbox" value="A" name={"budova"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="A"
                                   className="ms-2 font-normal">A</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="B" type="checkbox" value="B" name={"budova"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="B"
                                   className="ms-2 font-normal">B</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="C" type="checkbox" value="C" name={"budova"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="C"
                                   className="ms-2 font-normal">C</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="D" type="checkbox" value="D" name={"budova"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="D"
                                   className="ms-2 font-normal">D</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="E" type="checkbox" value="E" name={"budova"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="E"
                                   className="ms-2 font-normal">E</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="F" type="checkbox" value="F" name={"budova"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="F"
                                   className="ms-2 font-normal">F</label>
                        </div>
                    </section>
                    <section>
                        <p>Parkovanie typu:</p>
                        <div className="flex items-center mb-1">
                            <input id="parkingPlocha" type="checkbox" value="iba plocha"
                                   name={"parking"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="parkingPlocha"
                                   className="ms-2 text-sm font-medium">P - iba plocha</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="parkingAj" type="checkbox" value="plocha aj sklad"
                                   name={"parking"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="parkingAj"
                                   className="ms-2 text-sm font-medium">P - plocha aj sklad</label>
                        </div>
                        <div className="flex items-center mb-1">
                            <input id="parkingSeparated" type="checkbox" value={"oddelené"}
                                   name={"parking"}
                                   className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                            <label htmlFor="parkingSeparated"
                                   className="ms-2 text-sm font-medium">S - oddelené</label>
                        </div>
                    </section>
                </div>
                }
            </div> : <div className={"hidden"}>
                <section>
                    <div className="flex items-center mb-1">
                        <input id="1" type="checkbox" value="1" checked={props.selectedFlat["rooms"] === 1}
                               name={"pocetIzieb"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="1"
                               className="ms-2 font-normal">1-izbový</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="2" type="checkbox" value="2" checked={props.selectedFlat["rooms"] === 2}
                               name={"pocetIzieb"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="2"
                               className="ms-2 font-normal">2-izbový</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="3" type="checkbox" value="3" checked={props.selectedFlat["rooms"] === 3}
                               name={"pocetIzieb"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="3"
                               className="ms-2 font-normal">3-izbový</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="4" type="checkbox" value="4" checked={props.selectedFlat["rooms"] === 4}
                               name={"pocetIzieb"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="4"
                               className="ms-2 font-normal">4-izbový</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="4" type="checkbox" value="5" checked={props.selectedFlat["rooms"] === 5}
                               name={"pocetIzieb"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="4"
                               className="ms-2 font-normal">5-izbový</label>
                    </div>
                </section>
                <section>
                    <p>Preferujem:</p>
                    <div className="flex items-center mb-1">
                        <input id="1" type="checkbox" value="1" checked={props.selectedFlat["poschodie"] === 1}
                               name={"poschodie"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="1"
                               className="ms-2 text-sm font-medium">prízemie</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="2" type="checkbox" value="2" checked={props.selectedFlat["poschodie"] === 2}
                               name={"poschodie"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="2"
                               className="ms-2 text-sm font-medium">1. poschodie</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="3" type="checkbox" value="3" checked={props.selectedFlat["poschodie"] === 3}
                               name={"poschodie"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="3"
                               className="ms-2 text-sm font-medium">2. poschodie</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="4" type="checkbox" value="4" checked={props.selectedFlat["poschodie"] === 4}
                               name={"poschodie"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="4"
                               className="ms-2 text-sm font-medium">3. poschodie</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="5" type="checkbox" value="5" checked={props.selectedFlat["poschodie"] === 5}
                               name={"poschodie"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="5"
                               className="ms-2 text-sm font-medium">4. poschodie</label>
                    </div>
                </section>
                <section className={"grid grid-cols-2"}>
                    <p className={"col-span-2 mb-2"}>Páči sa mi dom:</p>
                    <div className="flex items-center mb-1">
                        <input id="A" type="checkbox" value="A" checked={props.selectedFlat["budova"] === "A"}
                               name={"budova"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="A"
                               className="ms-2 text-sm font-medium">A</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="B" type="checkbox" value="B" checked={props.selectedFlat["budova"] === "B"}
                               name={"budova"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="B"
                               className="ms-2 text-sm font-medium">B</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="C" type="checkbox" value="C" checked={props.selectedFlat["budova"] === "C"}
                               name={"budova"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="C"
                               className="ms-2 text-sm font-medium">C</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="D" type="checkbox" value="D" checked={props.selectedFlat["budova"] === "D"}
                               name={"budova"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="D"
                               className="ms-2 text-sm font-medium">D</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="E" type="checkbox" value="E" checked={props.selectedFlat["budova"] === "E"}
                               name={"budova"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="E"
                               className="ms-2 text-sm font-medium">E</label>
                    </div>
                    <div className="flex items-center mb-1">
                        <input id="F" type="checkbox" value="F" checked={props.selectedFlat["budova"] === "F"}
                               name={"budova"}
                               className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                        <label htmlFor="F"
                               className="ms-2 text-sm font-medium">F</label>
                    </div>
                </section>
                <input type={"text"} name={"zaujem"} value={"BYT"}/>
                <input type={"text"} name={"idBudova"} value={props.selectedFlat["id"]}/>
            </div>}
        </div>
    )
}

