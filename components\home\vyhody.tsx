import Image from 'next/image';

export default function Vyhody({isMobile}: { isMobile: boolean }) {
    return (
        <>
            <div className={"vyhody"}>
                <h2 className={"text-3xl lg:text-6xl px-16 lg:px-0 mb-8 font-normal text-center"}>V<PERSON><PERSON><PERSON>čne <strong>dostupné
                    výhody</strong></h2>
            </div>
            <Image src={isMobile ? "/vyhody_mobil.webp" : "/vyhody.webp"} className={"pt-0 lg:p-40"}
                   alt={"najlepší spôsob prepravy"} width={1920} quality={isMobile ? 10 : 60}
                   height={1080}/>
            {
                isMobile && <div className={"px-10 pt-10"}>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}><PERSON><PERSON><PERSON><PERSON> strechy</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Zelená fasáda na južnej strane ako ekologické chladenie</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Rekuperácia</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Podlahové vykurovanie</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Posúvne tienidlá</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Priestranné balkóny</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Množstvo zelene v areáli objektu</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Voľnočasová zóna s dospelým aj detským bazénom</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Detské ihrisko</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Vonkajší fitness</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Uzavretý cyklopoint</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Stanoviská pre bicykle</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Vonkajší fitness</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Vyvýšené záhony komunitných záhradiek</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Vonkajšie parkovanie</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Podzemné parkovanie so skladovou jednotkou, výťahom až k bytom a
                            predprípravou na elektronabíjanie</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Skvelá dostupnosť autom/MHD/pešo/bicyklom</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Blízke napojenie na diaľničný obchvat aj diaľnicu R7</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} aria-based={"what even is this?"} alt={"listocek"} width={30}
                               height={30}/>
                        <p className={"text-xl"}>Zdravé bývanie</p>
                    </div>
                    <div className={"flex gap-10 mb-4"}>
                        <Image src={"/listocek.svg"} alt={"listocek"} width={30} height={30}/>
                        <p className={"text-xl"}>Občianska vybavenosť</p>
                    </div>
                </div>
            }</>
    )
}