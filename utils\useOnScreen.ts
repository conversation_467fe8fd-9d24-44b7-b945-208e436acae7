import { useEffect, useMemo, useState } from "react";

// @ts-ignore
export default function useOnScreen(ref) {
    const [isIntersecting, setIntersecting] = useState(false);

    const observer = useMemo(() => {
        if (typeof window !== "undefined" && "IntersectionObserver" in window) {
            return new IntersectionObserver(([entry]) => setIntersecting(entry.isIntersecting));
        } else {
            return null;
        }
    }, []);

    useEffect(() => {
        if (observer && ref.current) {
            observer.observe(ref.current);
            return () => observer.disconnect();
        }
    }, [observer, ref]);

    return isIntersecting;
}
