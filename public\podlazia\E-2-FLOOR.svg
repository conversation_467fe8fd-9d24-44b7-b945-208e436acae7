<?xml version="1.0" encoding="UTF-8"?>
<svg id="b" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 706.5896 391.0867">
  <g id="c" data-name="_____OBRYSY BYTY">
    <g id="d" data-name="HAT<PERSON>">
      <polygon points="29.6515 101.4352 64.125 101.4352 152.9295 101.4352 195.091 101.4352 287.9248 101.4352 287.9248 111.7957 287.9248 151.2644 300.2587 151.2644 300.2587 159.2815 300.2587 183.9494 300.2587 191.9665 300.2587 343.6743 300.2587 348.6079 290.3103 348.6079 273.7407 348.6079 214.5377 348.6079 173.2189 348.6079 126.3498 348.6079 54.8128 348.6079 30.8222 354.0348 29.6515 354.0348 29.6515 101.4352 29.6515 101.4352" fill="#dde1eb" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="e" data-name="HATCH">
      <polygon points="294.0917 191.9665 406.3308 191.9665 412.4981 191.9665 412.4978 343.6743 412.4978 348.6079 409.868 348.6079 407.41 348.6079 405.2491 348.6079 399.4417 348.6079 394.1744 348.6079 382.8963 348.6079 323.6933 348.6079 304.3969 354.0348 299.642 354.0348 294.7084 354.0348 294.0783 354.0348 294.0917 343.6743 294.0917 198.1335 294.0917 191.9665 294.0917 191.9665" fill="#dde1eb" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="f" data-name="HATCH">
      <polygon points="406.3308 198.1346 406.3308 183.9494 406.3308 159.2815 406.3308 101.4269 415.7046 101.4269 442.0993 101.4352 479.4713 101.4352 512.4991 101.4352 553.6601 101.4352 598.0623 101.4352 642.4646 101.4352 670.8945 101.4287 672.7653 101.4287 672.7446 111.7957 672.7443 343.6743 672.7443 348.6079 666.5778 348.6079 651.7768 348.6079 580.2398 348.6079 533.3707 348.6079 492.0519 348.6079 432.8489 348.6079 411.8811 354.0348 406.9475 354.0348 406.3104 354.0348 406.3308 343.6743 406.3308 198.1346 406.3308 198.1346" fill="#dde1eb" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
  </g>
  <g id="g" data-name="88_M2 PLOCHA">
    <g id="h" data-name="HATCH">
      <polygon points="563.6814 215.7711 515.2552 215.7711 515.2243 258.0766 563.6506 258.0766 563.6814 215.7711 563.6814 215.7711" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="i" data-name="HATCH">
      <polygon points="40.012 246.606 85.0927 246.606 85.0927 244.5092 112.5974 244.5092 112.5974 343.6743 101.6819 343.6743 101.6819 346.2644 54.8128 346.2644 54.8128 343.6743 40.012 343.6743 40.012 246.606 40.012 246.606" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="j" data-name="HATCH">
      <polygon points="87.9295 111.7957 107.2938 111.7957 107.2938 109.2056 154.1629 109.2056 154.1629 111.7957 188.0196 111.7957 188.0196 185.7995 87.9295 185.7995 87.9295 111.7957 87.9295 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="k" data-name="HATCH">
      <polygon points="250.9229 111.7957 271.8906 111.7957 271.8906 127.8299 281.7578 127.8299 281.7578 154.5946 250.9229 154.5946 250.9229 111.7957 250.9229 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="l" data-name="HATCH">
      <polygon points="194.1866 157.4314 294.0917 157.4314 294.0917 191.9665 224.2815 191.9665 224.2815 215.401 194.1866 215.401 194.1866 212.9343 140.0405 212.9343 140.0405 241.6724 87.9295 241.6724 87.9295 188.6363 194.1866 188.6363 194.1866 157.4314 194.1866 157.4314" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="m" data-name="HATCH">
      <polygon points="40.012 111.7957 85.0927 111.7957 85.0927 195.6667 43.7122 195.6667 43.7122 175.9324 40.012 175.9324 40.012 111.7957 40.012 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="n" data-name="HATCH">
      <polygon points="512.4029 215.401 482.3081 215.401 477.3745 215.401 477.3745 200.6002 412.4978 200.6002 412.4978 343.6743 432.8489 343.6743 432.8489 346.2644 492.0519 346.2644 492.0519 343.6743 512.4029 343.6743 512.4029 215.401 512.4029 215.401" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="o" data-name="HATCH">
      <polygon points="591.1553 244.5092 566.4874 244.5092 566.4874 260.9134 515.2398 260.9134 515.2398 343.6743 533.3707 343.6743 533.3707 346.2644 580.2398 346.2644 580.2398 343.6743 591.1553 343.6743 591.1553 244.5092 591.1553 244.5092" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="p" data-name="HATCH">
      <polygon points="666.5775 246.606 621.4972 246.606 621.4972 244.5092 593.9921 244.5092 593.9921 343.6743 604.9077 343.6743 604.9077 346.2644 651.7768 346.2644 651.7768 343.6743 666.5775 343.6743 666.5775 246.606 666.5775 246.606" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="q" data-name="HATCH">
      <polygon points="194.1866 111.7957 248.0861 111.7957 248.0861 154.5946 194.1866 154.5946 194.1866 111.7957 194.1866 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="r" data-name="HATCH">
      <polygon points="194.1866 215.401 224.2815 215.401 229.2151 215.401 229.2151 200.6002 294.0917 200.6002 294.0917 343.6743 273.7407 343.6743 273.7407 346.2644 214.5377 346.2644 214.5377 343.6743 194.1866 343.6743 194.1866 215.401 194.1866 215.401" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="s" data-name="HATCH">
      <polygon points="142.9082 215.7711 191.3344 215.7711 191.3652 258.0766 142.939 258.0766 142.9082 215.7711 142.9082 215.7711" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="t" data-name="HATCH">
      <polygon points="662.8773 198.5035 621.4969 198.5035 621.4969 241.6724 656.7103 241.6724 656.7103 221.938 662.8773 221.938 662.8773 198.5035 662.8773 198.5035" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="u" data-name="HATCH">
      <polygon points="302.7255 200.6003 361.5431 200.6003 361.574 241.6724 310.1336 241.6724 310.1182 221.938 302.7255 221.938 302.7255 200.6003 302.7255 200.6003" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="v" data-name="HATCH">
      <polygon points="512.4029 157.4314 412.4978 157.4314 412.4981 191.9665 482.3081 191.9665 482.3081 215.401 512.4029 215.401 512.4029 212.9343 566.5491 212.9343 566.5491 241.6724 618.6601 241.6724 618.6601 188.6363 512.4029 188.6363 512.4029 157.4314 512.4029 157.4314" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="w" data-name="HATCH">
      <polygon points="455.6667 111.7957 422.365 111.7957 422.365 127.8299 412.4978 127.8299 412.4978 154.5946 455.667 154.5946 455.6667 111.7957 455.6667 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="x" data-name="HATCH">
      <polygon points="512.4029 111.7957 458.5035 111.7957 458.5035 154.5946 512.4029 154.5946 512.4029 111.7957 512.4029 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="y" data-name="HATCH">
      <polygon points="666.5775 111.7957 621.4969 111.7957 621.4969 195.6667 662.8777 195.6667 662.8777 175.9324 666.5775 175.9324 666.5775 111.7957 666.5775 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="z" data-name="HATCH">
      <polygon points="43.7122 198.5035 85.0927 198.5035 85.0927 241.6724 49.8792 241.6724 49.8792 221.938 43.7122 221.938 43.7122 198.5035 43.7122 198.5035" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="aa" data-name="HATCH">
      <polygon points="115.4343 244.5092 140.1022 244.5092 140.1022 260.9134 191.3498 260.9134 191.3498 343.6743 173.2189 343.6743 173.2189 346.2644 126.3498 346.2644 126.3498 343.6743 115.4343 343.6743 115.4343 244.5092 115.4343 244.5092" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ab" data-name="HATCH">
      <polygon points="364.3954 198.1335 406.3308 198.1335 406.3308 241.6724 391.5301 241.6724 364.3954 241.6724 364.3954 198.1335 364.3954 198.1335" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ac" data-name="HATCH">
      <polygon points="618.6601 111.7957 599.2957 111.7957 599.2957 109.2056 552.4267 109.2056 552.4267 111.7957 518.5699 111.7957 518.5699 185.7995 618.6601 185.7995 618.6601 111.7957 618.6601 111.7957" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ad" data-name="HATCH">
      <polygon points="364.3954 244.5092 364.3954 241.6724 391.5301 241.6724 391.5301 244.5092 406.3308 244.5092 406.3308 343.6743 382.8963 343.6743 382.8963 346.2644 323.6933 346.2644 323.6933 343.6743 300.2587 343.6743 300.2587 244.5092 364.3954 244.5092 364.3954 244.5092" fill="#b6bfd1" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
  </g>
  <g id="ae" data-name="59_STENA NOSNA JW">
    <g id="af" data-name="HATCH">
      <polyline points="672.7446 246.606 672.7464 343.6743 672.7443 343.6743 672.7443 343.6743 672.7443 348.6079 651.7768 348.6079 651.7771 343.6743 666.5757 343.6743 666.5775 246.606 621.4972 246.606 621.4969 241.6724 666.5775 241.6724 666.5775 111.7957 643.698 111.7957 643.698 106.8621 672.7446 106.8621 672.7446 111.7957 672.7446 111.7957 672.7446 241.6724" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ag" data-name="HATCH">
      <polygon points="621.5136 111.7957 599.2957 111.7957 599.2957 106.8621 621.4969 106.8621 621.5136 111.7957 621.5136 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ah" data-name="HATCH">
      <polygon points="552.4267 111.7957 518.5699 111.7957 518.5703 188.6363 512.4029 188.6363 512.4029 111.7957 480.7046 111.7957 480.7046 106.8621 552.4267 106.8621 552.4267 111.7957 552.4267 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ai" data-name="HATCH">
      <polygon points="440.8659 111.7957 412.4978 111.7957 412.4978 159.2815 406.3308 159.2815 406.3308 74.7938 329.8602 74.7938 329.8602 69.8603 411.2644 69.8602 411.2644 106.8621 440.8659 106.8621 440.8659 111.7957 440.8659 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="aj" data-name="HATCH">
      <polygon points="376.7293 154.9646 371.7957 154.9646 371.7957 109.3289 334.7938 109.3289 334.7938 150.031 344.661 150.031 344.661 154.9646 329.8602 154.9646 329.8602 104.3954 376.7293 104.3954 376.7293 154.9646 376.7293 154.9646" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ak" data-name="HATCH">
      <polygon points="300.2587 74.7938 295.3251 74.7938 295.3251 69.8603 300.2587 69.8602 300.2587 74.7938 300.2587 74.7938" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="al" data-name="HATCH">
      <polygon points="300.2587 102.6686 300.2587 111.7957 287.9248 111.7957 287.9248 151.2644 300.2587 151.2644 300.2587 159.2815 294.0917 159.2815 294.0917 157.4314 281.7578 157.4314 281.7578 111.7957 265.7236 111.7957 265.7236 106.8621 295.3251 106.8621 295.3251 102.6686 300.2587 102.6686 300.2587 102.6686" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="am" data-name="HATCH">
      <polygon points="225.8849 111.7957 194.1866 111.7957 194.1866 188.6363 188.0196 188.6363 188.0196 111.7957 154.1629 111.7957 154.1629 106.8621 225.8849 106.8621 225.8849 111.7957 225.8849 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="an" data-name="HATCH">
      <polygon points="107.2938 106.8621 107.2938 111.7957 85.0927 111.7958 85.0927 106.8621 107.2938 106.8621 107.2938 106.8621" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ao" data-name="HATCH">
      <polygon points="62.8916 111.7957 40.012 111.7957 40.012 241.6724 85.0927 241.6724 85.0927 246.606 40.012 246.606 40.012 343.6743 54.8128 343.6743 54.8128 348.6079 35.0785 348.6079 35.0785 106.8621 62.8916 106.8621 62.8916 111.7957 62.8916 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ap" data-name="HATCH">
      <polygon points="126.3498 343.6743 126.3498 348.6079 101.6819 348.6079 101.6819 343.6743 126.3498 343.6743 126.3498 343.6743" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="aq" data-name="HATCH">
      <polygon points="214.5377 343.6743 214.5377 348.6079 173.2189 348.6079 173.2189 343.6743 214.5377 343.6743 214.5377 343.6743" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ar" data-name="HATCH">
      <polygon points="323.6933 343.6743 323.6933 348.6079 273.7407 348.6079 273.7407 343.6743 294.0917 343.6743 294.0917 198.1335 229.2151 198.1335 229.2151 215.401 224.2815 215.401 224.2815 191.9665 294.0917 191.9665 294.0917 183.9494 300.2587 183.9494 300.2587 191.9665 366.2454 191.9665 366.2454 198.1335 300.2587 198.1335 300.2587 343.6743 323.6933 343.6743 323.6933 343.6743" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="as" data-name="HATCH">
      <polygon points="432.8489 343.6743 432.8489 348.6079 382.8963 348.6079 382.8963 343.6743 406.3308 343.6743 406.3308 198.1335 390.9134 198.1335 390.9134 191.9665 406.3308 191.9665 406.3308 183.9494 412.4978 183.9494 412.4981 191.9665 482.3081 191.9665 482.3081 215.401 477.3745 215.401 477.3745 198.1335 412.4978 198.1335 412.4978 343.6743 432.8489 343.6743 432.8489 343.6743" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="at" data-name="HATCH">
      <polygon points="533.3707 343.6743 533.3707 348.6079 492.0519 348.6079 492.0519 343.6743 533.3707 343.6762 533.3707 343.6743 533.3707 343.6743" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="au" data-name="HATCH">
      <polygon points="604.9077 343.6743 604.9077 348.6079 580.2398 348.6079 580.2398 343.6743 604.9077 343.6743 604.9077 343.6743" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="av" data-name="HATCH">
      <polygon points="297.7919 111.7957 297.7919 129.0633 287.9248 129.0633 287.9248 131.5301 297.7919 131.5301 297.7919 133.9969 300.2587 133.9969 300.2587 111.7957 297.7919 111.7957 297.7919 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="aw" data-name="HATCH">
      <polygon points="294.7084 354.0348 299.642 354.0348 299.642 391.0367 276.8242 391.0367 276.8242 386.1031 294.7084 386.1031 294.7084 354.0348 294.7084 354.0348" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ax" data-name="HATCH">
      <polygon points="411.8811 374.8384 411.8811 354.0348 406.9475 354.0348 406.9475 391.0367 429.7654 391.0367 429.7654 386.1031 411.8811 386.1031 411.8811 374.8384 411.8811 374.8384" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ay" data-name="HATCH">
      <polygon points=".05 349.3479 .05 391.0367 9.9172 391.0367 9.9172 359.2151 29.6515 359.2151 29.6515 349.3479 .05 349.3479 .05 349.3479" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="az" data-name="HATCH">
      <polygon points="706.5396 349.3479 706.5396 391.0367 696.6724 391.0367 696.6724 359.2151 676.938 359.2151 676.938 349.3479 706.5396 349.3479 706.5396 349.3479" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ba" data-name="HATCH">
      <polygon points="643.698 111.7957 666.5775 111.7957 666.5775 241.6724 621.4969 241.6724 621.4969 246.606 666.5775 246.606 666.5775 343.6743 651.7768 343.6743 651.7768 348.6079 671.5111 348.6079 671.5111 106.8621 643.698 106.8621 643.698 111.7957 643.698 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
  </g>
  <g id="bb" data-name="50_PRIECKY JW">
    <g id="bc" data-name="HATCH">
      <polygon points="29.6515 101.4352 64.125 101.4352 64.125 106.8621 35.0785 106.8621 35.0785 348.6079 56.0462 348.6079 56.0462 354.0348 29.6515 354.0348 29.6515 101.4352 29.6515 101.4352" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bd" data-name="HATCH">
      <path d="M669.6611,354.0348v-5.4269h-19.1177s0,5.4269,0,5.4269h19.1177ZM669.6611,106.8621v-5.4269h-27.1965v5.4269h27.1965Z" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="be" data-name="HATCH">
      <polygon points="83.8593 101.4352 108.5272 101.4352 108.5272 106.8621 83.8593 106.8621 83.8593 101.4352 83.8593 101.4352" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bf" data-name="HATCH">
      <path d="M266.957,105.8754h7.4004s-7.4004,0-7.4004,0h0ZM227.1183,106.8621v-5.4269h-74.1888s0,5.4269,0,5.4269h74.1888ZM264.4902,101.4352v5.4269h9.8672s20.9677,0,20.9677,0v-5.4269h-30.8349Z" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bg" data-name="HATCH">
      <polygon points="622.7303 101.4352 598.0623 101.4352 598.0623 106.8621 622.7303 106.8621 622.7303 101.4352 622.7303 101.4352" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bh" data-name="HATCH">
      <path d="M494.272,105.8754h-9.8672s0-3.7002,0-3.7002h9.8672s0,3.7002,0,3.7002h0ZM484.4048,106.8621h9.8672s59.388,0,59.388,0v-5.4269h-74.1888v5.4269h4.9336Z" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bi" data-name="HATCH">
      <polygon points="100.4485 348.6079 127.5832 348.6079 127.5832 354.0348 100.4485 354.0348 100.4485 348.6079 100.4485 348.6079" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bj" data-name="HATCH">
      <polygon points="171.9855 348.6079 215.7711 348.6079 215.7711 354.0348 171.9855 354.0348 171.9855 348.6079 171.9855 348.6079" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bk" data-name="HATCH">
      <polygon points="272.5073 348.6079 324.9267 348.6079 324.9267 354.0348 272.5073 354.0348 272.5073 348.6079 272.5073 348.6079" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bl" data-name="HATCH">
      <polygon points="381.6629 348.6079 434.0823 348.6079 434.0823 354.0348 381.6629 354.0348 381.6629 348.6079 381.6629 348.6079" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bm" data-name="HATCH">
      <polygon points="490.8185 348.6079 534.6041 348.6079 534.6041 354.0348 490.8185 354.0348 490.8185 348.6079 490.8185 348.6079" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bn" data-name="HATCH">
      <polygon points="579.0064 348.6079 606.1411 348.6079 606.1411 354.0348 579.0064 354.0348 579.0064 348.6079 579.0064 348.6079" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bo" data-name="HATCH">
      <polygon points="411.2644 101.4352 442.0993 101.4352 442.0993 106.8621 411.2644 106.8621 411.2644 101.4352 411.2644 101.4352" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bp" data-name="HATCH">
      <polygon points="140.0405 212.9343 194.1866 212.9343 194.1866 343.6743 191.3498 343.6743 191.3498 260.9134 140.1022 260.9134 140.1022 244.5092 138.8688 244.5092 138.8688 241.6724 142.939 241.6724 142.939 258.0766 191.3498 258.0766 191.3498 215.7711 142.8773 215.7711 142.8773 224.4048 140.0405 224.4048 140.0405 212.9343 140.0405 212.9343" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bq" data-name="HATCH">
      <polygon points="126.1648 185.7995 188.0196 185.7995 188.0196 188.6363 126.1648 188.6363 126.1648 185.7995 126.1648 185.7995" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="br" data-name="HATCH">
      <polygon points="87.9295 185.7995 103.9637 185.7995 103.9637 188.6363 87.9295 188.6363 87.9295 218.8546 85.0927 218.8546 85.0927 198.5035 43.7122 198.5035 43.7122 195.6667 85.0927 195.6667 85.0927 184.5661 87.9295 184.5661 87.9295 185.7995 87.9295 185.7995" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bs" data-name="HATCH">
      <polygon points="111.364 241.6724 116.6676 241.6724 116.6676 244.5092 115.4343 244.5092 115.4343 343.6743 112.5974 343.6743 112.5974 244.5092 111.364 244.5092 111.364 241.6724 111.364 241.6724" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bt" data-name="HATCH">
      <polygon points="85.0927 238.5889 87.9295 238.5889 87.9295 241.6724 89.1629 241.6724 89.1629 244.5092 85.0927 244.5092 85.0927 238.5889 85.0927 238.5889" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bu" data-name="HATCH">
      <polygon points="85.0927 111.7957 87.9295 111.7957 87.9295 164.8318 85.0927 164.8318 85.0927 111.7957 85.0927 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bv" data-name="HATCH">
      <polygon points="194.1866 154.5946 226.5016 154.5946 226.5016 157.4314 194.1866 157.4314 194.1866 154.5946 194.1866 154.5946" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bw" data-name="HATCH">
      <polygon points="246.236 154.5946 248.0861 154.5946 248.0861 108.7122 250.9229 108.7122 250.9229 154.5946 252.773 154.5946 252.773 157.4314 246.236 157.4314 246.236 154.5946 246.236 154.5946" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bx" data-name="HATCH">
      <polygon points="272.5073 154.5946 281.7578 154.5946 281.7578 157.4314 272.5073 157.4314 272.5073 154.5946 272.5073 154.5946" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="by" data-name="HATCH">
      <polygon points="40.012 221.938 40.012 224.4048 47.4124 224.4048 47.4124 241.6724 49.8792 241.6724 49.8792 221.938 40.012 221.938 40.012 221.938" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="bz" data-name="HATCH">
      <polygon points="274.3574 111.7957 271.8906 111.7957 271.8906 127.8299 281.7578 127.8299 281.7578 125.3631 274.3574 125.3631 274.3574 111.7957 274.3574 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ca" data-name="LINE">
      <line x1="87.9295" y1="238.5889" x2="87.9295" y2="218.8546" fill="#000" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cb" data-name="HATCH">
      <polygon points="300.2587 241.6724 361.5585 241.6724 361.5585 240.439 364.3954 240.439 364.3954 244.5092 300.2587 244.5092 300.2587 241.6724 300.2587 241.6724" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cc" data-name="HATCH">
      <polygon points="361.5585 198.1335 364.3954 198.1335 364.3954 220.7046 361.5586 220.7046 361.5585 198.1335 361.5585 198.1335" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cd" data-name="HATCH">
      <polygon points="391.5301 241.6724 406.3308 241.6724 406.3308 244.5092 391.5301 244.5092 391.5301 241.6724 391.5301 241.6724" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ce" data-name="HATCH">
      <polygon points="307.6591 224.4048 300.2587 224.4048 300.2587 221.938 310.1259 221.938 310.1259 241.6724 307.6591 241.6724 307.6591 224.4048 307.6591 224.4048" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cf" data-name="LINE">
      <line x1="621.4972" y1="237.3556" x2="621.4972" y2="220.0879" fill="#000" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cg" data-name="LINE">
      <line x1="621.4971" y1="183.3328" x2="621.4971" y2="166.0651" fill="#000" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ch" data-name="HATCH">
      <polygon points="518.5703 185.7995 580.4251 185.7995 580.4251 188.6363 518.5703 188.6363 518.5703 185.7995 518.5703 185.7995" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ci" data-name="HATCH">
      <polygon points="412.4981 154.5946 434.0826 154.5946 434.0826 157.4314 412.4981 157.4314 412.4981 154.5946 412.4981 154.5946" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cj" data-name="HATCH">
      <polygon points="480.0883 154.5946 512.4029 154.5946 512.4029 157.4314 480.0883 157.4314 480.0883 154.5946 480.0883 154.5946" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ck" data-name="HATCH">
      <polygon points="453.8169 154.5946 455.667 154.5946 455.667 108.7122 458.5035 108.7122 458.5038 154.5946 460.3539 154.5946 460.3539 157.4314 453.8169 157.4314 453.8169 154.5946 453.8169 154.5946" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cl" data-name="HATCH">
      <polygon points="412.4978 125.3631 419.8982 125.3631 419.8982 111.7957 422.365 111.7957 422.365 127.8299 412.4978 127.8299 412.4978 125.3631 412.4978 125.3631" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cm" data-name="HATCH">
      <polygon points="656.7103 241.6724 656.7103 221.938 666.5775 221.938 666.5775 224.4048 659.1771 224.4048 659.1771 241.6724 656.7103 241.6724 656.7103 241.6724" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cn" data-name="HATCH">
      <polygon points="512.4029 212.9343 566.5494 212.9343 566.5494 224.4048 563.7126 224.4048 563.7126 215.7711 515.2398 215.7711 515.2398 258.0766 563.6509 258.0766 563.6509 241.6724 567.7211 241.6724 567.7211 244.5092 566.4877 244.5092 566.4877 260.9134 515.2398 260.9134 515.2398 343.6743 512.4029 343.6743 512.4029 212.9343 512.4029 212.9343" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="co" data-name="HATCH">
      <polygon points="589.9222 241.6724 595.2259 241.6724 595.2259 244.5092 593.9925 244.5092 593.9921 343.6743 591.1553 343.6743 591.1556 244.5092 589.9222 244.5092 589.9222 241.6724 589.9222 241.6724" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cp" data-name="HATCH">
      <polygon points="617.427 241.6724 618.6604 241.6724 618.6604 238.5889 621.4972 238.5889 621.4972 244.5092 617.427 244.5092 617.427 241.6724 617.427 241.6724" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cq" data-name="HATCH">
      <polygon points="602.6266 185.7995 618.6604 185.7995 618.6604 184.5661 621.4972 184.5661 621.4972 195.6667 662.8777 195.6667 662.8777 198.5035 621.4972 198.5035 621.4972 218.8546 618.6604 218.8546 618.6604 188.6363 602.6266 188.6363 602.6266 185.7995 602.6266 185.7995" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cr" data-name="HATCH">
      <polygon points="618.6604 111.7957 621.4972 111.7957 621.4972 164.8318 618.6604 164.8318 618.6604 111.7957 618.6604 111.7957" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cs" data-name="HATCH">
      <polygon points="301.4921 69.8602 301.4921 6.2173 299.0253 6.2173 299.0253 69.8602 301.4921 69.8602 301.4921 69.8602" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="ct" data-name="HATCH">
      <polygon points="375.4959 69.8602 375.4959 6.2173 373.0291 6.2173 373.0291 69.8602 375.4959 69.8602 375.4959 69.8602" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cu" data-name="HATCH">
      <polygon points="290.8849 53.0864 297.7919 53.0864 297.7919 7.4507 290.8849 7.4507 290.8849 .05 302.7255 .05 302.7255 4.3673 301.4921 4.3673 301.4921 1.2834 292.1183 1.2834 292.1183 6.2173 299.0253 6.2173 299.0253 69.8603 295.3251 69.8603 295.3251 76.0272 290.8849 76.0272 290.8849 53.0864 290.8849 53.0864" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cv" data-name="HATCH">
      <polygon points="373.0291 69.8602 373.0291 67.8868 373.0291 65.42 328.6251 65.42 328.6251 69.8603 411.2644 69.8602 411.2644 101.4269 415.7046 101.4269 415.7046 65.42 376.7293 65.42 376.7293 .05 364.3954 .05 364.3954 4.3672 365.6287 4.3672 365.6287 1.2834 375.4959 1.2834 375.4959 67.8868 375.4959 69.8602 373.0291 69.8602 373.0291 69.8602" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cw" data-name="LINE">
      <line x1="85.0927" y1="183.3328" x2="85.0927" y2="166.0651" fill="#000" stroke="#000" stroke-linecap="round" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="cx" data-name="HATCH">
      <polygon points="676.938 101.4352 642.4646 101.4352 642.4646 106.8621 671.5111 106.8621 671.5111 348.6079 650.5434 348.6079 650.5434 354.0348 676.938 354.0348 676.938 101.4352 676.938 101.4352" fill="#000" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
  </g>
  <g id="cy" data-name="89_BALKONY A TERASY">
    <g id="cz" data-name="HATCH">
      <polygon points="9.9172 359.2151 29.6515 359.2151 29.6515 354.0348 56.0462 354.0348 56.0462 348.6079 100.4485 348.6079 100.4485 354.0348 127.5832 354.0348 127.5832 348.6079 171.9855 348.6079 171.9855 354.0348 215.7711 354.0348 215.7711 348.6079 272.5073 348.6079 272.5073 354.0348 294.7084 354.0348 294.7084 386.1031 276.8242 386.1031 276.8242 388.5699 9.9172 388.5699 9.9172 359.2151 9.9172 359.2151" fill="#d1bca7" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="da" data-name="HATCH">
      <polygon points="299.642 354.0348 324.9267 354.0348 324.9267 348.6079 381.6629 348.6079 381.6629 354.0348 406.9475 354.0348 406.9475 388.5699 299.642 388.5699 299.642 354.0348 299.642 354.0348" fill="#d1bca7" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
    <g id="db" data-name="HATCH">
      <polygon points="696.6451 386.1031 696.6724 359.2151 676.938 359.2151 676.938 354.0348 650.5434 354.0348 650.5434 348.6079 606.1411 348.6079 606.1411 354.0348 579.0064 354.0348 579.0064 348.6079 534.6041 348.6079 534.6041 354.0348 490.8185 354.0348 490.8185 348.6079 434.0823 348.6079 434.0823 354.0348 411.8811 354.0348 411.8811 386.1031 429.7654 386.1031 429.7654 388.5699 696.6724 388.5699 696.6451 386.1031" fill="#d1bca7" fill-rule="evenodd" stroke="#000" stroke-linejoin="round" stroke-width=".1"/>
    </g>
  </g>
</svg>