import executeQuery from "../../utils/dbConnection";

const nodemailer = require('nodemailer');

export default async function handler(req, res) {
    if (req.body.meno === "" && req.body.email === "" && req.body.phone === "" && req.body.message === "") {
        res.status(500).json({message: "Nesprávne vyplnený formulár! Vyplnte všetky povinné polia.", error: true})
    } else {
        let message;
        let clientCreationId = await createClient(req.body.meno, req.body.email, req.body.phone, req.body.message);
        let ziadostCreationId = await createZiadost(clientCreationId);
        await createComment(ziadostCreationId, clientCreationId, "Klient vyplnil formulár na stránke", "system", "formFilled")
        await updateClientIdZiadosti(ziadostCreationId, clientCreationId)
        if (req.body.message !== "") {
            await createComment(ziadostCreationId, clientCreationId, req.body.message, "klient", "clientMessage")
        }
        if (req.body.zaujem === "" || req.body.zaujem === undefined) {
        } else if (req.body.zaujem === "BYT") {
            await createZiadostDetail(ziadostCreationId, 1, parseInt(req.body.idBudova))
        } else {
            let placeP = JSON.parse(req.body.parking)
            await createZiadostDetail(ziadostCreationId, 2, parseInt(placeP.parkingNumber.replace(/\D/g, "")))
        }
        if (req.body.zaujem === "PARKING") {
            let placeP = JSON.parse(req.body.parking)
            message = {
                from: "<EMAIL>",
                to: "<EMAIL>",
                subject: "Nový záujemca o parkovacie miesto",
                text: req.body.message,
                html: `<div>
            <h2>${req.body.meno}</h2>
            <p>${req.body.email}</p>
            <p>${req.body.phone}</p>
            <p style="padding: 2rem; background-color: gainsboro; border-radius: 15px; color: black;">${req.body.message}</p>
            <hr/>
            <h3>Klient má záujem o: <strong>PARKOVACIE MIESTO</strong></h3>
            <p><strong>Parkovacie miesto č.: </strong>${placeP.parkingNumber}</p>
            <p><strong>Parkovacia sekcia: </strong>${placeP.parkingSection}</p>
            <p style="margin-bottom: 2rem"><strong>Link na konkrétne miesto: </strong><a href="https://laurindvor.com/parkovacie-miesta/${placeP.parkingNumber.replace(/\D/g, "")}">Parkovacie miesto ${placeP.parkingNumber}</a></p>
            <a style="background-color: #B4CC7A; color: black; padding: 1rem; border-radius: 10px; text-decoration: none" href="https://admin.laurindvor.com/ziadosti/detail/${ziadostCreationId}">Reagovať na žiadosť</a>
        </div>`
            }
        }
        if (req.body.zaujem === "BYT") {
            message = {
                from: "<EMAIL>",
                to: "<EMAIL>",
                subject: "Nový záujemca o byt",
                text: req.body.message,
                html: `<div>
            <h2>${req.body.meno}</h2>
            <p>${req.body.email}</p>
            <p>${req.body.phone}</p>
            <p style="padding: 2rem; background-color: gainsboro; border-radius: 15px; color: black;">${req.body.message}</p>
            <hr/>
            <h3>Klient má záujem o: </h3>
            <p><strong>Budova: </strong>${req.body.budova}</p>
            <p><strong>Poschodie: </strong>${req.body.poschodie}</p>
            <p><strong>Počet izieb: </strong>${req.body.pocetIzieb}</p>
            <p style="margin-bottom: 2rem"><strong>Link na konkrétny byt: </strong><a href="https://laurindvor.com/ponuka-bytov/${req.body.pocetIzieb}-izbove/${req.body.idBudova}">Byt ${req.body.idBudova}</a></p>
            <a style="background-color: #B4CC7A; color: black; padding: 1rem; border-radius: 10px; text-decoration: none" href="https://admin.laurindvor.com/ziadosti/detail/${ziadostCreationId}">Reagovať na žiadosť</a>
        </div>`,
            };
        } else {
            message = {
                from: "<EMAIL>",
                to: "<EMAIL>",
                subject: "Nový záujemca",
                text: req.body.message,
                html: `<div>
            <h2>${req.body.meno}</h2>
            <p>${req.body.email}</p>
            <p>${req.body.phone}</p>
            <p style="padding: 2rem; background-color: gainsboro; border-radius: 15px; color: black;">${req.body.message}</p>
            <hr/>
            <h3>Klient má záujem o: </h3>
            <p><strong>Budova: </strong>${req.body.budova}</p>
            <p><strong>Poschodie: </strong>${req.body.poschodie}</p>
            <p><strong>Počet izieb: </strong>${req.body.pocetIzieb}</p>
            <p><strong>Parking: </strong>${req.body.parking}</p>
            <a style="background-color: #B4CC7A; color: black; padding: 1rem; border-radius: 10px; text-decoration: none" href="https://admin.laurindvor.com/ziadosti/detail/${ziadostCreationId}">Reagovať na žiadosť</a>
        </div>`,
            };
        }

        let transporter = nodemailer.createTransport({
            host: "mail.polarisfinance.eu",
            port: 587,
            security: "starttls",
            auth: {
                user: "wwwlaurindvor",
                pass: "2465nhtr5g5425rge3rDG4t42",
            }
        });

        transporter.verify(function (error, success) {
            if (error) {
                console.error(error);
            } else {
            }
        });


        transporter.sendMail(message, (error, info) => {
            if (error) {
                return console.error(error);
            }
            res.status(200).json({message: "Správa bola úspešne odoslaná. Ďakujeme!", error: false})
        });
    }
}


async function createClient(meno, email, telefon, message) {
    let clientExists = false;
    let client;
    try {
        const rows = await executeQuery({
            query: 'SELECT id FROM klienti WHERE name = ? OR email = ? OR phone = ?',
            values: [meno, email, telefon]
        })
        if (rows.length > 0) {
            clientExists = true
            client = rows[0].id
        }
    } catch (error) {
        console.error('Error fetching data:', error);
    }
    if (!clientExists) {
        const datetime = new Date();
        const currentDate = datetime.toISOString().slice(0, 19).replace('T', ' ');
        try {
            const rows = await executeQuery({
                query: 'INSERT INTO klienti (name, email, phone, initialMessage, lastUpdated) VALUES(?, ?, ?, ?, ?)',
                values: [meno, email, telefon, message, currentDate]
            })
            return rows.insertId
        } catch (error) {
            console.error('Error fetching data:', error);
            return ""
        }

    }
    return client;
}

async function createComment(ziadostId, clientId, message, type, notification) {
    const datetime = new Date();
    const currentDate = datetime.toISOString().slice(0, 19).replace('T', ' ');
    try {
        const rows = await executeQuery({
            query: 'INSERT INTO komenty (idZiadosti, time, message, idKlienta, type, notification) VALUES(?, ?, ?, ?, ?, ?)',
            values: [ziadostId, currentDate, message, clientId, type, notification]
        })
        return rows.insertId
    } catch (error) {
        console.error('Error fetching data:', error);
        return ""
    }
}

async function updateClientIdZiadosti(idZiadosti, clientId) {
    try {
        const rows = await executeQuery({
            query: 'UPDATE klienti SET idZiadosti = ? WHERE id = ?',
            values: [idZiadosti, clientId]
        });
    } catch (error) {
        console.error('Error fetching data:', error);
    }
}

async function createZiadost(idKlienta) {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().slice(0, 19).replace('T', ' ');
    try {
        const rows = await executeQuery({
            query: 'INSERT INTO ziadosti (idKlient, dateCreated, stav) VALUES(?, ?, ?)',
            values: [idKlienta, formattedDate, 1]
        })
        return rows.insertId
    } catch (error) {
        console.error('Error fetching data:', error);
        return ""
    }
}

async function createZiadostDetail(idZiadosti, idTyp, idPredmet) {
    try {
        const rows = await executeQuery({
            query: 'INSERT INTO ziadost_detail (idZiadosti, idTyp, idPredmet) VALUES(?, ?, ?)',
            values: [idZiadosti, idTyp, idPredmet]
        })
        return rows.insertId
    } catch (error) {
        console.error('Error fetching data:', error);
        return ""
    }
}