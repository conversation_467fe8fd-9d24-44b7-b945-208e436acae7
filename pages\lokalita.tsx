import Image from "next/image";
import {Mo<PERSON>, Tooltip} from "flowbite-react";
import {useEffect, useRef, useState} from "react";
import LocationDetailCard from "../components/Location/locationDetailCard";
import LokalitaMap from "../components/lokalitaMap";
import Link from "next/link";
import ControlsOverlay from "../components/Location/controlsOverlay";
import useOnScreen from "../utils/useOnScreen";

export default function Lokalita({isMobile}) {
    const [isHidden, setIsHidden] = useState(true);
    const [isZoomed, setIsZoomed] = useState(true);
    const [isLocationShowed, setLocationShowed] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [locationData, setLocationData] = useState({});
    const okolie = useRef(null);
    const isVisible = useOnScreen(okolie);
    const mapa = useRef(null);

    useEffect(() => {
        setIsZoomed(!isVisible)
    }, [isVisible]);

    const handleMapChange = () => {
        if (isZoomed) {
            okolie?.current?.scrollIntoView({behavior: 'smooth'})
            setIsZoomed(false)
        } else {
            mapa?.current?.scrollIntoView({behavior: 'smooth'})
            setIsZoomed(true)
        }
    }

    const locationHandler = (section) => {
        if (isHidden && !isMobile)
            setIsHidden(false)
        if (section !== "") {
            setLocationShowed(true)
            setLocationData(section)
        }
        if (isMobile) {
            setOpenModal(true)
        }
    }

    return <>
        {isMobile ?
            <div className={"relative"}>
                <div className={"fixed bottom-28 w-full justify-center flex gap-4 z-30"}>
                    <div onClick={handleMapChange} className={"px-4 p-2 opacity-60 rounded-2xl bg-white"}>
                        {!isVisible|| isZoomed ?
                            <div className={"flex gap-2 text-[4vw] items-center"}><span>Zobraziť širšie okolie</span>
                                <svg className="w-[4.5vw] h-[4.5vw]" aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                     viewBox="0 0 24 24">
                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                          strokeWidth="2"
                                          d="M12 19V5m0 14-4-4m4 4 4-4"/>
                                </svg>
                            </div>
                            :
                            <div className={"flex gap-2 text-[4vw] items-center"}>
                                <span>Zobraziť bližšie okolie</span>
                                <svg className="w-[4.5vw] h-[4.5vw]" aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                     viewBox="0 0 24 24">
                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                          strokeWidth="2" d="M12 6v13m0-13 4 4m-4-4-4 4"/>
                                </svg>
                            </div>
                        }
                    </div>
                </div>
                <div className={"fixed px-5 top-24 w-full justify-center z-30 flex gap-4"}>
                    <div onClick={() => setIsHidden(!isHidden)}
                         className={isHidden ? "controls bg-white flex flex-col justify-center p-2 shadow-2xl rounded-2xl px-4 transition-all opacity-60" : "controls bg-white transition-all flex flex-col justify-center p-2 shadow-2xl rounded-2xl px-4 opacity-90"}>
                        <p className={"font-bold text-[4vw]"}>Zobraziť informácie o lokalite</p>
                        {!isHidden &&
                            <div
                                className={isHidden ? "flex overflow-hidden h-full flex-col gap-[1vw]" : "flex flex-col py-6 gap-[6vw]"}>
                                <div className={"flex gap-4 text-[3vw] items-center"}>
                                    <img src={"/icons/bus.png"} className={"w-[8vw]"}/>
                                    <p>Pohodlná a rýchla dostupnosť do centra mesta autom aj MHD</p>
                                </div>
                                <div className={"flex gap-4 text-[3vw] items-center"}>
                                    <img src={"/icons/road.png"} className={"w-[8vw]"}/>
                                    <p>Tesné napojenie na diaľničný obchvat aj rýchlostnú cestu R7</p>
                                </div>
                                <div className={"flex gap-4 text-[3vw] items-center"}>
                                    <img src={"/icons/health.png"} className={"w-[8vw]"}/>
                                    <p>Kompletná občianska vybavenosť - zdravotnícke zariadenia, banky, pošta</p>
                                </div>
                                <div className={"flex gap-4 text-[3vw] justify-start items-center"}>
                                    <img src={"/icons/educations_school.png"} className={"w-[8vw]"}/>
                                    <p className={"text-left"}>Jasle, škôlky aj plavecká akadémia v tesnej
                                        blízkosti </p>
                                </div>
                                <div className={"flex gap-4 text-[3vw] items-center"}>
                                    <img src={"/icons/shop.png"} className={"w-[8vw]"}/>
                                    <p>Nákupná zóna Kaufland, LIDL, BILLA</p>
                                </div>
                                <div className={"flex gap-4 text-[3vw] items-center"}>
                                    <img src={"/icons/bicykel.svg"} className={"w-[8vw]"}/>
                                    <p>Korčuľovanie, bicyklovanie či behanie na hrádzi doslova pod oknom </p>
                                </div>
                                <div className={"flex gap-4 text-[3vw] items-center"}>
                                    <img src={"/icons/park.png"} className={"w-[8vw]"}/>
                                    <p>Príroda Malého Dunaja, lesoparku Vrakuňa aj blízkych jazier v Rovinke alebo
                                        Dunajskej
                                        Lužnej</p>
                                </div>
                            </div>}
                    </div>
                    {isHidden &&
                        <Link target={"_blank"}
                              href={"https://www.google.com/maps/place/Laurin+Dvor/@48.1380618,17.189508,17z/data=!3m1!4b1!4m6!3m5!1s0x476c8ff7c41d74c1:0xcb57dfe61495534!8m2!3d48.1380582!4d17.1920829!16s%2Fg%2F11y320wgm5?entry=ttu"}
                              className={"bg-white opacity-60 p-2 px-4 shadow-2xl rounded-2xl"}>
                            <Image alt={"google maps icon"} src={"/icons/google_maps.svg"} width={32} height={32}/>
                        </Link>}

                </div>
                <div className={"overlayMobileControls relative flex justify-center"}>
                    <div ref={mapa} id={"mapa"} className={"overflow-scroll relative"}>
                        <div className={"relative h-[80vh] w-[1100px]"}>
                            <Image alt={"mapa lokality"} src={"/location/lokalita.svg"} className={"absolute top-20"}
                                   width={3000} height={2160}/>
                            <LokalitaMap isOnHomePage={false} isMobile={isMobile} locationHandler={locationHandler}/>
                        </div>
                    </div>
                </div>
                <Modal show={openModal} className={"MODALIK"} onClose={() => setOpenModal(false)}>
                    <LocationDetailCard sectionData={locationData} isMobile={true}/>
                </Modal>
                <div className={"overlayMobileControls relative flex justify-center"}>
                    <div ref={okolie} className={"relative w-full h-screen"}>
                        <Image alt={"mapa lokality"} className={"absolute top-[30%]"}
                               src={"/location/lokalita_celok.svg"}
                               width={3840}
                               height={2160}/>
                    </div>
                </div>
                <div className={"relative mb-2 bg-[#E2E2E7] z-10"}>
                    <Image alt={"mapa lokality"} src={"/location/lokalita1.webp"} width={3840} height={2160}/>
                </div>
                <div className={"relative mb-2 bg-[#E2E2E7] z-10"}>
                    <Image alt={"mapa lokality"} src={"/location/lokalita2.webp"} width={3840} height={2160}/>
                </div>
                <div className={"relative bg-[#E2E2E7] z-10"}>
                    <Image alt={"mapa lokality"} src={"/location/lokalita3.webp"} width={3840} height={2160}/>
                </div>
            </div>
            :
            <><ControlsOverlay isZoomed={isZoomed} changer={handleMapChange}/>
                <div className={"bg-[#E2E2E7] mt-16 lg:mt-20 mb-10 lg:mb-5 overflow-y-hidden"}>
                    <div ref={mapa} className={"relative flex justify-center"}>
                        <Image alt={"mapa lokality"} src={"/location/lokalita.svg"} width={isMobile ? 800 : 3840}
                               height={isMobile ? 800 : 2160}/>
                        <>
                            <div
                                className={"fixed mb-20 top-[7vw] duration-300 right-[3vw] rounded-3xl z-10 shadow-2xl cursor-pointer hover:opacity-100 opacity-65 transition-all p-[1vw] bg-white gap-4"}>
                                <Link target={"_blank"} className={"flex items-center gap-2 font-medium"}
                                      href={"https://www.google.com/maps/place/Laurin+Dvor/@48.1380618,17.189508,17z/data=!3m1!4b1!4m6!3m5!1s0x476c8ff7c41d74c1:0xcb57dfe61495534!8m2!3d48.1380582!4d17.1920829!16s%2Fg%2F11y320wgm5?entry=ttu"}>
                                    <Image alt={"google maps icon"} src={"/icons/google_maps.svg"} width={32}
                                           height={32}/>
                                    Google Maps
                                </Link>
                            </div>
                            <div onClick={() => isHidden ? setIsHidden(false) : {}}
                                 className={isHidden ? "h-[.5vw] w-[1vw] transition-all fixed top-[10vw] left-[5vw] duration-300 rounded-3xl z-10 shadow-2xl cursor-pointer hover:opacity-100 opacity-65 p-10 bg-white gap-4" : isLocationShowed ? "fixed mb-20 top-[9vw] duration-300 left-[5vw] rounded-3xl z-10 shadow-2xl cursor-pointer hover:opacity-100 opacity-100 transition-all p-[2vw] w-[25vw] bg-white gap-4" : "fixed mb-20 top-[9vw] duration-300 left-[5vw] rounded-3xl z-10 shadow-2xl cursor-pointer hover:opacity-100 opacity-65 transition-all p-[2vw] w-[25vw] bg-white gap-4"}>
                                <div className={"relative h-full"}>
                                    <div
                                        className={isHidden ? "absolute -top-[1.1vw] -left-[1.2vw] w-40 z-30" : "absolute -top-[3vw] w-40 z-30 -left-[3vw]"}>
                                        {isLocationShowed ? <Tooltip placement={"right"} content={"Zavrieť"}>
                                                <button onClick={() => setLocationShowed(false)}
                                                        className={"bg-black p-2  hover:bg-gray-700 transition-all hover:shadow-xl cursor-pointer rounded-full text-white"}>
                                                    <svg className="w-[1.5vw] h-[1.5vw]" aria-hidden="true"
                                                         xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                         fill="none"
                                                         viewBox="0 0 24 24">
                                                        <path stroke="currentColor" strokeLinecap="round"
                                                              strokeLinejoin="round"
                                                              strokeWidth="2" d="M6 18 17.94 6M18 18 6.06 6"/>
                                                    </svg>

                                                </button>
                                            </Tooltip> :
                                            <Tooltip placement={"right"}
                                                     content={isHidden ? "Zobraziť panel" : "Skryť panel"}>
                                                <button onClick={() => setIsHidden(!isHidden)}
                                                        className={"bg-black p-2  hover:bg-gray-700 transition-all hover:shadow-xl cursor-pointer rounded-full text-white"}>
                                                    {isHidden ?
                                                        <svg className="w-[1.5vw] h-[1.5vw] text-white"
                                                             aria-hidden="true"
                                                             xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                             fill="none"
                                                             viewBox="0 0 24 24">
                                                            <path stroke="currentColor" strokeWidth="2"
                                                                  d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"/>
                                                            <path stroke="currentColor" strokeWidth="2"
                                                                  d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                                        </svg>
                                                        : <svg className="w-[1.5vw] h-[1.5vw] text-white"
                                                               aria-hidden="true"
                                                               xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                               fill="none"
                                                               viewBox="0 0 24 24">
                                                            <path stroke="currentColor" strokeLinecap="round"
                                                                  strokeLinejoin="round"
                                                                  strokeWidth="2"
                                                                  d="M3.933 13.909A4.357 4.357 0 0 1 3 12c0-1 4-6 9-6m7.6 3.8A5.068 5.068 0 0 1 21 12c0 1-3 6-9 6-.314 0-.62-.014-.918-.04M5 19 19 5m-4 7a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                                                        </svg>}
                                                </button>
                                            </Tooltip>}
                                    </div>
                                    {isLocationShowed ?
                                        <LocationDetailCard sectionData={locationData} isMobile={false}/> :
                                        <div
                                            className={isHidden ? "flex overflow-hidden h-full flex-col gap-[1vw]" : "flex flex-col gap-[1vw]"}>
                                            <div className={"flex gap-[2vw] text-[.9vw] items-center"}>
                                                <img src={"/icons/bus.png"} className={"w-[3vw]"}/>
                                                <p>Pohodlná a rýchla dostupnosť do centra mesta autom aj MHD</p>
                                            </div>
                                            <div className={"flex gap-[2vw] text-[.9vw] items-center"}>
                                                <img src={"/icons/road.png"} className={"w-[3vw]"}/>
                                                <p>Tesné napojenie na diaľničný obchvat aj rýchlostnú cestu R7</p>
                                            </div>
                                            <div className={"flex gap-[2vw] text-[.9vw] items-center"}>
                                                <img src={"/icons/health.png"} className={"w-[3vw]"}/>
                                                <p>Kompletná občianska vybavenosť - zdravotnícke zariadenia, banky,
                                                    pošta</p>
                                            </div>
                                            <div className={"flex gap-[2vw] text-[.9vw] justify-start items-center"}>
                                                <img src={"/icons/educations_school.png"} className={"w-[3vw]"}/>
                                                <p className={"text-left"}>Jasle, škôlky aj plavecká akadémia v tesnej
                                                    blízkosti </p>
                                            </div>
                                            <div className={"flex gap-[2vw] text-[.9vw] items-center"}>
                                                <img src={"/icons/shop.png"} className={"w-[3vw]"}/>
                                                <p>Nákupná zóna Kaufland, LIDL, BILLA</p>
                                            </div>
                                            <div className={"flex gap-[2vw] text-[.9vw] items-center"}>
                                                <img src={"/icons/bicykel.svg"} className={"w-[3vw]"}/>
                                                <p>Korčuľovanie, bicyklovanie či behanie na hrádzi doslova pod
                                                    oknom </p>
                                            </div>
                                            <div className={"flex gap-[2vw] text-[.9vw] items-center"}>
                                                <img src={"/icons/park.png"} className={"w-[3vw]"}/>
                                                <p>Príroda Malého Dunaja, lesoparku Vrakuňa aj blízkych jazier v Rovinke
                                                    alebo
                                                    Dunajskej
                                                    Lužnej</p>
                                            </div>
                                        </div>}

                                </div>
                            </div>
                            <LokalitaMap isOnHomePage={false} locationHandler={locationHandler}/></>
                    </div>
                    <div ref={okolie} className={"relative bg-[#E2E2E7] z-10"}>
                        <Image alt={"mapa lokality"} src={"/location/lokalita_celok.svg"} width={3840} height={2160}/>
                    </div>
                    <div className={"relative bg-[#E2E2E7] z-10"}>
                        <Image alt={"mapa lokality"} src={"/location/lokalita1.webp"} width={3840} height={2160}/>
                    </div>
                    <div className={"relative bg-[#E2E2E7] z-10"}>
                        <Image alt={"mapa lokality"} src={"/location/lokalita2.webp"} width={3840} height={2160}/>
                    </div>
                    <div className={"relative bg-[#E2E2E7] z-10"}>
                        <Image alt={"mapa lokality"} src={"/location/lokalita3.webp"} width={3840} height={2160}/>
                    </div>
                </div>
            </>
        }
    </>
}