//@ts-nocheck
import { useGLTF } from "@react-three/drei";
import {Ivy} from "./Ivy";

export function BuildingModel(props) {
    const { nodes, materials } = useGLTF("/F-B-opt.glb");
    if(props.number === 0){
        return null;
    } else {
        return (
            <group position={props.position} rotation={props.rotation} dispose={null}>
                <group
                    scale={0.5}
                    rotation={[Math.PI / 2, Math.PI, Math.PI / 2]}
                >
                    <mesh
                        geometry={nodes.mesh_0["geometry"]}
                        material={materials["zabradli.001"]}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_1["geometry"]}
                        material={materials["OMITKA.006"]}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_2["geometry"]}
                        material={materials["atika.001"]}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_3["geometry"]}
                        material={materials.DrevoAtika}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_4["geometry"]}
                        material={materials["beton.001"]}
                    />
                    <mesh
                        geometry={nodes.mesh_0_5["geometry"]}
                        material={materials["Material #-2147483567.071"]}
                    />
                    <mesh
                        receiveShadow
                        geometry={nodes.mesh_0_6["geometry"]}
                        material={materials["OKNA.007"]}
                    />
                    <mesh
                        receiveShadow
                        geometry={nodes.mesh_0_7["geometry"]}
                        material={materials["OKNA.008"]}
                    />
                    <mesh
                        geometry={nodes.mesh_0_8["geometry"]}
                        material={materials["Material #-2147483567.072"]}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_9["geometry"]}
                        material={materials.DrevoVpredu}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_10["geometry"]}
                        material={materials.DrevoAtika2}
                    />
                    <mesh
                        castShadow receiveShadow
                        geometry={nodes.mesh_0_11["geometry"]}
                        material={materials.Travichka}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_12["geometry"]}
                        material={materials.BACKPLATE}
                    />
                    <mesh
                        castShadow
                        receiveShadow
                        geometry={nodes.mesh_0_13["geometry"]}
                        material={materials.MetalCladding}
                    />
                </group>
                <group>
                    <Ivy position={[-10.6, 6, -2.6]}
                         rotation={[1.571, 0.009, 1.577]}
                         scale={[2.3, 0.5, 1.7]}/>
                </group>
            </group>
        );
    }
}

useGLTF.preload("/F-B-opt.glb");