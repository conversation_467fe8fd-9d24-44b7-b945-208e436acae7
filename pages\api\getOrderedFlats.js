import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {floor} = req.query;
        const {building} = req.query;
        const {rooms} = req.query;
        const {ordering} = req.query;
        const {stav} = req.query;
        const {orderBy} = req.query;
        const isVariant = rooms.includes(5) ? "1" : "0";
        let query;
        let count;
        if (floor === "" && building === "" && rooms === "" && stav === "") {
            query = "SELECT * FROM byty ORDER BY " + orderBy + " " + ordering;
            count = "SELECT COUNT(*) as count FROM byty WHERE isVariant = 0 ORDER BY " + orderBy + " " + ordering;
        } else {
            query = "SELECT * FROM byty WHERE" + floor + building + rooms + stav + " ORDER BY " + orderBy + " " + ordering;
            query = query.replace(/\bAND\b/, '');
            count = "SELECT COUNT(*) as count FROM byty WHERE" + floor + building + rooms + stav + " AND isVariant = " + isVariant + " ORDER BY " + orderBy + " " + ordering;
            count = count.replace(/\bAND\b/, '');
        }
        const rows = await executeQuery({
            query: query
        });
        const countQuery = await executeQuery({
            query: count 
        });
        res.status(200).json({data: rows, count: countQuery});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}
