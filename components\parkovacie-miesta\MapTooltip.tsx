import {Badge} from "flowbite-react";

export default function MapTooltip(props: any) {
    let flat: object;
    if (props.flat != undefined) {
        flat = JSON.parse(props.flat);
    } else {
        return null;
    }
    return (
        <div className="max-w-sm">
            <div className={"flex gap-8 px-2 justify-between"}>
                <div className={"flatNo w-full"}>
                    <small>{flat["section"] === "VS" ? "Číslo skladového priestoru" : "Číslo parkovacieho miesta:"}</small>
                    <h2 className="text-2xl"><strong>{flat["parking_number"]}</strong></h2>
                </div>
            </div>
            <hr className="my-2"/>
            <div className={"flex items-center gap-8 px-2 justify-between"}>
                <small>Dostupnosť: </small>
                <Badge color="success">{flat["dostupnost"] === 1 ? <span>Dostupný</span> :
                    <span>Predaný</span>}</Badge>
            </div>
            <hr className="my-2"/>
            <div className={"flex items-center gap-8 px-2 justify-between"}>
                <small className={"align-self-left"}>Výmera: </small>
                <div className={"text-center"}>
                    <h2 className="text-xl">{flat["celkova_vymera"]}m²</h2>
                </div>
            </div>
            <hr className="my-2"/>
            <div className={"flex items-center px-2 justify-between"}>
                <small className={"align-self-left"}>Celková cena: </small>
                <div className={"text-center"}>
                    <h2 className="text-xl">{flat["price"].toLocaleString().replace(/,/g, ' ')} €</h2>
                </div>
            </div>
        </div>
    )
}