import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {building} = req.query;
        let numberOfFlats = [];

        for (let i = 0; i < 5; i++) {
            const pocet = await executeQuery({
                query: 'SELECT COUNT(id) AS id FROM byty WHERE budova = ? AND poschodie = ? AND dostupnost = 1',
                values: [building, i+1]
            })
            numberOfFlats.push(pocet[0].id)
        }

        res.status(200).json({data: numberOfFlats});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}