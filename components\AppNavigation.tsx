import {AnimatePresence, motion} from "framer-motion";
import Image from "next/image";
import Link from "next/link";
import {useRouter} from "next/router";
import {useEffect, useState} from "react";

export default function AppNavigation() {
    const router = useRouter()
    const [flatPick, setFlatPick] = useState(false)
    const [isHome, setIsHome] = useState(false)
    const [pageHeading, setPageHeading] = useState("");
    const [colorChange, setColorchange] = useState(false);
    const [hide, setHide] = useState(false)
    const [showLinks, setShowLinks] = useState(false)
    let [easterEgg, setEasterEgg] = useState(0);

    const changeNavbarColor = () => {
        if (window.scrollY >= 10) {
            setColorchange(true);
        } else {
            setColorchange(false);
        }
    };

    useEffect(() => {
        if (showLinks) {
            document.body.style.overflowY = "hidden";
        } else {
            document.body.style.overflowY = "auto";
        }
    }, [showLinks]);

    useEffect(function onFirstMount() {
        window.addEventListener("scroll", changeNavbarColor);
    }, []);

    useEffect(() => {
        setHide(false)
        if (router.asPath.match(/\/ponuka-bytov\/(\d+)/) !== null) {
            if (!router.asPath.includes("3d-vyber")) {
                setPageHeading(router.asPath.match(/\/ponuka-bytov\/(\d+)/)[1] + "-izbové byty")
            }
        }
        if (router.pathname === "/ponuka-bytov") {
            setFlatPick(true)
        }
        if (router.pathname === "/") {
            setIsHome(true)
        } else {
            setIsHome(false)
        }
        if (router.pathname === "/lokalita") {
            setPageHeading("Lokalita")
        }
        if (router.pathname === "/developer") {
            setPageHeading("Developer")
        }
        if (router.pathname === "/technicke-specifikacie") {
            setPageHeading("Technické špecifikácie")
        }
        if (router.pathname === "/ponuka-bytov/prehlad-bytov") {
            setPageHeading("Prehľad bytov")
        }
        if (router.pathname === "/harmonogram") {
            setPageHeading("Harmonogram stavby")
        }
        if (router.pathname === "/galeria") {
            setPageHeading("Galéria")
        }
        if (router.pathname === "/3d-vyber") {
            setPageHeading("3D Výber")
        }
        if (router.pathname.includes("aktuality")) {
            setPageHeading("Aktuality")
        }
        if (router.pathname.includes("parkovacie-miesta")) {
            setPageHeading("Parkovacie miesta a sklady")
        }
        if (router.asPath.includes("vsetky-byty")) {
            setPageHeading("Všetky byty")
        }
        if (router.asPath.includes("financovanie")) {
            setPageHeading("Financovanie")
        }
        if (router.pathname === "/napiste-nam" || router.pathname.includes("admin")) {
            setHide(true)
        }
    }, [router.pathname, router.asPath])

    if (hide) {
        return null;
    }

    return (
        <>
            <div className={"relative md:hidden"}>
                <nav
                    className={"flex fixed top-0 z-50 shadow-xl w-full rounded-b-xl bg-white items-center transition-all justify-between py-2 px-4"}>
                    <Link href={"/"} onClick={() => setShowLinks(false)}>
                        <Image src={"/logo.svg"} width={100} height={250} priority
                               alt={"Laurin Dvor Logo"}/>
                    </Link>
                    <div className={"flex gap-4 items-center"}>
                        <Image src={"/listocek.svg"} className={"burgerMenu"} width={80} height={250}
                               alt={"Laurin Dvor Logo"}/>
                        <button onClick={() => setShowLinks(!showLinks)}>
                            {showLinks ?
                                <svg className="w-12 h-12 text-gray-800" aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                          strokeWidth="2.2" d="M6 18 18 6m0 12L6 6"/>
                                </svg>
                                :
                                <svg className="w-12 h-12 text-gray-800" aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" strokeLinecap="round" strokeWidth="2.2"
                                          d="M5 7h14M5 12h14M5 17h14"/>
                                </svg>
                            }
                        </button>
                    </div>
                </nav>
                <AnimatePresence>
                    {showLinks ?
                        <motion.div key={"hovno"}>
                            <motion.div key={"backdrop"}
                                        className={"w-screen h-screen bg-black absolute bottomNavigation"}
                                        initial={{opacity: 0}}
                                        animate={{opacity: 0.9}} exit={{opacity: 0}} style={{zIndex: "30"}}>
                            </motion.div>
                            <motion.div key={"navigation"} initial={{width: 0}} animate={{width: "80vw"}}
                                        exit={{width: 0}}
                                        className={"mobileMenu w-full right-0 shadow-xl top-0 -mt-10 overflow-y-scroll fixed h-screen bg-[#E1E5ED]"}
                                        style={{zIndex: "40"}}>
                                <div className={"links flex w-full flex-col text-white pt-8 pb-5 px-5"}>
                                    <Link href="/lokalita" className={"shadow-xl"}
                                          onClick={() => setShowLinks(!showLinks)}><span>LOKALITA</span>
                                    </Link>
                                    <Link href="/ponuka-bytov/vsetky-byty" className={"shadow-xl"}
                                          onClick={() => setShowLinks(!showLinks)}>BYTY
                                    </Link>
                                    <Link href="/financovanie" className={"shadow-xl"}
                                          onClick={() => setShowLinks(!showLinks)}>FINANCOVANIE
                                    </Link>
                                    <Link href={"/galeria"} onClick={() => setShowLinks(!showLinks)}>GALÉRIA</Link>
                                    <Link href="/aktuality"
                                          onClick={() => setShowLinks(!showLinks)}>AKTUALITY</Link>
                                    <Link href="/vystavba-live"
                                          onClick={() => setShowLinks(!showLinks)}>Výstavba</Link>
                                </div>
                            </motion.div>
                        </motion.div> : <></>
                    }</AnimatePresence>
            </div>
            <div
                className={colorChange ? "bg-[#E2E2E7] hidden md:block fixed top-0 w-full z-50 shadow-lg" : "bg-transparent fixed hidden md:block top-0 w-full z-50"}>
                <nav onClick={() => setEasterEgg(easterEgg => easterEgg + 1)}
                     className={isHome ? "flex items-center gap-6 py-4 justify-between px-8" : easterEgg === 25 ? "flex py-4 bg-[url('/rainbow.jpg')] text-white items-center fixed top-0 w-full z-20 gap-6 justify-between px-8" : "flex bg-white items-center fixed top-0 w-full z-20 gap-6 justify-between py-2 px-8"}>
                    {isHome ? <div className="flex items-center gap-10">
                        <Link href={"/"}>
                            <Image src={"/logo.svg"} width={150} height={250} alt={"Laurin Dvor Logo"}/>
                        </Link>
                        <h1 className={"navH text-[#683107]"}>Výnimočne <strong>dostupné bývanie</strong></h1>
                    </div> : <>
                        <div className="flex items-center gap-20">
                            <Link href={"/"}>
                                <Image src={"/logo.svg"} width={150} height={250} alt={"Laurin Dvor Logo"}/>
                            </Link>
                            <div className="flex items-center gap-8">
                                <Image src={"/sipka_lava.svg"} className={"sipocka"} alt={"šípka"} width={45}
                                       height={25}/>
                                <h1 className={"appNavigationHeading text-[#657492] font-light -mt-1"}>
                                    <span className={"text-[2vw]"}>{pageHeading}</span></h1>
                                <Image src={"/sipka_prava.svg"} className={"sipocka"} alt={"šípka"} width={45}
                                       height={25}/>
                            </div>
                        </div>

                    </>
                    }
                    <div
                        className={!colorChange && isHome ? "linksC text-white flex gap-4" : "links text-white flex gap-4"}>
                        <div className="dropdownWrapper">
                            <Link href={"/lokalita"}>Lokalita</Link>
                        </div>
                        <div className="dropdownWrapper">
                            <Link href="/ponuka-bytov/vsetky-byty" className={"flex gap-2"}>Byty</Link>
                        </div>
                        <div className="dropdownWrapper">
                            <Link href={"/financovanie"}>Financovanie</Link>
                        </div>
                        <div className="dropdownWrapper">
                            <Link href={"/galeria"}>Galéria</Link>
                        </div>
                        <div className="dropdownWrapper">
                            <Link href="/aktuality">Aktuality</Link>
                        </div>
                        <div className="dropdownWrapper !text-left">
                            <Link className={"relative text-left"} href="/vystavba-live">Výstavba <span className={"absolute shadow-lg text-xs flex items-center gap-1 -top-3.5 p-1 text-white rounded-lg -right-3 bg-gray-500"}><i
                                className={"w-2 h-2 block animate-pulse rounded-full bg-red-500"}></i>Naživo</span></Link>
                        </div>
                    </div>
                </nav>
            </div>
        </>
    );
}