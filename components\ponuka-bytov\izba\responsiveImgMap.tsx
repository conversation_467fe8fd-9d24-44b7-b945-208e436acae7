import Image from "next/image";
import {useEffect, useState} from "react";
import Router, {useRouter} from "next/router";
import toast from "react-hot-toast";

export default function ResponsiveImgMap({
                                             getter,
                                             predmetZaujmu,
                                             numberOfFlats,
                                             room,
                                             selectedBuilding,
                                             isMobile,
                                             isFlatDetail
                                         }) {
    const [selectedFlat, setSelectedFlat] = useState<string>("")
    const [imageLoaded, setImageLoaded] = useState<boolean>(false)
    const [hoveredBuilding, setHoveredBuilding] = useState("")

    const router = useRouter()
    const budovy = ['F', 'E', 'D', 'C', 'B', 'A'];
    const getXAxis = (value: number, mapWidth: number): number => {
        if (imageLoaded) {
            return value / mapWidth * document.getElementById("floorImage").clientWidth
        }
        return value;
    }
    const getYAxis = (value: number, mapHeight: number): number => {
        if (imageLoaded) {
            return value / mapHeight * document.getElementById("floorImage").clientHeight
        }
        return value;
    }

    useEffect(() => {
        const query = router.asPath.split('?')[1];
        let values;
        let keyValuePairs;
        if (query !== undefined) {
            keyValuePairs = query.split('&');
            values = keyValuePairs.map(pair => pair.split('=')[1]);
            if (router.asPath.includes("=")) {
                setSelectedFlat(values[0])
            }
        } else {
            setSelectedFlat("")
        }
    }, [router.asPath])

    useEffect(() => {
        if (selectedFlat !== "") {
            if (!isFlatDetail) {
                getter(selectedFlat);
            }
            if (router.asPath.match(/byt=(\d+)/) !== null) {
                router.back();
            }
        }
    }, [selectedFlat])

    return (
        <>
            <div className={"flex relative flex-col items-center lg:px-5 justify-center"}>
                {predmetZaujmu === "parking" ?
                    <><Image id={"floorImage"}
                             src={selectedFlat ? "/parkovacie-miesta/mala situ_vnutorne_" + selectedFlat + ".svg" : "/parkovacie-miesta/mapka_map.png"}
                             alt={"situace"}
                             width={704}
                             height={200} onLoad={() => setImageLoaded(true)} useMap={"#image-mapaa"}/>
                        <map name="image-mapaa">
                            <area target="" alt="fButton" title="fButton" onClick={() => setSelectedFlat("V")}
                                  coords={getXAxis(0, 944) + "," + getYAxis(56, 203) + "," + getXAxis(103, 944) + "," + getYAxis(203, 203)}
                                  shape="rect"/>
                            <area target="" alt="fButton" title="fButton" onClick={() => setSelectedFlat("1")}
                                  coords={getXAxis(119, 944) + "," + getYAxis(72, 203) + "," + getXAxis(324, 944) + "," + getYAxis(187, 203)}
                                  shape="rect"/>
                            <area target="" alt="fButton" title="fButton" onClick={() => setSelectedFlat("2")}
                                  coords={getXAxis(325, 944) + "," + getYAxis(72, 203) + "," + getXAxis(435, 944) + "," + getYAxis(187, 203)}
                                  shape="rect"/>
                            <area target="" alt="fButton" title="fButton" onClick={() => setSelectedFlat("3")}
                                  coords={getXAxis(437, 944) + "," + getYAxis(72, 203) + "," + getXAxis(561, 944) + "," + getYAxis(187, 203)}
                                  shape="rect"/>
                            <area target="" alt="fButton" title="fButton" onClick={() => setSelectedFlat("4")}
                                  coords={getXAxis(563, 944) + "," + getYAxis(72, 203) + "," + getXAxis(795, 944) + "," + getYAxis(187, 203)}
                                  shape="rect"/>
                            <area target="" alt="fButton" title="fButton" onClick={() => setSelectedFlat("V2")}
                                  coords={getXAxis(817, 944) + "," + getYAxis(40, 203) + "," + getXAxis(943, 944) + "," + getYAxis(202, 203)}
                                  shape="rect"/>
                            <area target="" alt="fButton" title="fButton" onClick={() => setSelectedFlat("VS")}
                                  coords={getXAxis(594, 944) + "," + getYAxis(52, 203) + "," + getXAxis(189, 944) + "," + getYAxis(1, 203)}
                                  shape="rect"/>
                        </map>
                    </>
                    :
                    <><img id={"floorImage"}
                           src={hoveredBuilding !== "" ? "/situations/sit_new_" + hoveredBuilding + ".png" : selectedFlat ? "/situations/sit_new_" + selectedFlat + ".png" : selectedBuilding ? "/situations/sit_new_" + selectedBuilding + ".png" : "/situations/sit_new_00.png"}
                           alt={"situace"} className={isFlatDetail ? "lg:w-[30vw] w-full" : "lg:w-[37.5vw] w-full"}
                           onLoad={() => setImageLoaded(true)} useMap={"#image-map"}/>
                        <map name="image-map">
                            <area target="" alt="Budova F" style={{cursor: "pointer"}} title="Budova F"
                                  onMouseEnter={() => setHoveredBuilding("F")}
                                  onMouseLeave={() => setHoveredBuilding("")} onClick={() => {
                                if (numberOfFlats[0] === 0) {
                                    toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                        position: "bottom-center"
                                    })
                                } else {
                                    if (isFlatDetail) {
                                        Router.replace("/ponuka-bytov/" + room + "-izbove?selected=F").then()
                                    } else {
                                        setSelectedFlat("F")
                                    }
                                }
                            }}
                                  coords={getXAxis(10, 704) + "," + getYAxis(40, 200) + "," + getXAxis(122, 704) + "," + getYAxis(170, 200)}
                                  shape="rect"/>
                            <area target="" alt="Budova E" style={{cursor: "pointer"}} title="Budova E"
                                  onMouseEnter={() => setHoveredBuilding("E")}
                                  onMouseLeave={() => setHoveredBuilding("")} onClick={() => {
                                if (numberOfFlats[1] === 0) {
                                    toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                        position: "bottom-center"
                                    })
                                } else {
                                    if (isFlatDetail) {
                                        Router.replace("/ponuka-bytov/" + room + "-izbove?selected=E").then()
                                    } else {
                                        setSelectedFlat("E")
                                    }
                                }
                            }}
                                  coords={getXAxis(245, 704) + "," + getYAxis(170, 200) + "," + getXAxis(130, 704) + "," + getYAxis(69, 200)}
                                  shape="rect"/>
                            <area target="" alt="Budova D" style={{cursor: "pointer"}} title="Budova D"
                                  onMouseEnter={() => setHoveredBuilding("D")}
                                  onMouseLeave={() => setHoveredBuilding("")} onClick={() => {
                                if (numberOfFlats[2] === 0) {
                                    toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                        position: "bottom-center"
                                    })
                                } else {
                                    if (isFlatDetail) {
                                        Router.replace("/ponuka-bytov/" + room + "-izbove?selected=D").then()
                                    } else {
                                        setSelectedFlat("D")
                                    }
                                }
                            }}
                                  coords={getXAxis(258, 704) + "," + getYAxis(70, 200) + "," + getXAxis(367, 704) + "," + getYAxis(170, 200)}
                                  shape="rect"/>
                            <area target="" alt="Budova C" style={{cursor: "pointer"}} title="Budova C"
                                  onMouseEnter={() => setHoveredBuilding("C")}
                                  onMouseLeave={() => setHoveredBuilding("")} onClick={() => {
                                if (numberOfFlats[3] === 0) {
                                    toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                        position: "bottom-center"
                                    })
                                } else {
                                    if (isFlatDetail) {
                                        Router.replace("/ponuka-bytov/" + room + "-izbove?selected=C").then()
                                    } else {
                                        setSelectedFlat("C")
                                    }
                                }
                            }}
                                  coords={getXAxis(374, 704) + "," + getYAxis(19, 200) + "," + getXAxis(473, 704) + "," + getYAxis(120, 200)}
                                  shape="rect"/>
                            <area target="" alt="Budova B" style={{cursor: "pointer"}} title="Budova B"
                                  onMouseEnter={() => setHoveredBuilding("B")}
                                  onMouseLeave={() => setHoveredBuilding("")} onClick={() => {
                                if (numberOfFlats[4] === 0) {
                                    toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                        position: "bottom-center"
                                    })
                                } else {
                                    if (isFlatDetail) {
                                        Router.replace("/ponuka-bytov/" + room + "-izbove?selected=B").then()
                                    } else {
                                        setSelectedFlat("B")
                                    }
                                }
                            }}
                                  coords={getXAxis(480, 704) + "," + getYAxis(19, 200) + "," + getXAxis(576, 704) + "," + getYAxis(120, 200)}
                                  shape="rect"/>
                            <area target="" alt="Budova A" style={{cursor: "pointer"}} title="Budova A"
                                  onMouseEnter={() => setHoveredBuilding("A")}
                                  onMouseLeave={() => setHoveredBuilding("")} onClick={() => {
                                if (numberOfFlats[5] === 0) {
                                    toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                        position: "bottom-center"
                                    })
                                } else {
                                    if (isFlatDetail) {
                                        Router.replace("/ponuka-bytov/" + room + "-izbove?selected=A").then()
                                    } else {
                                        setSelectedFlat("A")
                                    }
                                }
                            }}
                                  coords={getXAxis(601, 704) + "," + getYAxis(19, 200) + "," + getXAxis(677, 704) + "," + getYAxis(190, 200)}
                                  shape="rect"/>
                        </map>
                        {numberOfFlats &&
                            <div
                                className={"flex gap-3 mt-3 lg:mt-0 lg:ml-5 text-[3vw] md:text-[2vw] justify-start w-[85vw] lg:text-[1vw] lg:w-[704px]"}>
                                {numberOfFlats.map((number: number, index: number) => (
                                    <p key={index} onClick={() => setSelectedFlat(budovy[index])}
                                       className={number > 0 ? "bg-[#B4CC7A] rounded-lg w-full py-1 text-center px-2" : "bg-[#7F4F2A] text-center py-1 text-white w-full rounded-lg px-4"}>{isMobile ?
                                        <span>{number}</span> :
                                        <span>{number === 1 ? number + " voľný" : number > 1 && number < 5 ? number + " voľné" : number + " voľných"}</span>}</p>
                                ))}
                            </div>
                        }
                    </>
                }
            </div>
        </>
    )
}