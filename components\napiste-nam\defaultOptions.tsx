export default function DefaultOptions() {
    return (
        <div
            className={"form-group lg:w-1/4 flex order-last lg:order-first px-12 lg:px-0 flex-col mb-10 lg:mb-0 gap-6"}>
            <section>
                <p>Zaujíma ma byt:</p>
                <div className="flex items-center mb-1">
                    <input id="1i" type="checkbox" value="1" name={"pocetIzieb"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="1i"
                           className="ms-2 text-sm font-medium">1-izbový</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="2i" type="checkbox" value="2" name={"pocetIzieb"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="2i"
                           className="ms-2 text-sm font-medium">2-izbový</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="3i" type="checkbox" value="3" name={"pocetIzieb"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="3i"
                           className="ms-2 text-sm font-medium">3-izbový</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="4i" type="checkbox" value="4" name={"pocetIzieb"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="4i"
                           className="ms-2 text-sm font-medium">4-izbový</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="4i" type="checkbox" value="5" name={"pocetIzieb"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="4i"
                           className="ms-2 text-sm font-medium">5-izbový</label>
                </div>
            </section>
            <section>
                <p>Preferujem:</p>
                <div className="flex items-center mb-1">
                    <input id="1" type="checkbox" value="1" name={"poschodie"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="1"
                           className="ms-2 text-sm font-medium">prízemie</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="2" type="checkbox" value="2" name={"poschodie"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="2"
                           className="ms-2 text-sm font-medium">1. poschodie</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="3" type="checkbox" value="3" name={"poschodie"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="3"
                           className="ms-2 text-sm font-medium">2. poschodie</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="4" type="checkbox" value="4" name={"poschodie"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="4"
                           className="ms-2 text-sm font-medium">3. poschodie</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="5" type="checkbox" value="5" name={"poschodie"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="5"
                           className="ms-2 text-sm font-medium">4. poschodie</label>
                </div>
            </section>
            <section className={"grid grid-cols-2"}>
                <p className={"col-span-2 mb-2"}>Páči sa mi dom:</p>
                <div className="flex items-center mb-1">
                    <input id="A" type="checkbox" value="A" name={"budova"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="A"
                           className="ms-2 text-sm font-medium">A</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="B" type="checkbox" value="" name={"budova"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="B"
                           className="ms-2 text-sm font-medium">B</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="C" type="checkbox" value="" name={"budova"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="C"
                           className="ms-2 text-sm font-medium">C</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="D" type="checkbox" value="" name={"budova"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="D"
                           className="ms-2 text-sm font-medium">D</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="E" type="checkbox" value="" name={"budova"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="E"
                           className="ms-2 text-sm font-medium">E</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="F" type="checkbox" value="" name={"budova"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="F"
                           className="ms-2 text-sm font-medium">F</label>
                </div>
            </section>
            <section>
                <p>Parkovanie typu:</p>
                <div className="flex items-center mb-1">
                    <input id="parkingPlocha" type="checkbox" value="iba plocha"
                           name={"parking"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="parkingPlocha"
                           className="ms-2 text-sm font-medium">P - iba plocha</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="parkingAj" type="checkbox" value="plocha aj sklad"
                           name={"parking"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="parkingAj"
                           className="ms-2 text-sm font-medium">P - plocha aj sklad</label>
                </div>
                <div className="flex items-center mb-1">
                    <input id="parkingSeparated" type="checkbox" value={"oddelené"}
                           name={"parking"}
                           className="w-4 h-4 mt-0.5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"/>
                    <label htmlFor="parkingSeparated"
                           className="ms-2 text-sm font-medium">S - oddelené</label>
                </div>
            </section>
        </div>
    )
}