<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 749.28 71.17">
  <g id="PLOCHA">
    <polygon points="0 66.26 10.2 71.17 316.08 71.17 316.08 0 31.88 0 0 66.26" fill="#b6bfd1" stroke-width="0"/>
    <rect x="342.02" y="0" width="407.2588" height="71.1683" fill="#b6bfd1" stroke-width="0"/>
  </g>
  <g id="PLOCHY_jednotlive" data-name="PLOCHY jednotlive">
    <g id="LWPOLYLINE">
      <polygon points="78.7275 2.5985 98.1825 2.5985 102.7652 2.5985 102.7652 6.9218 102.7652 13.7609 102.7652 13.8391 102.7652 48.426 78.7275 48.4258 78.7275 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-2" data-name="LWPOLYLINE">
      <polygon points="53.6521 2.5985 76.5658 2.5985 77.8628 2.5985 77.8628 6.9218 77.8628 13.7609 77.8628 13.8391 77.8628 48.4259 54.9491 48.4258 54.9491 45.8318 53.6521 45.8318 53.6521 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-3" data-name="LWPOLYLINE">
      <polygon points="127.6677 2.5985 103.6299 2.5985 103.6299 6.9218 103.6299 13.7609 103.6299 48.4258 127.6677 48.4258 127.6677 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-4" data-name="LWPOLYLINE">
      <polygon points="177.4725 2.5985 153.2618 2.5985 153.4348 2.5985 153.4348 6.9218 153.4348 13.7609 153.4348 13.8391 153.4348 48.426 177.4725 48.4258 177.4725 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-5" data-name="LWPOLYLINE">
      <polygon points="128.5323 2.5985 141.4159 2.5985 152.5701 2.5985 152.5701 6.9218 152.5701 13.7609 152.5701 13.8391 152.5701 48.426 128.5323 48.4258 128.5323 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-6" data-name="LWPOLYLINE">
      <polygon points="277.0823 2.5985 252.8716 2.5985 253.0445 2.5985 253.0445 6.9218 253.0445 13.7609 253.0445 13.8391 253.0445 48.426 277.0823 48.4258 277.0823 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-7" data-name="LWPOLYLINE">
      <polygon points="228.1421 2.5985 227.8827 2.5985 252.1799 2.5985 252.1799 6.9218 252.1799 13.7609 252.1799 13.8391 252.1799 48.426 228.1421 48.4258 228.1421 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-8" data-name="LWPOLYLINE">
      <polygon points="178.3372 2.5985 184.6493 2.5985 202.1588 2.5985 202.1588 6.9218 202.1588 13.7609 202.1588 13.8391 202.1588 48.426 178.3372 48.4258 178.3372 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-9" data-name="LWPOLYLINE">
      <polygon points="227.2774 2.5985 203.4558 2.5985 203.4558 6.9218 203.4558 13.7609 203.4558 13.8391 203.4558 48.426 227.2774 48.4258 227.2774 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-10" data-name="LWPOLYLINE">
      <polygon points="396.7091 2.5985 372.4984 2.5985 372.4984 6.9218 372.4984 13.7609 372.4984 13.8391 372.4984 45.8316 373.4496 45.8319 373.4496 48.4259 396.7091 48.4258 396.7091 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-11" data-name="LWPOLYLINE">
      <polygon points="277.947 2.5985 314.3495 2.5985 314.3495 6.9218 314.3495 13.7609 314.3495 13.8391 314.3495 48.426 277.947 48.4258 277.947 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-12" data-name="LWPOLYLINE">
      <path d="M397.5738,2.5985v11.2407s0,34.5868,0,34.5868l24.0299-.0002.0157-41.504c-.0078,0-.0078-4.3233-.0078-4.3233h-24.0378Z" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-13" data-name="LWPOLYLINE">
      <polygon points="447.3787 2.5985 471.4164 2.5985 471.4164 13.7609 471.4164 13.8391 471.4164 48.426 447.3787 48.4258 447.3787 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-14" data-name="LWPOLYLINE">
      <polygon points="422.4762 2.5985 422.3033 2.5985 446.514 2.5985 446.514 6.9218 446.514 13.7609 446.514 13.8391 446.514 48.4259 422.4762 48.4258 422.4762 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-15" data-name="LWPOLYLINE">
      <polygon points="496.3189 2.5985 472.2811 2.5985 472.2811 6.9218 472.2811 13.7609 472.2811 13.8391 472.2811 48.426 496.3189 48.4258 496.3189 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-16" data-name="LWPOLYLINE">
      <polygon points="522.086 2.5985 521.9131 2.5985 546.1238 2.5985 546.1238 6.9218 546.1238 13.7609 546.1238 13.8391 546.1238 48.426 522.086 48.4258 522.086 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-17" data-name="LWPOLYLINE">
      <polygon points="497.1835 2.5985 497.0106 2.5985 521.2213 2.5985 521.2213 6.9218 521.2213 13.7609 521.2213 13.8391 521.2213 48.426 497.1835 48.426 497.1835 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-18" data-name="LWPOLYLINE">
      <polygon points="645.7335 2.5985 621.5228 2.5985 621.6957 2.5985 621.6957 6.9218 621.6957 13.7609 621.6957 13.8391 621.6957 48.426 645.7335 48.4258 645.7335 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-19" data-name="LWPOLYLINE">
      <polygon points="596.7933 2.5985 596.6204 2.5985 620.8311 2.5985 620.8311 6.9218 620.8311 13.7609 620.8311 13.8391 620.8311 48.426 596.7933 48.4258 596.7933 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-20" data-name="LWPOLYLINE">
      <polygon points="546.9884 2.5985 546.8155 2.5985 570.81 2.5985 570.81 6.9218 570.81 13.7609 570.81 13.8391 570.81 48.426 546.9884 48.4258 546.9884 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-21" data-name="LWPOLYLINE">
      <polygon points="595.9286 2.5985 572.107 2.5984 572.107 6.9218 572.107 13.7609 572.107 13.8391 572.107 48.4259 595.9286 48.4258 595.9286 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-22" data-name="LWPOLYLINE">
      <polygon points="695.5384 2.5985 589.6166 2.5985 671.5006 2.5985 671.5006 6.9218 671.5006 13.7609 671.5006 13.8391 671.5006 48.426 695.5384 48.4258 695.5384 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-23" data-name="LWPOLYLINE">
      <polygon points="646.5982 2.5985 646.4253 2.5985 670.636 2.5985 670.636 6.9218 670.636 13.7609 670.636 13.8391 670.636 48.426 646.5982 48.4258 646.5982 2.5985" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-24" data-name="LWPOLYLINE">
      <polygon points="696.4031 2.5984 718.798 2.5985 720.6138 2.5985 720.6138 6.9218 720.6138 13.7609 720.6138 13.8391 720.6138 45.832 719.3168 45.8316 719.3168 48.4259 696.4031 48.4258 696.4031 2.5984" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-25" data-name="LWPOLYLINE">
      <polygon points="54.9491 50.1552 76.1335 50.1552 77.8628 50.1552 77.8628 69.4375 54.9491 69.4373 54.9491 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-26" data-name="LWPOLYLINE">
      <polygon points="397.5738 50.1553 397.4009 50.1553 421.6116 50.1552 421.6116 69.4373 397.5738 69.4373 397.5738 50.1553" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-27" data-name="LWPOLYLINE">
      <rect x="422.4762" y="50.1552" width="24.0378" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-28" data-name="LWPOLYLINE">
      <polygon points="78.7275 50.1552 97.7502 50.1552 102.7652 50.1552 102.7652 69.4375 78.7275 69.4373 78.7275 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-29" data-name="LWPOLYLINE">
      <polygon points="103.6299 50.1552 119.1939 50.1552 127.6677 50.1552 127.6677 69.4375 103.6299 69.4373 103.6299 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-30" data-name="LWPOLYLINE">
      <polygon points="128.5323 50.1552 140.9836 50.1552 152.5701 50.1552 152.5701 69.4375 128.5323 69.4373 128.5323 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-31" data-name="LWPOLYLINE">
      <polygon points="153.4348 50.1552 162.6003 50.1552 177.4725 50.1552 177.4725 69.4375 153.4348 69.4373 153.4348 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-32" data-name="LWPOLYLINE">
      <polygon points="178.3372 50.1552 183.7846 50.1552 202.1588 50.1552 202.1588 69.4375 178.3372 69.4373 178.3372 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-33" data-name="LWPOLYLINE">
      <polygon points="203.4558 50.1552 205.8337 50.1552 227.2774 50.1552 227.2774 69.4375 203.4558 69.4373 203.4558 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-34" data-name="LWPOLYLINE">
      <rect x="228.1421" y="50.1552" width="24.0378" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-35" data-name="LWPOLYLINE">
      <rect x="253.0445" y="50.1552" width="24.0378" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-36" data-name="LWPOLYLINE">
      <rect x="277.947" y="50.1553" width="36.4025" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-37" data-name="LWPOLYLINE">
      <rect x="373.4496" y="50.1552" width="23.2596" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-38" data-name="LWPOLYLINE">
      <polygon points="447.3787 50.1552 447.2057 50.1552 471.4164 50.1552 471.4164 69.4375 447.3787 69.4373 447.3787 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-39" data-name="LWPOLYLINE">
      <rect x="472.2811" y="50.1552" width="24.0378" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-40" data-name="LWPOLYLINE">
      <polygon points="497.1835 50.1552 497.0106 50.1552 521.2213 50.1552 521.2213 69.4375 497.1835 69.4373 497.1835 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-41" data-name="LWPOLYLINE">
      <rect x="511.3262" y="50.5439" width="24.0378" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-42" data-name="LWPOLYLINE">
      <polygon points="546.9668 50.1552 546.7939 50.1552 570.81 50.1552 570.81 69.4375 547.01 69.4373 546.9668 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-43" data-name="LWPOLYLINE">
      <rect x="572.107" y="50.1552" width="23.8216" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-44" data-name="LWPOLYLINE">
      <polygon points="596.7933 50.1552 596.6204 50.1552 620.8311 50.1551 620.8311 69.4374 596.7933 69.4373 596.7933 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-45" data-name="LWPOLYLINE">
      <rect x="621.6957" y="50.1552" width="24.0378" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-46" data-name="LWPOLYLINE">
      <polygon points="646.5982 50.1552 646.4253 50.1552 670.636 50.1551 670.636 69.4374 646.5982 69.4373 646.5982 50.1552" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-47" data-name="LWPOLYLINE">
      <rect x="671.5006" y="50.1552" width="24.0378" height="19.2822" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-48" data-name="LWPOLYLINE">
      <polygon points="696.4031 50.1553 696.2301 50.1553 719.3168 50.1552 719.3168 69.4375 696.4031 69.4375 696.4031 50.1553" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-49" data-name="LWPOLYLINE">
      <polygon points="10.9186 47.5612 53.2197 47.5612 53.2197 69.4375 10.5923 69.4371 2.3109 65.453 10.9186 47.5612" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-50" data-name="LWPOLYLINE">
      <polygon points="721.0461 47.5612 742.2305 47.5612 747.5482 47.5612 747.5482 69.4375 721.0461 69.4373 721.0461 47.5612" fill="#b6bfd1" stroke-width="0"/>
    </g>
    <g id="LWPOLYLINE-51" data-name="LWPOLYLINE">
      <rect x="343.7482" y="47.5612" width="27.972" height="21.8762" fill="#b6bfd1" stroke-width="0"/>
    </g>
  </g>
  <g id="DVERE">
    <g id="ARC">
      <path d="M740.5012,48.8584c0,3.8203,3.097,6.9173,6.9173,6.9173" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LINE">
      <line x1="748.2832" y1="48.4261" x2="747.4185" y2="48.4261" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LINE-2" data-name="LINE">
      <line x1="748.2832" y1="56.2081" x2="747.4185" y2="56.2081" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LWPOLYLINE-52" data-name="LWPOLYLINE">
      <polyline points="748.2832 56.2081 748.2832 55.7757 747.4185 55.7757 747.4185 56.4675 747.5482 56.4675" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LWPOLYLINE-53" data-name="LWPOLYLINE">
      <polyline points="748.2832 48.4261 748.2832 48.8584 747.4185 48.8584 747.4185 48.1666 747.5482 48.1666" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LINE-3" data-name="LINE">
      <line x1="747.4185" y1="48.8584" x2="740.5012" y2="48.8584" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="ARC-2" data-name="ARC">
      <path d="M350.7952,48.8584c0,3.8203-3.097,6.9173-6.9173,6.9173" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LINE-4" data-name="LINE">
      <line x1="343.0132" y1="48.4261" x2="343.8779" y2="48.4261" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LINE-5" data-name="LINE">
      <line x1="343.0132" y1="56.2081" x2="343.8779" y2="56.2081" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LWPOLYLINE-54" data-name="LWPOLYLINE">
      <polyline points="343.0132 56.2081 343.0132 55.7757 343.8779 55.7757 343.8779 56.4675 343.7482 56.4675" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LWPOLYLINE-55" data-name="LWPOLYLINE">
      <polyline points="343.0132 48.4261 343.0132 48.8584 343.8779 48.8584 343.8779 48.1666 343.7482 48.1666" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
    <g id="LINE-6" data-name="LINE">
      <line x1="343.8779" y1="48.8584" x2="350.7952" y2="48.8584" fill="none" stroke="#8590a6" stroke-dasharray="0 0 1 .4" stroke-linecap="round" stroke-linejoin="round" stroke-width=".09"/>
    </g>
  </g>
  <g id="STENY">
    <polygon points="395.07 0 399.22 0 399.22 2.6 397.57 2.6 397.57 69.44 421.61 69.44 421.61 50.16 406.22 50.16 406.22 48.43 421.61 48.43 421.61 2.6 419.97 2.6 419.97 0 424.12 0 424.12 2.6 422.48 2.6 422.48 48.43 437.87 48.43 437.87 50.16 422.48 50.16 422.48 69.44 446.51 69.44 446.51 2.6 444.87 2.6 444.87 0 449.02 0 449.02 2.6 447.38 2.6 447.38 69.44 471.42 69.44 471.42 50.16 456.03 50.16 456.03 48.43 471.42 48.43 471.42 2.6 469.77 2.6 469.77 0 473.92 0 473.92 2.6 472.28 2.6 472.28 48.43 487.67 48.43 487.67 50.16 472.28 50.16 472.28 69.44 496.32 69.44 496.32 2.6 494.68 2.6 494.68 0 498.83 0 498.83 2.6 497.18 2.6 497.18 69.44 521.22 69.44 521.22 50.16 505.83 50.16 505.83 48.43 521.22 48.43 521.22 2.6 519.58 2.6 519.58 0 523.73 0 523.73 2.6 522.09 2.6 522.09 48.43 537.48 48.43 537.48 50.16 522.09 50.16 522.09 69.44 546.12 69.44 546.12 2.6 544.48 2.6 544.48 0 548.63 0 548.63 2.6 546.99 2.6 546.99 69.44 570.81 69.44 570.81 50.16 555.64 50.16 555.64 48.43 570.81 48.43 570.81 2.6 569.38 2.6 569.38 0 573.53 0 573.53 2.6 572.11 2.6 572.11 48.43 587.28 48.43 587.28 50.16 572.11 50.16 572.11 69.44 595.93 69.44 595.93 2.6 594.29 2.6 594.29 0 598.44 0 598.44 2.6 596.79 2.6 596.79 69.44 620.83 69.44 620.83 50.16 605.44 50.16 605.44 48.43 620.83 48.43 620.83 2.6 619.19 2.6 619.19 0 623.34 0 623.34 2.6 621.7 2.6 621.7 48.43 637.09 48.43 637.09 50.16 621.7 50.16 621.7 69.44 645.73 69.44 645.73 2.6 644.09 2.6 644.09 0 648.24 0 648.24 2.6 646.6 2.6 646.6 69.44 670.64 69.44 670.64 50.16 655.24 50.16 655.24 48.43 670.64 48.43 670.64 2.6 668.99 2.6 668.99 0 673.14 0 673.14 2.6 671.5 2.6 671.5 48.43 686.89 48.43 686.89 50.16 671.5 50.16 671.5 69.44 695.54 69.44 695.54 2.6 693.9 2.6 693.9 0 698.05 0 698.05 2.6 696.4 2.6 696.4 69.44 719.32 69.44 719.32 50.16 705.05 50.16 705.05 48.43 719.32 48.43 719.32 45.83 720.61 45.83 720.61 2.6 718.8 2.6 718.8 0 749.28 0 749.28 2.6 722.34 2.6 722.34 45.83 747.55 45.83 747.55 28.54 749.28 28.54 749.28 48.43 747.55 48.43 747.55 47.56 721.05 47.56 721.05 69.44 747.55 69.44 747.55 56.21 749.28 56.21 749.28 71.17 342.02 71.17 342.02 56.21 343.75 56.21 343.75 69.44 371.72 69.44 371.72 47.56 343.75 47.56 343.75 48.43 342.02 48.43 342.02 28.54 343.75 28.54 343.75 45.83 370.77 45.83 370.77 2.6 342.02 2.6 342.02 0 374.31 0 374.31 2.6 372.5 2.6 372.5 45.83 373.45 45.83 373.45 48.43 388.06 48.43 388.06 50.16 373.45 50.16 373.45 69.44 396.71 69.44 396.71 2.6 395.07 2.6 395.07 0" fill="#000" stroke-width="0"/>
    <polygon points="152.57 69.44 152.57 50.16 137.18 50.16 137.18 48.43 152.57 48.43 152.57 2.6 150.93 2.6 150.93 0 155.08 0 155.08 2.6 153.43 2.6 153.43 48.43 168.83 48.43 168.83 50.16 153.43 50.16 153.43 69.44 177.47 69.44 177.47 2.6 175.83 2.6 175.83 0 179.98 0 179.98 2.6 178.34 2.6 178.34 69.44 202.16 69.44 202.16 50.16 186.98 50.16 186.98 48.43 202.16 48.43 202.16 2.6 200.73 2.6 200.73 0 204.88 0 204.88 2.6 203.46 2.6 203.46 48.43 218.63 48.43 218.63 50.16 203.46 50.16 203.46 69.44 227.28 69.44 227.28 2.6 225.63 2.6 225.63 0 229.78 0 229.78 2.6 228.14 2.6 228.14 69.44 252.18 69.44 252.18 50.16 236.79 50.16 236.79 48.43 252.18 48.43 252.18 2.6 250.54 2.6 250.54 0 254.69 0 254.69 2.6 253.04 2.6 253.04 48.43 268.44 48.43 268.44 50.16 253.04 50.16 253.04 69.44 277.08 69.44 277.08 2.6 275.44 2.6 275.44 0 279.59 0 279.59 2.6 277.95 2.6 277.95 69.44 314.35 69.44 314.35 50.16 286.59 50.16 286.59 48.43 314.35 48.43 314.35 2.6 300.34 2.6 300.34 0 316.08 0 316.08 71.17 10.2 71.17 0 66.26 .75 64.7 10.59 69.44 53.22 69.44 53.22 47.56 9 47.56 19.39 25.97 20.94 26.72 11.75 45.83 51.92 45.83 51.92 2.6 30.63 2.6 31.88 0 55.47 0 55.47 2.6 53.65 2.6 53.65 45.83 54.95 45.83 54.95 48.43 69.22 48.43 69.22 50.16 54.95 50.16 54.95 69.44 77.86 69.44 77.86 2.6 76.22 2.6 76.22 0 80.37 0 80.37 2.6 78.73 2.6 78.73 69.44 102.77 69.44 102.77 50.16 87.37 50.16 87.37 48.43 102.77 48.43 102.77 2.6 101.12 2.6 101.12 0 105.27 0 105.27 2.6 103.63 2.6 103.63 48.43 119.02 48.43 119.02 50.16 103.63 50.16 103.63 69.44 127.67 69.44 127.67 2.6 126.02 2.6 126.02 0 130.18 0 130.18 2.6 128.53 2.6 128.53 69.44 152.57 69.44" fill="#000" stroke-width="0"/>
  </g>
  <g id="_VZT">
    <g id="LINE-7" data-name="LINE">
      <line x1="76.5658" y1="69.4373" x2="76.5658" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-8" data-name="LINE">
      <line x1="75.2688" y1="69.4373" x2="75.2688" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-9" data-name="LINE">
      <line x1="76.5658" y1="69.4373" x2="76.5658" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-10" data-name="LINE">
      <line x1="75.2688" y1="70.8208" x2="76.5658" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-11" data-name="LINE">
      <line x1="80.0245" y1="69.4373" x2="80.0245" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-12" data-name="LINE">
      <line x1="81.3215" y1="69.4373" x2="81.3215" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-13" data-name="LINE">
      <line x1="80.0245" y1="70.8208" x2="81.3215" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-14" data-name="LINE">
      <line x1="56.2461" y1="69.4373" x2="56.2461" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-15" data-name="LINE">
      <line x1="57.5431" y1="69.4373" x2="57.5431" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-16" data-name="LINE">
      <line x1="56.2461" y1="70.8208" x2="57.5431" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-17" data-name="LINE">
      <line x1="313.0525" y1="69.4375" x2="313.0525" y2="71.1668" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-18" data-name="LINE">
      <line x1="311.7555" y1="69.4375" x2="311.7555" y2="71.1668" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-19" data-name="LINE">
      <line x1="313.0525" y1="69.4375" x2="313.0525" y2="71.1668" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-20" data-name="LINE">
      <line x1="311.7555" y1="70.821" x2="313.0525" y2="70.821" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-21" data-name="LINE">
      <line x1="101.4682" y1="69.4373" x2="101.4682" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-22" data-name="LINE">
      <line x1="100.1712" y1="69.4373" x2="100.1712" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-23" data-name="LINE">
      <line x1="101.4682" y1="69.4373" x2="101.4682" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-24" data-name="LINE">
      <line x1="100.1712" y1="70.8208" x2="101.4682" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-25" data-name="LINE">
      <line x1="104.9269" y1="69.4373" x2="104.9269" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-26" data-name="LINE">
      <line x1="106.2239" y1="69.4373" x2="106.2239" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-27" data-name="LINE">
      <line x1="104.9269" y1="70.8208" x2="106.2239" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-28" data-name="LINE">
      <line x1="126.3707" y1="69.4373" x2="126.3707" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-29" data-name="LINE">
      <line x1="125.0737" y1="69.4373" x2="125.0737" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-30" data-name="LINE">
      <line x1="126.3707" y1="69.4373" x2="126.3707" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-31" data-name="LINE">
      <line x1="125.0737" y1="70.8208" x2="126.3707" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-32" data-name="LINE">
      <line x1="129.8293" y1="69.4373" x2="129.8293" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-33" data-name="LINE">
      <line x1="131.1263" y1="69.4373" x2="131.1263" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-34" data-name="LINE">
      <line x1="129.8293" y1="70.8208" x2="131.1263" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-35" data-name="LINE">
      <line x1="151.2731" y1="69.4373" x2="151.2731" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-36" data-name="LINE">
      <line x1="149.9761" y1="69.4373" x2="149.9761" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-37" data-name="LINE">
      <line x1="151.2731" y1="69.4373" x2="151.2731" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-38" data-name="LINE">
      <line x1="149.9761" y1="70.8208" x2="151.2731" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-39" data-name="LINE">
      <line x1="154.7318" y1="69.4373" x2="154.7318" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-40" data-name="LINE">
      <line x1="156.0288" y1="69.4373" x2="156.0288" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-41" data-name="LINE">
      <line x1="154.7318" y1="70.8208" x2="156.0288" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-42" data-name="LINE">
      <line x1="176.1755" y1="69.4373" x2="176.1755" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-43" data-name="LINE">
      <line x1="174.8785" y1="69.4373" x2="174.8785" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-44" data-name="LINE">
      <line x1="176.1755" y1="69.4373" x2="176.1755" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-45" data-name="LINE">
      <line x1="174.8785" y1="70.8208" x2="176.1755" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-46" data-name="LINE">
      <line x1="179.6342" y1="69.4373" x2="179.6342" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-47" data-name="LINE">
      <line x1="180.9312" y1="69.4373" x2="180.9312" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-48" data-name="LINE">
      <line x1="179.6342" y1="70.8208" x2="180.9312" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-49" data-name="LINE">
      <line x1="201.078" y1="69.4373" x2="201.078" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-50" data-name="LINE">
      <line x1="199.781" y1="69.4373" x2="199.781" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-51" data-name="LINE">
      <line x1="201.078" y1="69.4373" x2="201.078" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-52" data-name="LINE">
      <line x1="199.781" y1="70.8208" x2="201.078" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-53" data-name="LINE">
      <line x1="204.5367" y1="69.4373" x2="204.5367" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-54" data-name="LINE">
      <line x1="205.8337" y1="69.4373" x2="205.8337" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-55" data-name="LINE">
      <line x1="204.5367" y1="70.8208" x2="205.8337" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-56" data-name="LINE">
      <line x1="225.9804" y1="69.4373" x2="225.9804" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-57" data-name="LINE">
      <line x1="224.6834" y1="69.4373" x2="224.6834" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-58" data-name="LINE">
      <line x1="225.9804" y1="69.4373" x2="225.9804" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-59" data-name="LINE">
      <line x1="224.6834" y1="70.8208" x2="225.9804" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-60" data-name="LINE">
      <line x1="229.4391" y1="69.4373" x2="229.4391" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-61" data-name="LINE">
      <line x1="230.7361" y1="69.4373" x2="230.7361" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-62" data-name="LINE">
      <line x1="229.4391" y1="70.8208" x2="230.7361" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-63" data-name="LINE">
      <line x1="250.8829" y1="69.4373" x2="250.8829" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-64" data-name="LINE">
      <line x1="249.5859" y1="69.4373" x2="249.5859" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-65" data-name="LINE">
      <line x1="250.8829" y1="69.4373" x2="250.8829" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-66" data-name="LINE">
      <line x1="249.5859" y1="70.8208" x2="250.8829" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-67" data-name="LINE">
      <line x1="254.3415" y1="69.4373" x2="254.3415" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-68" data-name="LINE">
      <line x1="255.6385" y1="69.4373" x2="255.6385" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-69" data-name="LINE">
      <line x1="254.3415" y1="70.8208" x2="255.6385" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-70" data-name="LINE">
      <line x1="275.7853" y1="69.4373" x2="275.7853" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-71" data-name="LINE">
      <line x1="274.4883" y1="69.4373" x2="274.4883" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-72" data-name="LINE">
      <line x1="275.7853" y1="69.4373" x2="275.7853" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-73" data-name="LINE">
      <line x1="274.4883" y1="70.8208" x2="275.7853" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-74" data-name="LINE">
      <line x1="279.244" y1="69.4373" x2="279.244" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-75" data-name="LINE">
      <line x1="280.541" y1="69.4373" x2="280.541" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-76" data-name="LINE">
      <line x1="279.244" y1="70.8208" x2="280.541" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-77" data-name="LINE">
      <line x1="395.4121" y1="69.4371" x2="395.4121" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-78" data-name="LINE">
      <line x1="394.1151" y1="69.4371" x2="394.1151" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-79" data-name="LINE">
      <line x1="395.4121" y1="69.4371" x2="395.4121" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-80" data-name="LINE">
      <line x1="394.1151" y1="70.8206" x2="395.4121" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-81" data-name="LINE">
      <line x1="398.8708" y1="69.4371" x2="398.8708" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-82" data-name="LINE">
      <line x1="400.1678" y1="69.4371" x2="400.1678" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-83" data-name="LINE">
      <line x1="398.8708" y1="70.8206" x2="400.1678" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-84" data-name="LINE">
      <line x1="420.3146" y1="69.4371" x2="420.3146" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-85" data-name="LINE">
      <line x1="419.0176" y1="69.4371" x2="419.0176" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-86" data-name="LINE">
      <line x1="420.3146" y1="69.4371" x2="420.3146" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-87" data-name="LINE">
      <line x1="419.0176" y1="70.8206" x2="420.3146" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-88" data-name="LINE">
      <line x1="423.7732" y1="69.4371" x2="423.7732" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-89" data-name="LINE">
      <line x1="425.0702" y1="69.4371" x2="425.0702" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-90" data-name="LINE">
      <line x1="423.7732" y1="70.8206" x2="425.0702" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-91" data-name="LINE">
      <line x1="445.217" y1="69.4371" x2="445.217" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-92" data-name="LINE">
      <line x1="443.92" y1="69.4371" x2="443.92" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-93" data-name="LINE">
      <line x1="445.217" y1="69.4371" x2="445.217" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-94" data-name="LINE">
      <line x1="443.92" y1="70.8206" x2="445.217" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-95" data-name="LINE">
      <line x1="448.6757" y1="69.4371" x2="448.6757" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-96" data-name="LINE">
      <line x1="449.9727" y1="69.4371" x2="449.9727" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-97" data-name="LINE">
      <line x1="448.6757" y1="70.8206" x2="449.9727" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-98" data-name="LINE">
      <line x1="470.1194" y1="69.4371" x2="470.1194" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-99" data-name="LINE">
      <line x1="468.8224" y1="69.4371" x2="468.8224" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-100" data-name="LINE">
      <line x1="470.1194" y1="69.4371" x2="470.1194" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-101" data-name="LINE">
      <line x1="468.8224" y1="70.8206" x2="470.1194" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-102" data-name="LINE">
      <line x1="473.5781" y1="69.4371" x2="473.5781" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-103" data-name="LINE">
      <line x1="474.8751" y1="69.4371" x2="474.8751" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-104" data-name="LINE">
      <line x1="473.5781" y1="70.8206" x2="474.8751" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-105" data-name="LINE">
      <line x1="495.0219" y1="69.4371" x2="495.0219" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-106" data-name="LINE">
      <line x1="493.7249" y1="69.4371" x2="493.7249" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-107" data-name="LINE">
      <line x1="495.0219" y1="69.4371" x2="495.0219" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-108" data-name="LINE">
      <line x1="493.7249" y1="70.8206" x2="495.0219" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-109" data-name="LINE">
      <line x1="498.4806" y1="69.4371" x2="498.4806" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-110" data-name="LINE">
      <line x1="499.7776" y1="69.4371" x2="499.7776" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-111" data-name="LINE">
      <line x1="498.4806" y1="70.8206" x2="499.7776" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-112" data-name="LINE">
      <line x1="519.9243" y1="69.4373" x2="519.9243" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-113" data-name="LINE">
      <line x1="518.6273" y1="69.4373" x2="518.6273" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-114" data-name="LINE">
      <line x1="519.9243" y1="69.4373" x2="519.9243" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-115" data-name="LINE">
      <line x1="518.6273" y1="70.8208" x2="519.9243" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-116" data-name="LINE">
      <line x1="523.383" y1="69.4373" x2="523.383" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-117" data-name="LINE">
      <line x1="524.68" y1="69.4373" x2="524.68" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-118" data-name="LINE">
      <line x1="523.383" y1="70.8208" x2="524.68" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-119" data-name="LINE">
      <line x1="544.8268" y1="69.4373" x2="544.8268" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-120" data-name="LINE">
      <line x1="543.5298" y1="69.4373" x2="543.5298" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-121" data-name="LINE">
      <line x1="544.8268" y1="69.4373" x2="544.8268" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-122" data-name="LINE">
      <line x1="543.5298" y1="70.8208" x2="544.8268" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-123" data-name="LINE">
      <line x1="548.2854" y1="69.4373" x2="548.2854" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-124" data-name="LINE">
      <line x1="549.5824" y1="69.4373" x2="549.5824" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-125" data-name="LINE">
      <line x1="548.2854" y1="70.8208" x2="549.5824" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-126" data-name="LINE">
      <line x1="569.7292" y1="69.4371" x2="569.7292" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-127" data-name="LINE">
      <line x1="568.4322" y1="69.4371" x2="568.4322" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-128" data-name="LINE">
      <line x1="569.7292" y1="69.4371" x2="569.7292" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-129" data-name="LINE">
      <line x1="568.4322" y1="70.8206" x2="569.7292" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-130" data-name="LINE">
      <line x1="573.1879" y1="69.4371" x2="573.1879" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-131" data-name="LINE">
      <line x1="574.4849" y1="69.4371" x2="574.4849" y2="71.1665" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-132" data-name="LINE">
      <line x1="573.1879" y1="70.8206" x2="574.4849" y2="70.8206" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-133" data-name="LINE">
      <line x1="594.6316" y1="69.4372" x2="594.6316" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-134" data-name="LINE">
      <line x1="593.3346" y1="69.4372" x2="593.3346" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-135" data-name="LINE">
      <line x1="594.6316" y1="69.4372" x2="594.6316" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-136" data-name="LINE">
      <line x1="593.3346" y1="70.8207" x2="594.6316" y2="70.8207" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-137" data-name="LINE">
      <line x1="598.0903" y1="69.4372" x2="598.0903" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-138" data-name="LINE">
      <line x1="599.3873" y1="69.4372" x2="599.3873" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-139" data-name="LINE">
      <line x1="598.0903" y1="70.8207" x2="599.3873" y2="70.8207" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-140" data-name="LINE">
      <line x1="619.5341" y1="69.4372" x2="619.5341" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-141" data-name="LINE">
      <line x1="618.2371" y1="69.4372" x2="618.2371" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-142" data-name="LINE">
      <line x1="619.5341" y1="69.4372" x2="619.5341" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-143" data-name="LINE">
      <line x1="618.2371" y1="70.8207" x2="619.5341" y2="70.8207" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-144" data-name="LINE">
      <line x1="622.9927" y1="69.4372" x2="622.9927" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-145" data-name="LINE">
      <line x1="624.2898" y1="69.4372" x2="624.2898" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-146" data-name="LINE">
      <line x1="622.9927" y1="70.8207" x2="624.2898" y2="70.8207" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-147" data-name="LINE">
      <line x1="644.4365" y1="69.4372" x2="644.4365" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-148" data-name="LINE">
      <line x1="643.1395" y1="69.4372" x2="643.1395" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-149" data-name="LINE">
      <line x1="644.4365" y1="69.4372" x2="644.4365" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-150" data-name="LINE">
      <line x1="643.1395" y1="70.8207" x2="644.4365" y2="70.8207" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-151" data-name="LINE">
      <line x1="647.8952" y1="69.4372" x2="647.8952" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-152" data-name="LINE">
      <line x1="649.1922" y1="69.4372" x2="649.1922" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-153" data-name="LINE">
      <line x1="647.8952" y1="70.8207" x2="649.1922" y2="70.8207" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-154" data-name="LINE">
      <line x1="669.339" y1="69.4373" x2="669.339" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-155" data-name="LINE">
      <line x1="668.042" y1="69.4373" x2="668.042" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-156" data-name="LINE">
      <line x1="669.339" y1="69.4373" x2="669.339" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-157" data-name="LINE">
      <line x1="668.042" y1="70.8208" x2="669.339" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-158" data-name="LINE">
      <line x1="672.7976" y1="69.4373" x2="672.7976" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-159" data-name="LINE">
      <line x1="674.0946" y1="69.4373" x2="674.0946" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-160" data-name="LINE">
      <line x1="672.7976" y1="70.8208" x2="674.0946" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-161" data-name="LINE">
      <line x1="694.2414" y1="69.4373" x2="694.2414" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-162" data-name="LINE">
      <line x1="692.9444" y1="69.4373" x2="692.9444" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-163" data-name="LINE">
      <line x1="694.2414" y1="69.4373" x2="694.2414" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-164" data-name="LINE">
      <line x1="692.9444" y1="70.8208" x2="694.2414" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-165" data-name="LINE">
      <line x1="697.7001" y1="69.4373" x2="697.7001" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-166" data-name="LINE">
      <line x1="698.9971" y1="69.4373" x2="698.9971" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-167" data-name="LINE">
      <line x1="697.7001" y1="70.8208" x2="698.9971" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-168" data-name="LINE">
      <line x1="718.0198" y1="69.4375" x2="718.0198" y2="71.1668" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-169" data-name="LINE">
      <line x1="716.7228" y1="69.4375" x2="716.7228" y2="71.1668" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-170" data-name="LINE">
      <line x1="718.0198" y1="69.4375" x2="718.0198" y2="71.1668" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-171" data-name="LINE">
      <line x1="716.7228" y1="70.821" x2="718.0198" y2="70.821" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-172" data-name="LINE">
      <line x1="374.7466" y1="69.4373" x2="374.7466" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-173" data-name="LINE">
      <line x1="376.0436" y1="69.4373" x2="376.0436" y2="71.1666" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
    <g id="LINE-174" data-name="LINE">
      <line x1="374.7466" y1="70.8208" x2="376.0436" y2="70.8208" fill="none" stroke="#5e5e5e" stroke-linecap="round" stroke-linejoin="round" stroke-width=".0005"/>
    </g>
  </g>
  <g id="Popis_cisla_krivky" data-name="Popis cisla krivky">
    <g id="MTEXT">
      <g>
        <path d="M73.7338,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0137,1.0718-3.5205,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3203-.6958s.8604-1.0908.8604-1.8799c0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6133,0-1.1064.1357-1.4805.4077-.373.272-.6992.7334-.9795,1.3843l-.8203,1.856c-.5996,1.5039-1.8262,2.2559-3.6797,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1592-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8662,0-1.5361.1919-2.0098.5757-.4727.3843-.71.9336-.71,1.6479,0,.6191.1904,1.1069.5703,1.4644s.8896.5356,1.5293.5356c.5869,0,1.0742-.1543,1.46-.4639.3877-.3091.7275-.8105,1.0205-1.5039l.7598-1.728c.6533-1.4722,1.9141-2.208,3.7803-2.208,1.1865,0,2.1436.3335,2.8701,1s1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M73.4135,27.558h-9.7402v2.272h-1.5391c0-.9492-.167-1.584-.5-1.9038-.334-.3198-.9404-.48-1.8203-.48v-1.3281h13.5996v1.4399Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-2" data-name="MTEXT">
      <g>
        <path d="M98.101,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0137,1.0718-3.5205,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3203-.6958s.8604-1.0908.8604-1.8799c0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6133,0-1.1064.1357-1.4805.4077-.373.272-.6992.7334-.9795,1.3843l-.8203,1.856c-.5996,1.5039-1.8262,2.2559-3.6797,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1592-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8662,0-1.5361.1919-2.0098.5757-.4727.3843-.71.9336-.71,1.6479,0,.6191.1904,1.1069.5703,1.4644s.8896.5356,1.5293.5356c.5869,0,1.0742-.1543,1.46-.4639.3877-.3091.7275-.8105,1.0205-1.5039l.7598-1.728c.6533-1.4722,1.9141-2.208,3.7803-2.208,1.1865,0,2.1436.3335,2.8701,1s1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M97.7807,29.558c-1.5059,0-2.7266-.208-3.6602-.624-.9326-.416-1.7197-1.1089-2.3594-2.0796l-1.0801-1.6641c-.6533-1.0024-1.6006-1.5044-2.8398-1.5044-.6807,0-1.2305.1763-1.6504.5283s-.6299.8105-.6299,1.376c0,.7041.2734,1.2505.8203,1.6401.5469.3892,1.3193.584,2.3193.584v1.5039c-1.4395,0-2.5898-.3384-3.4492-1.0161-.8604-.6772-1.29-1.5811-1.29-2.7119,0-1.0562.3398-1.8936,1.0195-2.5122.6797-.6182,1.6064-.9277,2.7803-.9277,1.7734,0,3.1396.7041,4.0996,2.1118l1.1006,1.6641c.4531.6719.9033,1.1416,1.3496,1.4082.4473.2666,1.0566.4375,1.8301.5117v-5.7119h1.6396v7.4238Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-3" data-name="MTEXT">
      <g>
        <path d="M123.1694,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0137,1.0718-3.5205,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3203-.6958s.8604-1.0908.8604-1.8799c0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6133,0-1.1064.1357-1.4805.4077-.373.272-.6992.7334-.9795,1.3843l-.8203,1.856c-.5996,1.5039-1.8262,2.2559-3.6797,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1592-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8662,0-1.5361.1919-2.0098.5757-.4727.3843-.71.9336-.71,1.6479,0,.6191.1904,1.1069.5703,1.4644s.8896.5356,1.5293.5356c.5869,0,1.0742-.1543,1.46-.4639.3877-.3091.7275-.8105,1.0205-1.5039l.7598-1.728c.6533-1.4722,1.9141-2.208,3.7803-2.208,1.1865,0,2.1436.3335,2.8701,1s1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M123.0697,25.8939c0,1.2163-.3975,2.168-1.1904,2.8564-.793.688-1.8965,1.0317-3.3096,1.0317v-1.4878c.9326,0,1.6562-.2104,2.1699-.6323.5127-.4209.7695-1.0103.7695-1.7681,0-.6611-.2266-1.1973-.6797-1.6079-.4531-.4102-1.0469-.6157-1.7803-.6157-.7998,0-1.417.1973-1.8496.5918-.4336.395-.6504.9653-.6504,1.7119v.8003h-1.5801v-.8164c0-.6611-.1934-1.165-.5801-1.5117s-.9463-.52-1.6797-.52c-.6396,0-1.1602.1841-1.5605.5518-.3994.3682-.5996.8511-.5996,1.4482,0,.6616.2305,1.1895.6904,1.584s1.083.5918,1.8691.5918v1.4243c-1.2393,0-2.2295-.3252-2.9697-.9761-.7402-.6504-1.1094-1.5254-1.1094-2.624,0-1.0454.3369-1.8882,1.0098-2.5278.6729-.6401,1.5635-.96,2.6699-.96,1.5732,0,2.5801.6611,3.0195,1.9839.1074-.6504.4668-1.1841,1.0801-1.6001s1.3604-.624,2.2402-.624c1.1865,0,2.1533.3442,2.9004,1.0322.7461.688,1.1201,1.5757,1.1201,2.6636Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-4" data-name="MTEXT">
      <g>
        <path d="M147.7778,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0137,1.0718-3.5205,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3203-.6958s.8604-1.0908.8604-1.8799c0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6133,0-1.1064.1357-1.4805.4077-.373.272-.6992.7334-.9795,1.3843l-.8203,1.856c-.5996,1.5039-1.8262,2.2559-3.6797,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1592-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8662,0-1.5361.1919-2.0098.5757-.4727.3843-.71.9336-.71,1.6479,0,.6191.1904,1.1069.5703,1.4644s.8896.5356,1.5293.5356c.5869,0,1.0742-.1543,1.46-.4639.3877-.3091.7275-.8105,1.0205-1.5039l.7598-1.728c.6533-1.4722,1.9141-2.208,3.7803-2.208,1.1865,0,2.1436.3335,2.8701,1s1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M141.4174,21.8461h1.6406v1.6963h4.3994v1.4399h-4.3994v4.3838h-9.2002v-1.4561h7.5596v-2.9277h-4.0195v-1.4399h4.0195v-1.6963Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-5" data-name="MTEXT">
      <g>
        <path d="M173.0434,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0137,1.0718-3.5205,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3203-.6958s.8604-1.0908.8604-1.8799c0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6133,0-1.1064.1357-1.4805.4077-.373.272-.6992.7334-.9795,1.3843l-.8203,1.856c-.5996,1.5039-1.8262,2.2559-3.6797,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1592-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8662,0-1.5361.1919-2.0098.5757-.4727.3843-.71.9336-.71,1.6479,0,.6191.1904,1.1069.5703,1.4644s.8896.5356,1.5293.5356c.5869,0,1.0742-.1543,1.46-.4639.3877-.3091.7275-.8105,1.0205-1.5039l.7598-1.728c.6533-1.4722,1.9141-2.208,3.7803-2.208,1.1865,0,2.1436.3335,2.8701,1s1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M172.9438,25.7181c0,1.0347-.3506,1.8936-1.0508,2.5762-.6992.6826-1.6025,1.0508-2.71,1.104v-1.4722c.6533-.0322,1.1738-.2534,1.5605-.6641s.5801-.9307.5801-1.5601c0-.7466-.2871-1.333-.8604-1.7598s-1.3535-.6401-2.3398-.6401c-.96,0-1.7197.1973-2.2803.5918-.5596.395-.8398.9336-.8398,1.6162,0,.5977.1367,1.0801.4102,1.4478.2734.3682.7168.6592,1.3301.8721v1.3604h-7.6201v-6.624h1.6201v5.2959h4.1797c-.9326-.5547-1.4004-1.4023-1.4004-2.5439,0-1.0454.4102-1.8774,1.2305-2.4961s1.9238-.9282,3.3096-.9282c1.4141,0,2.5801.3521,3.5,1.0562s1.3809,1.627,1.3809,2.7681Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-6" data-name="MTEXT">
      <g>
        <path d="M197.683,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0137,1.0718-3.5205,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3203-.6958s.8604-1.0908.8604-1.8799c0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6133,0-1.1064.1357-1.4805.4077-.373.272-.6992.7334-.9795,1.3843l-.8203,1.856c-.5996,1.5039-1.8262,2.2559-3.6797,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1592-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8662,0-1.5361.1919-2.0098.5757-.4727.3843-.71.9336-.71,1.6479,0,.6191.1904,1.1069.5703,1.4644s.8896.5356,1.5293.5356c.5869,0,1.0742-.1543,1.46-.4639.3877-.3091.7275-.8105,1.0205-1.5039l.7598-1.728c.6533-1.4722,1.9141-2.208,3.7803-2.208,1.1865,0,2.1436.3335,2.8701,1s1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M187.9028,25.8783c0-1.1733.46-2.1094,1.3799-2.8081s2.0742-1.0479,3.46-1.0479c1.3877,0,2.5439.3491,3.4707,1.0479s1.3896,1.6348,1.3896,2.8081c0,1.1626-.4629,2.0933-1.3896,2.792s-2.083,1.0479-3.4707,1.0479c-1.1064,0-2.0859-.2344-2.9395-.7041l-6.04-3.4238v-1.7441l4.1602,2.4482c-.0137-.0854-.0205-.2241-.0205-.416ZM195.1127,27.6703c.6201-.4375.9307-1.0347.9307-1.792s-.3105-1.3574-.9307-1.8003c-.6201-.4424-1.4102-.6641-2.3701-.6641-.9463,0-1.7295.2217-2.3496.6641-.6201.4429-.9297,1.043-.9297,1.8003s.3096,1.3545.9297,1.792,1.4033.6558,2.3496.6558c.96,0,1.75-.2183,2.3701-.6558Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-7" data-name="MTEXT">
      <g>
        <path d="M222.8178,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0137,1.0718-3.5205,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3203-.6958s.8604-1.0908.8604-1.8799c0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6133,0-1.1064.1357-1.4805.4077-.373.272-.6992.7334-.9795,1.3843l-.8203,1.856c-.5996,1.5039-1.8262,2.2559-3.6797,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1592-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8662,0-1.5361.1919-2.0098.5757-.4727.3843-.71.9336-.71,1.6479,0,.6191.1904,1.1069.5703,1.4644s.8896.5356,1.5293.5356c.5869,0,1.0742-.1543,1.46-.4639.3877-.3091.7275-.8105,1.0205-1.5039l.7598-1.728c.6533-1.4722,1.9141-2.208,3.7803-2.208,1.1865,0,2.1436.3335,2.8701,1s1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M222.4975,29.4462l-11.9199-5.0562v5.2959h-1.6797v-7.4878l13.5996,5.6318v1.6162Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-8" data-name="MTEXT">
      <g>
        <path d="M247.5473,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0132,1.0718-3.52,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3198-.6958.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6128,0-1.1064.1357-1.48.4077-.373.272-.6997.7334-.98,1.3843l-.8198,1.856c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1597-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8667,0-1.5366.1919-2.0098.5757-.4731.3843-.71.9336-.71,1.6479,0,.6191.1899,1.1069.5698,1.4644s.8901.5356,1.5298.5356c.5869,0,1.0737-.1543,1.46-.4639.3872-.3091.7271-.8105,1.02-1.5039l.7603-1.728c.6533-1.4722,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.3335,2.8701,1,.7271.6665,1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M246.3676,22.9901c.7197.6987,1.0801,1.6294,1.0801,2.792s-.3604,2.0908-1.0801,2.7842c-.7202.6934-1.6802,1.04-2.8804,1.04-.8262,0-1.5298-.168-2.1099-.5044-.5801-.3359-.9766-.8076-1.1899-1.416-.2129.5444-.6099.9736-1.1899,1.2881-.5801.3149-1.2432.4722-1.9902.4722-1.0664,0-1.9331-.3384-2.5996-1.0161-.6665-.6772-1-1.5601-1-2.6479s.3335-1.9731,1-2.6558,1.5332-1.0244,2.5996-1.0244c.7471,0,1.4072.1631,1.98.4883.5737.3252.9736.7549,1.2002,1.2881.2134-.6084.6099-1.0825,1.1899-1.4243.5801-.3413,1.2837-.5117,2.1099-.5117,1.2002,0,2.1602.3496,2.8804,1.0479ZM239.4477,25.7821c0-.6719-.2065-1.2026-.6201-1.5918-.4131-.3896-.96-.584-1.6401-.584-.7065,0-1.2666.1973-1.6802.5918-.4131.3945-.6196.9229-.6196,1.584,0,.6616.2065,1.187.6196,1.5762.4136.3892.9736.584,1.6802.584.6802,0,1.2271-.2002,1.6401-.6001.4136-.3999.6201-.9199.6201-1.5601ZM245.2074,27.4779c.4536-.4263.6802-.9917.6802-1.6958s-.2266-1.272-.6802-1.7041c-.4531-.4316-1.0601-.6479-1.8198-.6479-.7603,0-1.3667.2134-1.8203.6401-.4531.4268-.6797.9976-.6797,1.7119,0,.6826.23,1.2427.6899,1.6802s1.0635.6558,1.8101.6558c.7598,0,1.3667-.2134,1.8198-.6401Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-9" data-name="MTEXT">
      <g>
        <path d="M272.3832,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0132,1.0718-3.52,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3198-.6958.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6128,0-1.1064.1357-1.48.4077-.373.272-.6997.7334-.98,1.3843l-.8198,1.856c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1597-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8667,0-1.5366.1919-2.0098.5757-.4731.3843-.71.9336-.71,1.6479,0,.6191.1899,1.1069.5698,1.4644s.8901.5356,1.5298.5356c.5869,0,1.0737-.1543,1.46-.4639.3872-.3091.7271-.8105,1.02-1.5039l.7603-1.728c.6533-1.4722,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.3335,2.8701,1,.7271.6665,1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M263.0834,22.2142c1.1069,0,2.0869.2349,2.9399.7041l6.04,3.4238v1.7441l-4.1602-2.4482c.0137.0854.02.2241.02.416,0,1.1733-.46,2.1094-1.3799,2.8081s-2.0732,1.0479-3.46,1.0479-2.543-.3491-3.4702-1.0479c-.9263-.6987-1.3896-1.6348-1.3896-2.8081,0-1.1626.4634-2.0933,1.3896-2.792.9272-.6982,2.0835-1.0479,3.4702-1.0479ZM263.0834,28.5179c.9468,0,1.73-.2212,2.3501-.6636.6201-.4429.9297-1.043.9297-1.8003s-.3096-1.3545-.9297-1.792c-.6201-.437-1.4033-.6558-2.3501-.6558-.96,0-1.75.2188-2.3701.6558-.6201.4375-.9297,1.0347-.9297,1.792s.3096,1.3574.9297,1.8003c.6201.4424,1.4102.6636,2.3701.6636Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-10" data-name="MTEXT">
      <g>
        <path d="M303.4897,34.7098c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0132,1.0718-3.52,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3198-.6958.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6128,0-1.1064.1357-1.48.4077-.373.272-.6997.7334-.98,1.3843l-.8198,1.856c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1597-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8667,0-1.5366.1919-2.0098.5757-.4731.3843-.71.9336-.71,1.6479,0,.6191.1899,1.1069.5698,1.4644s.8901.5356,1.5298.5356c.5869,0,1.0737-.1543,1.46-.4639.3872-.3091.7271-.8105,1.02-1.5039l.7603-1.728c.6533-1.4722,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.3335,2.8701,1,.7271.6665,1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M303.1698,27.558h-9.7402v2.272h-1.5396c0-.9492-.167-1.584-.5-1.9038-.3335-.3198-.9404-.48-1.8203-.48v-1.3281h13.6001v1.4399Z" fill="#67707f" stroke-width="0"/>
        <path d="M301.6,16.3822c1.1934.6987,1.79,1.7197,1.79,3.064s-.5967,2.3652-1.79,3.064-2.9434,1.0479-5.25,1.0479c-2.2935,0-4.0366-.3491-5.23-1.0479s-1.79-1.7202-1.79-3.064.5967-2.3652,1.79-3.064,2.9365-1.0483,5.23-1.0483c2.3066,0,4.0566.3496,5.25,1.0483ZM301.79,19.4462c0-1.8135-1.8135-2.7202-5.4399-2.7202-3.6133,0-5.4204.9067-5.4204,2.7202,0,1.8237,1.8071,2.7358,5.4204,2.7358,3.6265,0,5.4399-.9121,5.4399-2.7358Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-11" data-name="MTEXT">
      <g>
        <path d="M416.9301,36.0936c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0132,1.0718-3.52,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3198-.6958.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6128,0-1.1064.1357-1.48.4077-.373.272-.6997.7334-.98,1.3843l-.8198,1.856c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1597-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8667,0-1.5366.1919-2.0098.5757-.4731.3843-.71.9336-.71,1.6479,0,.6191.1899,1.1069.5698,1.4644s.8901.5356,1.5298.5356c.5869,0,1.0737-.1543,1.46-.4639.3872-.3091.7271-.8105,1.02-1.5039l.7603-1.728c.6533-1.4722,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.3335,2.8701,1,.7271.6665,1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M416.6103,28.9418h-9.7402v2.272h-1.5396c0-.9492-.167-1.584-.5-1.9038-.3335-.3198-.9404-.48-1.8203-.48v-1.3281h13.6001v1.4399Z" fill="#67707f" stroke-width="0"/>
        <path d="M416.6103,24.8622c-1.5063,0-2.7266-.208-3.6602-.624-.9331-.416-1.7197-1.1094-2.3599-2.0801l-1.0801-1.6641c-.6528-1.0029-1.6001-1.5039-2.8398-1.5039-.6802,0-1.23.1758-1.6499.5273-.4199.3525-.6304.8115-.6304,1.376,0,.7041.2734,1.251.8203,1.6406s1.3198.584,2.3198.584v1.5039c-1.4399,0-2.5898-.3389-3.4497-1.0156-.8604-.6777-1.29-1.582-1.29-2.7129,0-1.0557.3398-1.8926,1.02-2.5117.6797-.6182,1.6064-.9277,2.7798-.9277,1.7734,0,3.1401.7041,4.1001,2.1123l1.1001,1.6631c.4531.6729.9033,1.1416,1.3496,1.4082.4473.2666,1.0571.4375,1.8301.5127v-5.7129h1.6401v7.4248Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-12" data-name="MTEXT">
      <g>
        <path d="M442.0224,36.0956c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M441.7026,28.9442h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
        <path d="M441.9228,21.2001c0,1.2158-.397,2.168-1.1904,2.8555-.793.6885-1.8965,1.0322-3.3096,1.0322v-1.4883c.9331,0,1.6567-.21,2.1699-.6318.5132-.4209.77-1.0107.77-1.7676,0-.6611-.2266-1.1973-.6802-1.6084-.4531-.4102-1.0464-.6162-1.7798-.6162-.8003,0-1.417.1982-1.8501.5928s-.6499.9648-.6499,1.7119v.7998h-1.5801v-.8164c0-.6611-.1934-1.165-.5801-1.5117s-.9468-.5205-1.6802-.5205c-.6396,0-1.1597.1846-1.5601.5527-.3999.3682-.5996.8506-.5996,1.4473,0,.6621.23,1.1904.6899,1.584.46.3955,1.0835.5928,1.8696.5928v1.4238c-1.2397,0-2.23-.3252-2.9697-.9766-.7402-.6504-1.1099-1.5244-1.1099-2.624,0-1.0449.3369-1.8877,1.0098-2.5273.6733-.6406,1.5635-.96,2.6699-.96,1.5737,0,2.5801.6611,3.02,1.9834.1069-.6504.4668-1.1836,1.0801-1.5996s1.3599-.624,2.2402-.624c1.1865,0,2.1533.3438,2.8999,1.0322.7466.6875,1.1201,1.5762,1.1201,2.6641Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-13" data-name="MTEXT">
      <g>
        <path d="M466.6181,36.0956c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M466.2983,28.9442h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
        <path d="M460.2582,17.1522h1.6401v1.6953h4.3999v1.4404h-4.3999v4.3838h-9.2002v-1.4561h7.5601v-2.9277h-4.02v-1.4404h4.02v-1.6953Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-14" data-name="MTEXT">
      <g>
        <path d="M491.6923,36.0956c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M491.3725,28.9442h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
        <path d="M491.5927,21.0233c0,1.0352-.3501,1.8936-1.0503,2.5762-.6997.6826-1.603,1.0508-2.71,1.1045v-1.4727c.6533-.0312,1.1733-.2529,1.5601-.6631.3867-.4111.5801-.9307.5801-1.5605,0-.7461-.2866-1.333-.8599-1.7598s-1.3535-.6396-2.3403-.6396c-.96,0-1.7197.1973-2.2798.5918s-.8403.9336-.8403,1.6152c0,.5977.1372,1.0801.4102,1.4482.2734.3682.7168.6592,1.3301.8721v1.3604h-7.6201v-6.624h1.6201v5.2959h4.1802c-.9331-.5547-1.4004-1.4023-1.4004-2.5439,0-1.0449.4102-1.877,1.23-2.4961.8203-.6182,1.9238-.9277,3.3101-.9277,1.4136,0,2.5801.3516,3.5,1.0557s1.3804,1.627,1.3804,2.7676Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-15" data-name="MTEXT">
      <g>
        <path d="M516.7118,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M516.392,28.9423h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
        <path d="M506.9321,21.1815c0-1.1729.46-2.1094,1.3799-2.8076s2.0737-1.0479,3.46-1.0479c1.3872,0,2.5435.3496,3.4702,1.0479s1.3901,1.6348,1.3901,2.8076c0,1.1631-.4634,2.0938-1.3901,2.792-.9268.6992-2.083,1.0479-3.4702,1.0479-1.1064,0-2.0864-.2344-2.9399-.7031l-6.04-3.4248v-1.7432l4.1602,2.4473c-.0132-.085-.02-.2236-.02-.416ZM514.142,22.9735c.6201-.4365.9302-1.0342.9302-1.792,0-.7568-.3101-1.3564-.9302-1.7998-.6201-.4424-1.4102-.6641-2.3701-.6641-.9463,0-1.73.2217-2.3501.6641-.6196.4434-.9297,1.043-.9297,1.7998,0,.7578.3101,1.3555.9297,1.792.6201.4375,1.4038.6562,2.3501.6562.96,0,1.75-.2188,2.3701-.6562Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-16" data-name="MTEXT">
      <g>
        <path d="M541.7353,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M541.4155,28.9423h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
        <path d="M541.4155,24.7499l-11.9199-5.0557v5.2959h-1.6802v-7.4883l13.6001,5.6318v1.6162Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-17" data-name="MTEXT">
      <g>
        <path d="M566.1435,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M565.8237,28.9423h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
        <path d="M564.9638,18.2938c.7197.6992,1.0801,1.6299,1.0801,2.792,0,1.1631-.3604,2.0908-1.0801,2.7842-.7202.6934-1.6802,1.04-2.8804,1.04-.8262,0-1.5298-.168-2.1099-.5039s-.9766-.8086-1.1899-1.416c-.2129.5439-.6099.9736-1.1899,1.2881s-1.2432.4717-1.9902.4717c-1.0664,0-1.9331-.3389-2.5996-1.0156-.6665-.6777-1-1.5605-1-2.6484s.3335-1.9736,1-2.6562,1.5332-1.0234,2.5996-1.0234c.7471,0,1.4072.1621,1.98.4873.5737.3262.9736.7549,1.2002,1.2881.2134-.6074.6099-1.082,1.1899-1.4238.5801-.3408,1.2837-.5117,2.1099-.5117,1.2002,0,2.1602.3496,2.8804,1.0479ZM558.0439,21.0858c0-.6719-.2065-1.2021-.6201-1.5918-.4131-.3896-.96-.584-1.6401-.584-.7065,0-1.2666.1973-1.6802.5918-.4131.3945-.6196.9229-.6196,1.584s.2065,1.1865.6196,1.5762c.4136.3896.9736.584,1.6802.584.6802,0,1.2271-.2002,1.6401-.6006.4136-.3994.6201-.9199.6201-1.5596ZM563.8036,22.7821c.4536-.4268.6802-.9922.6802-1.6963s-.2266-1.2725-.6802-1.7041c-.4531-.4316-1.0601-.6475-1.8198-.6475-.7603,0-1.3667.2129-1.8203.6396-.4531.4268-.6797.9971-.6797,1.7119,0,.6826.23,1.2432.6899,1.6797.46.4375,1.0635.6562,1.8101.6562.7598,0,1.3667-.2129,1.8198-.6396Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-18" data-name="MTEXT">
      <g>
        <path d="M591.2685,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M590.9487,28.9423h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
        <path d="M581.9687,17.5175c1.1069,0,2.0869.2354,2.9399.7041l6.04,3.4238v1.7441l-4.1602-2.4473c.0137.085.02.2236.02.416,0,1.1729-.46,2.1094-1.3799,2.8076s-2.0732,1.0479-3.46,1.0479-2.543-.3496-3.4702-1.0479c-.9263-.6982-1.3896-1.6348-1.3896-2.8076,0-1.1631.4634-2.0938,1.3896-2.792.9272-.6992,2.0835-1.0488,3.4702-1.0488ZM581.9687,23.8221c.9468,0,1.73-.2217,2.3501-.6641s.9297-1.043.9297-1.7998c0-.7578-.3096-1.3545-.9297-1.792s-1.4033-.6562-2.3501-.6562c-.96,0-1.75.2188-2.3701.6562s-.9297,1.0342-.9297,1.792c0,.7568.3096,1.3574.9297,1.7998s1.4102.6641,2.3701.6641Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-19" data-name="MTEXT">
      <g>
        <path d="M615.8407,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M615.5209,30.9423c-1.5063,0-2.7266-.208-3.6602-.624-.9331-.416-1.7197-1.1094-2.3599-2.0801l-1.0801-1.6641c-.6528-1.0029-1.6001-1.5039-2.8398-1.5039-.6802,0-1.23.1758-1.6499.5273-.4199.3525-.6304.8115-.6304,1.376,0,.7041.2734,1.251.8203,1.6406s1.3198.584,2.3198.584v1.5039c-1.4399,0-2.5898-.3389-3.4497-1.0156-.8604-.6777-1.29-1.582-1.29-2.7129,0-1.0557.3398-1.8926,1.02-2.5117.6797-.6182,1.6064-.9277,2.7798-.9277,1.7734,0,3.1401.7041,4.1001,2.1123l1.1001,1.6631c.4531.6729.9033,1.1416,1.3496,1.4082.4473.2666,1.0571.4375,1.8301.5127v-5.7129h1.6401v7.4248Z" fill="#67707f" stroke-width="0"/>
        <path d="M613.9511,14.7421c1.1934.6992,1.79,1.7207,1.79,3.0645s-.5967,2.3652-1.79,3.0635c-1.1934.6992-2.9434,1.0488-5.25,1.0488-2.2935,0-4.0366-.3496-5.23-1.0488-1.1934-.6982-1.79-1.7197-1.79-3.0635s.5967-2.3652,1.79-3.0645c1.1934-.6982,2.9365-1.0479,5.23-1.0479,2.3066,0,4.0566.3496,5.25,1.0479ZM614.141,17.8065c0-1.8135-1.8135-2.7197-5.4399-2.7197-3.6133,0-5.4204.9062-5.4204,2.7197,0,1.8242,1.8071,2.7363,5.4204,2.7363,3.6265,0,5.4399-.9121,5.4399-2.7363Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-20" data-name="MTEXT">
      <g>
        <path d="M641.2724,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M640.9526,30.9423c-1.5063,0-2.7266-.208-3.6602-.624-.9331-.416-1.7197-1.1094-2.3599-2.0801l-1.0801-1.6641c-.6528-1.0029-1.6001-1.5039-2.8398-1.5039-.6802,0-1.23.1758-1.6499.5273-.4199.3525-.6304.8115-.6304,1.376,0,.7041.2734,1.251.8203,1.6406s1.3198.584,2.3198.584v1.5039c-1.4399,0-2.5898-.3389-3.4497-1.0156-.8604-.6777-1.29-1.582-1.29-2.7129,0-1.0557.3398-1.8926,1.02-2.5117.6797-.6182,1.6064-.9277,2.7798-.9277,1.7734,0,3.1401.7041,4.1001,2.1123l1.1001,1.6631c.4531.6729.9033,1.1416,1.3496,1.4082.4473.2666,1.0571.4375,1.8301.5127v-5.7129h1.6401v7.4248Z" fill="#67707f" stroke-width="0"/>
        <path d="M640.9526,19.8387h-9.7402v2.2715h-1.5396c0-.9492-.167-1.584-.5-1.9043-.3335-.3193-.9404-.4795-1.8203-.4795v-1.3281h13.6001v1.4404Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-21" data-name="MTEXT">
      <g>
        <path d="M665.9306,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M665.6108,30.9423c-1.5063,0-2.7266-.208-3.6602-.624-.9331-.416-1.7197-1.1094-2.3599-2.0801l-1.0801-1.6641c-.6528-1.0029-1.6001-1.5039-2.8398-1.5039-.6802,0-1.23.1758-1.6499.5273-.4199.3525-.6304.8115-.6304,1.376,0,.7041.2734,1.251.8203,1.6406s1.3198.584,2.3198.584v1.5039c-1.4399,0-2.5898-.3389-3.4497-1.0156-.8604-.6777-1.29-1.582-1.29-2.7129,0-1.0557.3398-1.8926,1.02-2.5117.6797-.6182,1.6064-.9277,2.7798-.9277,1.7734,0,3.1401.7041,4.1001,2.1123l1.1001,1.6631c.4531.6729.9033,1.1416,1.3496,1.4082.4473.2666,1.0571.4375,1.8301.5127v-5.7129h1.6401v7.4248Z" fill="#67707f" stroke-width="0"/>
        <path d="M665.6108,21.8387c-1.5063,0-2.7266-.208-3.6602-.624-.9331-.416-1.7197-1.1094-2.3599-2.0801l-1.0801-1.6641c-.6528-1.0029-1.6001-1.5039-2.8398-1.5039-.6802,0-1.23.1758-1.6499.5273-.4199.3525-.6304.8115-.6304,1.376,0,.7041.2734,1.251.8203,1.6406s1.3198.584,2.3198.584v1.5039c-1.4399,0-2.5898-.3389-3.4497-1.0156-.8604-.6777-1.29-1.582-1.29-2.7129,0-1.0557.3398-1.8926,1.02-2.5117.6797-.6182,1.6064-.9277,2.7798-.9277,1.7734,0,3.1401.7041,4.1001,2.1123l1.1001,1.6631c.4531.6729.9033,1.1416,1.3496,1.4082.4473.2666,1.0571.4375,1.8301.5127v-5.7129h1.6401v7.4248Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-22" data-name="MTEXT">
      <g>
        <path d="M691.0849,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M690.7651,30.9423c-1.5063,0-2.7266-.208-3.6602-.624-.9331-.416-1.7197-1.1094-2.3599-2.0801l-1.0801-1.6641c-.6528-1.0029-1.6001-1.5039-2.8398-1.5039-.6802,0-1.23.1758-1.6499.5273-.4199.3525-.6304.8115-.6304,1.376,0,.7041.2734,1.251.8203,1.6406s1.3198.584,2.3198.584v1.5039c-1.4399,0-2.5898-.3389-3.4497-1.0156-.8604-.6777-1.29-1.582-1.29-2.7129,0-1.0557.3398-1.8926,1.02-2.5117.6797-.6182,1.6064-.9277,2.7798-.9277,1.7734,0,3.1401.7041,4.1001,2.1123l1.1001,1.6631c.4531.6729.9033,1.1416,1.3496,1.4082.4473.2666,1.0571.4375,1.8301.5127v-5.7129h1.6401v7.4248Z" fill="#67707f" stroke-width="0"/>
        <path d="M690.9853,18.1747c0,1.2158-.397,2.168-1.1904,2.8555-.793.6885-1.8965,1.0322-3.3096,1.0322v-1.4883c.9331,0,1.6567-.21,2.1699-.6318.5132-.4209.77-1.0107.77-1.7676,0-.6611-.2266-1.1973-.6802-1.6084-.4531-.4102-1.0464-.6162-1.7798-.6162-.8003,0-1.417.1982-1.8501.5928s-.6499.9648-.6499,1.7119v.7998h-1.5801v-.8164c0-.6611-.1934-1.165-.5801-1.5117s-.9468-.5205-1.6802-.5205c-.6396,0-1.1597.1846-1.5601.5527-.3999.3682-.5996.8506-.5996,1.4473,0,.6621.23,1.1904.6899,1.584.46.3955,1.0835.5928,1.8696.5928v1.4238c-1.2397,0-2.23-.3252-2.9697-.9766-.7402-.6504-1.1099-1.5244-1.1099-2.624,0-1.0449.3369-1.8877,1.0098-2.5273.6733-.6406,1.5635-.96,2.6699-.96,1.5737,0,2.5801.6611,3.02,1.9834.1069-.6504.4668-1.1836,1.0801-1.5996s1.3599-.624,2.2402-.624c1.1865,0,2.1533.3438,2.8999,1.0322.7466.6875,1.1201,1.5762,1.1201,2.6641Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-23" data-name="MTEXT">
      <g>
        <path d="M716.7236,36.0936c0,1.2695-.4199,2.2617-1.2598,2.9766s-2.0132,1.0723-3.52,1.0723v-1.4561c.9736,0,1.7471-.2324,2.3198-.6963.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6318-.4062-.3848-.9766-.5762-1.71-.5762-.6128,0-1.1064.1357-1.48.4082-.373.2715-.6997.7334-.98,1.3838l-.8198,1.8555c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9756s-1.04-1.498-1.04-2.5439c0-1.0986.3867-1.9785,1.1597-2.6396.7734-.6611,1.8135-.9922,3.1201-.9922v1.4561c-.8667,0-1.5366.1914-2.0098.5762-.4731.3838-.71.9336-.71,1.6475,0,.6191.1899,1.1064.5698,1.4639s.8901.5361,1.5298.5361c.5869,0,1.0737-.1543,1.46-.4639.3872-.3096.7271-.8105,1.02-1.5039l.7603-1.7285c.6533-1.4717,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.334,2.8701,1,.7271.667,1.0898,1.5557,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M716.4037,30.9423c-1.5063,0-2.7266-.208-3.6602-.624-.9331-.416-1.7197-1.1094-2.3599-2.0801l-1.0801-1.6641c-.6528-1.0029-1.6001-1.5039-2.8398-1.5039-.6802,0-1.23.1758-1.6499.5273-.4199.3525-.6304.8115-.6304,1.376,0,.7041.2734,1.251.8203,1.6406s1.3198.584,2.3198.584v1.5039c-1.4399,0-2.5898-.3389-3.4497-1.0156-.8604-.6777-1.29-1.582-1.29-2.7129,0-1.0557.3398-1.8926,1.02-2.5117.6797-.6182,1.6064-.9277,2.7798-.9277,1.7734,0,3.1401.7041,4.1001,2.1123l1.1001,1.6631c.4531.6729.9033,1.1416,1.3496,1.4082.4473.2666,1.0571.4375,1.8301.5127v-5.7129h1.6401v7.4248Z" fill="#67707f" stroke-width="0"/>
        <path d="M710.3637,14.1268h1.6401v1.6953h4.3999v1.4404h-4.3999v4.3838h-9.2002v-1.4561h7.5601v-2.9277h-4.02v-1.4404h4.02v-1.6953Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
    <g id="MTEXT-24" data-name="MTEXT">
      <g>
        <path d="M393.1918,36.0936c0,1.2695-.4199,2.2612-1.2598,2.9761-.8398.7144-2.0132,1.0718-3.52,1.0718v-1.4561c.9736,0,1.7471-.2319,2.3198-.6958.5737-.4639.8604-1.0908.8604-1.8799,0-.7041-.2031-1.248-.6104-1.6323-.4062-.3838-.9766-.5757-1.71-.5757-.6128,0-1.1064.1357-1.48.4077-.373.272-.6997.7334-.98,1.3843l-.8198,1.856c-.6001,1.5039-1.8267,2.2559-3.6802,2.2559-1.1201,0-2.0264-.3252-2.7197-.9761-.6934-.6504-1.04-1.4985-1.04-2.5439,0-1.0986.3867-1.9785,1.1597-2.6401.7734-.6611,1.8135-.9917,3.1201-.9917v1.4561c-.8667,0-1.5366.1919-2.0098.5757-.4731.3843-.71.9336-.71,1.6479,0,.6191.1899,1.1069.5698,1.4644s.8901.5356,1.5298.5356c.5869,0,1.0737-.1543,1.46-.4639.3872-.3091.7271-.8105,1.02-1.5039l.7603-1.728c.6533-1.4722,1.9136-2.208,3.7798-2.208,1.187,0,2.1436.3335,2.8701,1,.7271.6665,1.0898,1.5547,1.0898,2.6641Z" fill="#67707f" stroke-width="0"/>
        <path d="M392.872,28.9418h-9.7402v2.272h-1.5396c0-.9492-.167-1.584-.5-1.9038-.3335-.3198-.9404-.48-1.8203-.48v-1.3281h13.6001v1.4399Z" fill="#67707f" stroke-width="0"/>
        <path d="M392.872,22.8617h-9.7402v2.272h-1.5396c0-.9492-.167-1.584-.5-1.9038-.3335-.3198-.9404-.48-1.8203-.48v-1.3281h13.6001v1.4399Z" fill="#67707f" stroke-width="0"/>
      </g>
    </g>
  </g>
</svg>