//@ts-nocheck
import * as React from 'react'
import {useEffect, useState} from 'react'
import Letter from "./BuildingLetter";
import FloorsWrapper from "./FloorsWrapper";

export default function BuildingWrapper(props) {
    const [hovered, setHovered] = useState(false);
    const [active, setProps] = useState(false)
    const [zoomed, setZoomed] = useState(false)
    const {cameraControlsRef} = props;

    const unZoom = () => {
        setZoomed(false)
        props.stateChanger("cameraReset")
    }

    useEffect(() => {
        setProps(false)
    }, [props])

    return (
        <group onClick={() => {
            if (props.isMobile) {
                cameraControlsRef.current?.setLookAt(props.buildingZoomMobile[0], props.buildingZoomMobile[1], props.buildingZoomMobile[2], props.buildingZoomMobile[3], props.buildingZoomMobile[4], props.buildingZoomMobile[5], true)
                setProps(true)
                setZoomed(true)
            } else {
                if (zoomed) {
                    props.stateChanger('disableSpace', false)
                } else {
                    cameraControlsRef.current?.setLookAt(props.buildingZoomMobile[0], props.buildingZoomMobile[1], props.buildingZoomMobile[2] - 40, props.buildingZoomMobile[3], props.buildingZoomMobile[4], props.buildingZoomMobile[5], true)
                    setProps(true)
                    setZoomed(true)
                    props.stateChanger('disableSpace', false)
                }
            }
        }} onPointerOver={() => setHovered(true)} onPointerOut={() => setHovered(false)}
               onPointerMissed={() => {
                   setProps(false)
                   setZoomed(false)
               }}>
            <FloorsWrapper zoomed={zoomed} unZoom={unZoom} x={props.x} isMobile={props.isMobile}
                           cameraRef={cameraControlsRef} z={props.z} hovered={true} letter={props.letter}
                           active={props.setActive} flatsAvaibility={props.flatsAvaibility}
                           rotation={props.rotation} geometry={props.geometry} stateChanger={props.stateChanger}/>
            <Letter active={active} isSpaceEnabled={props.spaceIsEnabled} hovered={hovered} letter={props.letter} position={props.letterPosition}/>
        </group>
    )
}

