import {Alert} from "flowbite-react";
import Link from "next/link";

export default function TableMobile({filteredFlats}) {
    console.log(filteredFlats);
    return (
        <>
            {filteredFlats && filteredFlats.length > 0 ?
                <>
                    {filteredFlats.map((flat, index) =>
                        <div key={index}
                             className={"flex flex-col gap-4 pt-5 border-b-4 border-[#657492]"}>
                            <div className={"flex gap-1 justify-evenly"}>
                                <div className={"flex flex-col items-center justify-between gap-4"}>
                                    <small className={"text-[3vw] font-medium"}>DOM</small>
                                    <small className={"text-[5vw] font-medium"}>{flat.budova}</small>
                                </div>
                                <div className={"flex flex-col items-center justify-between gap-4"}>
                                    <small className={"text-[3vw] font-medium"}>PODLAŽIE</small>
                                    <small className={"text-[5vw] font-medium"}>{flat.poschodie}</small>
                                </div>
                                <div className={"flex flex-col items-center justify-between gap-4"}>
                                    <small className={"text-[3vw] font-medium"}>ČÍSLO BYTU</small>
                                    <small
                                        className={"bg-black text-[4vw] text-white px-4 py-1 rounded-full"}>{flat["flatNumber"]}</small>
                                </div>
                                <div className={"flex flex-col items-center justify-between gap-[1vw]"}>
                                    <small className={"text-[3vw] font-medium"}>POČET IZIEB</small>
                                    <small className={"text-[5vw] font-medium"}>{flat.rooms}i</small>
                                </div>
                                <div className={"flex flex-col items-center justify-between gap-4"}>
                                    <small className={"text-[3vw] font-medium"}>VÝMERA INTERIÉRU</small>
                                    <small className={"text-[5vw] font-medium"}>{flat["plocha_interier"]}m²</small>
                                </div>
                            </div>
                            <div className={"flex justify-between gap-20"}>
                                <div className={"vymery w-full"}>
                                    <small className={"font-medium text-left mb-2"}>VÝMERA EXTERIÉRU</small>
                                    <div className={"flex justify-between text-right w-full mb-1"}>
                                        <span
                                            className={"bg-[#683107] w-full flex rounded-md items-center justify-center text-white"}>terasa</span>
                                        <span className={"text-[5vw] w-full font-light"}>{flat["terasa"]} m²</span>
                                    </div>
                                    <div className={"flex justify-between text-right w-full mb-1"}>
                                        <span
                                            className={"bg-[#7D904F] w-full flex rounded-md items-center justify-center text-white"}>záhrada</span>
                                        <span className={"text-[5vw] w-full font-light"}>{flat["zahrada"]} m²</span>
                                    </div>
                                    <div className={"flex justify-between text-right w-full mb-1"}>
                                        <span
                                            className={"bg-[#7D904F] w-full flex rounded-md items-center justify-center text-white"}>balkón</span>
                                        <span className={"text-[5vw] w-full font-light"}>{flat["balkon"]} m²</span>
                                    </div>
                                </div>
                                <div className={"flex flex-col w-1/2 justify-between text-right"}>
                                    <div className={"flex flex-col"}>
                                        <small className={"font-medium"}>CELKOVÁ VÝMERA</small>
                                        <small
                                            className={"text-[5vw] text-right font-medium"}>{flat["vymera"]}m²</small>
                                    </div>
                                    {flat["dostupnost"] !== 0 && <div className={"flex w-full flex-col"}>
                                        <small className={"font-medium"}>CENA S DPH</small>
                                        <small
                                            className={"text-[5vw] font-medium"}>{flat["price"]} €</small>
                                    </div>}
                                </div>
                            </div>

                            <div className={"flex gap-5 mb-4"}>
                                <div className={"flex text-left w-full flex-col"}>
                                    <small className={"font-medium mb-2"}>STAV</small>
                                    <div className={"flex flex-wrap gap-2"}>
                                        <span
                                            className={flat.dostupnost === 2 ? "bg-[#FFAB48] text-black w-full text-xl py-1 px-4 rounded-full font-light" : flat["dostupnost"] === 0 ? "bg-red-500 text-white w-full text-xl py-1 px-4 rounded-full font-light" : "bg-[#B4CC7A] text-black w-full text-xl py-1 px-4 rounded-full font-light"}>{flat["dostupnost"] === 1 ? "voľný" : flat["dostupnost"] === 2 ? "rezervovaný" : "predaný"}</span>
                                    </div>
                                </div>

                                <div className={"flex text-right w-full flex-col"}>
                                    {flat["dostupnost"] !== 0 &&
                                        <><small className={"font-medium mb-2"}>DETAIL</small><Link
                                            href={"/ponuka-bytov/" + flat["rooms"] + "-izbove/" + flat["id"]}
                                            className={"bg-[#657492] w-full py-2 text-center -ml-1 text-[4vw] text-white shadow-md rounded-full font-light"}>pôdorys
                                        </Link></>
                                    }
                                </div>
                            </div>
                        </div>)
                    }
                </>
                :
                <Alert color="info">
                    <span className="font-medium">Nič sme nenašli!</span> Vaším kritériám nevyhovuje žiaden z bytov,
                    upravte svoje kritériá a skúste to znova.
                </Alert>
            }
        </>
    )
}