import {useEffect, useState} from "react";
import {useRouter} from "next/router";
import {useParams} from "next/navigation";
import Link from "next/link";
import dynamic from "next/dynamic";

const TableDesktop = dynamic(() => import("./desktopFilter"))
const TableMobile = dynamic(() => import("./mobileFilter"))

export default function PrehladBytov({flats, room, isMobile}) {
    if (flats !== undefined) {
        flats = JSON.parse(flats);
    }
    const [filteredFlats, setFilteredFlats] = useState(flats);
    const [filtered, setFiltered] = useState(false);
    const [orderingVymera, setOrderingVymera] = useState(false);
    const [orderingCena, setOrderingCena] = useState(false);
    const [roomsCount, setRoomsCount] = useState(0);
    const [filteredFlatsCount, setFilteredFlatsCount] = useState();
    const router = useRouter();
    const params = useParams();
    let orderING: string;

    useEffect(() => {
        if (params.izba.includes("vsetky")) {
            handleChangeToSubmit({target: {id: 'clearFilter'}})
        } else {
            // @ts-ignore
            document.getElementById("roomSelect").value = router.asPath.match(/\/ponuka-bytov\/(\d+)/)[1]
            document.getElementById("filter").click();
        }
    }, [router.asPath])

    const handleChangeToSubmit = (e: any) => {
        if (e.target.id === "clearFilter" || e === "clearFilter") {
            const form = document.getElementById("filterino");
            //@ts-ignore
            form.reset();
            const selects = form.querySelectorAll("select");
            selects.forEach(select => {
                select.selectedIndex = 0;
            });
            setOrderingVymera(false);
            setOrderingCena(false)
            setFiltered(false);
            orderING = "";
        } else {
            if (e.target.ariaLabel !== null) {
                if (e.target.ariaLabel === "cena") {
                    setOrderingCena(true);
                    setOrderingVymera(false)
                    orderING = "price";
                } else {
                    setOrderingVymera(true);
                    setOrderingCena(false)
                    orderING = "vymera";
                }
            }
            setFiltered(true);
        }
        document.getElementById("filter").click();
    };

    const filterFlats = async (e: any) => {
        e.preventDefault();

        let budova: string, podlazie: string, rooms, stav;
        if (e.target.dom.value === "DOM") {
            budova = "";
        } else {
            budova = " AND budova = '" + e.target.dom.value + "'";
        }
        if (isNaN(e.target.podlazie.value)) {
            podlazie = "";
        } else {
            podlazie = " AND poschodie = " + e.target.podlazie.value;
        }
        if (isNaN(e.target.rooms.value)) {
            rooms = "";
        } else {
            rooms = " AND rooms = " + e.target.rooms.value;
        }
        if (isNaN(e.target.stav.value)) {
            stav = "";
        } else {
            stav = " AND dostupnost = " + e.target.stav.value;
        }

        const fetchOrdered = async (orderBy: string): Promise<void> => {
            let orderDirection = "desc";
            if (orderBy === "price")
                orderDirection = e.target.cenaOrder.value;
            if (orderBy === "vymera")
                orderDirection = e.target.vymeraOrder.value;
            try {
                const response = await fetch(
                    "/api/getOrderedFlats?floor=" + podlazie + "&building=" + budova + "&rooms=" + rooms + "&canrooms = " + e.target.rooms.value + "&stav=" + stav + "&ordering=" + (orderDirection === "asc" ? "ASC" : "DESC") + "&orderBy=" + orderBy);
                const result = await response.json();
                setFilteredFlats(result.data);
                setFilteredFlatsCount(result.count[0].count);
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        }

        if (orderING === "price" || orderING === "vymera") {
            await fetchOrdered(orderING)
        } else {
            try {
                const response = await fetch("/api/getFilteredFlats?floor=" + podlazie + "&building=" + budova + "&rooms=" + rooms + "&canrooms=" + e.target.rooms.value + "&stav=" + stav);
                const result = await response.json();
                console.log(result);
                setFilteredFlats(result.data);
                setFilteredFlatsCount(result.count);
                setRoomsCount(result.rooms);
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        }
    };

    return (
        <>
            <>
                <div
                    className={"bg-[#E2E2E7] relative z-10 px-5 lg:px-24 lg:rounded-t-[4rem] pb-28 mt-10 lg:mt-0 pt-10 lg:pt-36"}>
                    <form
                        id={"filterino"}
                        onSubmit={filterFlats}
                        className={"mb-5 flex-col lg:flex-row w-full"}
                    >
                        <div className={"flex flex-col justify-between gap-2 lg:flex-row"}>
                            <div className={"flex flex-col justify-between items-center gap-2 lg:flex-row"}>
                                <div className={"flex  flex-col items-start gap-1"}>
                                    <label><small>Dom</small></label>
                                    <select
                                        id={"dom"}
                                        className={"w-full rounded-xl outline-none border-0 shadow-md"}
                                        onChange={handleChangeToSubmit}
                                        name={"dom"}>
                                        <option selected hidden disabled>
                                            DOM
                                        </option>
                                        <option value={"A"}>A</option>
                                        <option value={"B"}>B</option>
                                        <option value={"C"}>C</option>
                                        <option value={"D"}>D</option>
                                        <option value={"E"}>E</option>
                                        <option value={"F"}>F</option>
                                    </select>
                                </div>
                                <div className={"flex flex-col items-start gap-1"}>
                                    <label><small>Nadzemné podlažie</small></label><select
                                    className={"w-full rounded-xl outline-none border-0 shadow-md"}
                                    name={"podlazie"}
                                    onChange={handleChangeToSubmit}
                                >
                                    <option selected hidden disabled>
                                        NADZEMNÉ PODLAŽIE
                                    </option>
                                    <option value={1}>1NP</option>
                                    <option value={2}>2NP</option>
                                    <option value={3}>3NP</option>
                                    <option value={4}>4NP</option>
                                    <option value={5}>5NP</option>
                                </select>
                                </div>
                                <div className={"flex flex-col items-start gap-1"}>
                                    <label><small>Počet izieb</small></label>
                                    <select id={"roomSelect"}
                                            className={"w-full rounded-xl outline-none border-0 shadow-md"}
                                            onChange={handleChangeToSubmit}
                                            name={"rooms"}>
                                        <option selected hidden disabled>
                                            POČET IZIEB
                                        </option>
                                        <option value={1}>1</option>
                                        <option value={2}>2</option>
                                        <option value={3}>3</option>
                                        <option value={4}>4</option>
                                        <option value={5}>5</option>
                                    </select>
                                </div>
                                <div className={"flex flex-col items-center gap-1"}>
                                    <label><small>Stav</small></label>
                                    <select
                                        className={"w-full rounded-xl outline-none border-0 shadow-md"}
                                        onChange={handleChangeToSubmit}
                                        name={"stav"}>
                                        <option selected hidden disabled>
                                            STAV
                                        </option>
                                        <option value={0}>Predaný</option>
                                        <option value={1}>Voľný</option>
                                        <option value={2}>Rezervovaný</option>
                                    </select>
                                </div>
                                {filtered && (
                                    <button
                                        onClick={handleChangeToSubmit}
                                        id={"clearFilter"}
                                        className={"bytButton uppercase flex shadow-md items-center px-6 justify-center mt-6 text-white py-2"}>
                                        Zrušiť filter
                                    </button>
                                )}
                                <button
                                    id={"filter"}
                                    hidden
                                    type={"submit"}
                                    className={"w-0 h-0"}
                                ></button>
                            </div>
                            <div className={"mt-4"}>
                                <Link href={"/Laurin_Dvor_Katalog_bytov_250210.pdf"} target={"_blank"}
                                      className={"flex bg-gray-500 text-white p-2 px-4 my-2 transition-all rounded-xl cursor-pointer items-center gap-4"}>
                                    <p>Stiahnuť katalóg bytov <small>(PDF 18,5MB)</small></p>
                                    <svg className="w-10 h-10 text-white" aria-hidden="true"
                                         xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                                         viewBox="0 0 24 24">
                                        <path fillRule="evenodd"
                                              d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z"
                                              clipRule="evenodd"/>
                                    </svg>
                                </Link>
                            </div>
                        </div>
                        {isMobile ? (
                            <TableMobile filteredFlats={filteredFlats}/>
                        ) : (
                            <TableDesktop room={roomsCount}
                                filteredFlats={filteredFlats} filteredFlatsCount={filteredFlatsCount}
                                handleChangeToSubmit={handleChangeToSubmit}
                                orderingVymera={orderingVymera} orderingCena={orderingCena} is3D={false}/>
                        )}

                    </form>
                </div>
            </>
        </>
    );
}
