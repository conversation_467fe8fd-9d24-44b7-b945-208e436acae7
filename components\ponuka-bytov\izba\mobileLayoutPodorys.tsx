import Image from "next/image";
import {Carousel} from "flowbite-react";
import Link from "next/link";
import Router from "next/router";

export default function MobileLayoutPodorys({flat, gallery, room}) {
    if (flat === undefined) {
        return null;
    }
    return (
        <>
            <section className={"flex flex-col items-center px-5 justify-between bg-[#E3E2E7] -mt-8 mb-20"}>
                <div className={"info w-full px-1 mb-6"}>
                    <div className={"flex justify-between mt-5 pt-8"}>
                        <div onClick={() => Router.back()}
                             className={"bg-black text-white flex items-center p-[0.5vw] rounded-full"}>
                            <svg className="w-[13vw] h-[13vw] text-white" aria-hidden="true"
                                 xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                      strokeWidth="2" d="M5 12h14M5 12l4-4m-4 4 4 4"/>
                            </svg>
                        </div>
                        <div className={"bg-black text-white rounded-full px-[5vw] flex items-center gap-2"}>
                            <span className={"text-[4vw] m-0 font-light"}>BYT ČÍSLO: </span><span
                            className={"text-[8vw] m-0 font-extralight"}>{flat?.flatNumber}</span>
                        </div>
                    </div>
                    <div className={"flex mt-5 justify-between items-center gap-4"}>
                        <div className={"flex flex-col"}>
                            <div className="mb-[2vw]">
                                <small className="uppercase text-[3vw]">PLOCHA INTERIÉRU</small>
                                <h3 className={"mb-5 text-[7vw]"}>{flat["plocha_interier"]}m²</h3>
                            </div>
                            <div className="mb-[2vw]">
                                <small className="uppercase text-[3vw]">POČET IZIEB</small>
                                <h3 className="text-[7vw] font-bold">{flat["rooms"]}-izbový</h3>
                            </div>
                        </div>
                        <div className={"flex flex-col items-end"}>
                            <div className="mb-[2vw] flex flex-col items-end justify-end">
                                <small className="uppercase text-[3vw] text-right"> PLOCHA EXTERIÉRU</small>
                                <h3 className={"mb-5 text-[7vw]"}>{flat["exterier_spolu"]}m²</h3>
                            </div>
                            <div className="mb-[2vw]">
                                <small className={"text-[3vw]"}>CELKOVÁ VÝMERA</small>
                                <h3 className={"text-[7vw] font-bold"}>{flat["vymera"]}m²</h3>
                            </div>
                        </div>
                    </div>
                    <div className={"text-center my-5"}>
                        <div
                            className={"bg-black flex items-center px-[2vw] justify-center gap-2 text-white mx-2 rounded-full py-[1vw]"}>
                            <small className="font-light text-[4vw]">CENA S DPH</small>
                            <h2 className={"text-[7vw]"}>{flat["price"].toString().replace(/.{3}/g, '$& ')}EUR</h2>
                        </div>
                    </div>
                </div>
                <Link href={"/byty/" + flat.flatNumber.replace(/\./g, '') + ".png"} target={"_blank"}>
                    <Image src={"/byty/" + flat.flatNumber.replace(/\./g, '') + ".png"} alt={"pôdorys bytu"}
                           width={400}
                           height={400}/>
                </Link>
            </section>
            <section className={"relative mb-24"}>
                <div className={"absolute z-20 -top-10 px-4 w-full flex justify-center"}>
                    <div className={"flex gap-4"}>
                        <Link href={"/galeria"}>
                            <button className={"bytButton uppercase text-white text-[4vw] px-[3vw] py-1"}>Galéria
                            </button>
                        </Link>
                        <Link href={"/technicke-specifikacie"}>
                            <button
                                className="bg-black contactButton text-white uppercase rounded-full px-[3vw] py-1">
                                <h4 className="font-medium text-nowrap text-[4vw]">Technická špecifikácia</h4></button>
                        </Link>
                    </div>
                </div>
                <div className="h-[30vh] relative bg-[#E3E2E7] z-50 sm:h-64 xl:h-80 2xl:h-[90vh]">
                    <Carousel>
                        {[...Array(gallery[flat["rooms"] - 1])].map((e, i) =>
                            <Image key={i} src={"/galeria/" + room + "/" + i + ".webp"}
                                   alt={"galeria"}
                                   width={1920}
                                   height={1080}/>
                        )}
                    </Carousel>
                </div>
            </section>
        </>
    )
}