import Image from "next/image";
import {useEffect, useRef, useState} from "react";
import toast from "react-hot-toast";
import Router from "next/router";

export default function VisualisationOverlay({numberOfFlats, getter, isOnHome, isMobile, room, selectedBuild}) {
    const vizozka = useRef(null)
    const [imageData, setImageData] = useState([])
    const [imageLoaded, setImageLoaded] = useState(false)
    const [hovered, setHovered] = useState(false)
    const [hoveredBuilding, setHoveredBuilding] = useState("")
    const [selectedBuilding, setSelectedBuilding] = useState("")

    useEffect(() => {
        setSelectedBuilding(selectedBuild)
    }, [selectedBuild]);

    const handleResize = () => {
        setImageLoaded(true);
        if (vizozka === null) {
            console.error("Image is not yet loaded!")
        } else {
            setImageData([
                vizozka.current.clientWidth ?? "0",
                vizozka.current.clientHeight ?? "0"
            ]);
        }
    }

    useEffect(() => {
        window.addEventListener("resize", handleResize);
    }, []);

    useEffect(() => {
        setImageLoaded(true);
        if (vizozka.current !== undefined) {
            setImageData([
                vizozka.current.clientWidth ?? "0",
                vizozka.current.clientHeight ?? "0"
            ]);
        }
    }, [vizozka]);

    useEffect(() => {
        if (selectedBuilding !== "") {
            getter(selectedBuilding);
        }
    }, [selectedBuilding])

    return (
        <div className={"overlay relative"}>
            <Image ref={vizozka} src={"/vizozka.webp"}
                   className={isOnHome ? "rounded-2xl" : !isMobile ? "rounded-b-[3rem]" : ""}
                   onLoad={() => setImageLoaded(true)} alt={"vizualizácia budov"}
                   width={3000} quality={10}
                   height={1033}/>
            {imageLoaded ?
                <>
                    <svg width={imageData[0]}
                         height={imageData[1]}
                         className={isOnHome ? "absolute top-[8.9vw] rounded-b-[3rem] -left-[2.1vw]" : "absolute top-[11.6vw] rounded-b-[3rem] -left-[2.8vw]"}
                         viewBox="0 0 3000 1033" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path id={"D"} onMouseEnter={() => {
                            setHovered(true)
                            setHoveredBuilding("D")
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlats[2] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                if (isOnHome) {
                                    Router.replace("/ponuka-bytov/vsetky-byty?selected=D").then()
                                } else {
                                    setSelectedBuilding("D")
                                }
                            }
                        }}
                              d="M1371.5 112C1371.33 110.5 1371.1 106.3 1371.5 101.5L1441.5 97V80.5L1510.5 77.5L1533.5 90.5L1599.5 88L1617 97.5L1654 96L1688 115.5V134L1712.5 147V162H1728.5L1735 166.5L1734 300.5L1712 301.5V340L1328.5 363.5L1295.5 338L1296.5 140.5L1316.5 139L1317.5 114.5L1371.5 112Z"
                              fill={hovered && hoveredBuilding === "D" ? numberOfFlats[2] === 0 ? "red" : "#D9D9D9" : selectedBuilding === "D" ? "#a3cb47" : "transparent"}
                              fillOpacity="0.5"/>
                        <path id={"C"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding("C")
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlats[3] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                if (isOnHome)
                                    Router.replace("/ponuka-bytov/vsetky-byty?selected=C").then()
                                else
                                    setSelectedBuilding("C")
                            }
                        }}
                              d="M1809 45.5V31L1822 30V26L1866.5 24.5L1874 30H1880V17H1883.5V31.5L1901 39.5L1956 36.5L1977 46L2005 44L2046 63.5V80.5L2070 92V265.5L1730 290.5L1680.5 253.5V83.5L1701.5 83V61L1750 59V48L1809 45.5Z"
                              fill={hovered && hoveredBuilding === "C" ? numberOfFlats[3] === 0 ? "red" : "#D9D9D9" : selectedBuilding === "C" ? "#a3cb47" : "transparent"}
                              fillOpacity="0.5"/>
                        <path id={"B"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding("B")
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlats[4] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                if (isOnHome)
                                    Router.replace("/ponuka-bytov/vsetky-byty?selected=B").then()
                                else
                                    setSelectedBuilding("B")
                            }
                        }}
                              d="M2129.5 26.5V13L2189.5 9L2221 21L2272 18.5L2296 27.5L2319 26.5L2368.5 44V46.5H2366V63L2387.5 70V85.5L2404.5 84L2411 88L2408 211H2383.5V244.5L2069.5 265L2007.5 230V64H2032.5V42.5L2076.5 40V29.5L2129.5 26.5Z"
                              fill={hovered && hoveredBuilding === "B" ? numberOfFlats[4] === 0 ? "red" : "#D9D9D9" : selectedBuilding === "B" ? "#a3cb47" : "transparent"}
                              fillOpacity="0.5"/>
                        <path id={"A"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding("A")
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlats[5] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                if (isOnHome)
                                    Router.replace("/ponuka-bytov/vsetky-byty?selected=A").then()
                                else
                                    setSelectedBuilding("A")
                            }
                        }}
                              d="M2686.5 31.5V14.5L2649 3L2598.5 6.5L2576 0L2489 3V5L2458.5 6V25.5L2434 26.5V41L2427.5 39H2412V41V177L2621 265L2752 250.5L2761 85L2730 74.5V59.5H2732V56.5L2707 48.5L2707.5 37.5L2686.5 31.5Z"
                              fill={hovered && hoveredBuilding === "A" ? numberOfFlats[5] === 0 ? "red" : "#D9D9D9" : selectedBuilding === "A" ? "#a3cb47" : "transparent"}
                              fillOpacity="0.5"/>
                        <path id={"F"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding("F")
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlats[0] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                if (isOnHome)
                                    Router.replace("/ponuka-bytov/vsetky-byty?selected=F").then()
                                else
                                    setSelectedBuilding("F")
                            }
                        }}
                              d="M246 242L251.5 207H254.5H267L270.5 179L334 176.5V163L425 160V141L508.5 138L513.5 154L601 149.5L605 160L663 157.5L673 179H680L694 215L697 231.5H715.5L723.5 381.5H703.5L706.5 425L241.5 462.5L227 272.5V261.5H248.5L246 242Z"
                              fill={hovered && hoveredBuilding === "F" ? numberOfFlats[0] === 0 ? "red" : "#D9D9D9" : selectedBuilding === "F" ? "#a3cb47" : "transparent"}
                              fillOpacity="0.5"/>
                        <path id={"E"} onMouseEnter={() => {
                            setHovered(true);
                            setHoveredBuilding("E")
                        }} onMouseLeave={() => setHovered(false)} onClick={() => {
                            if (numberOfFlats[1] === 0) {
                                toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                                    position: "bottom-center"
                                })
                            } else {
                                if (isOnHome)
                                    Router.replace("/ponuka-bytov/vsetky-byty?selected=E").then()
                                else
                                    setSelectedBuilding("E")
                            }
                        }}
                              d="M951 124V109L1027 104L1040.5 117.5L1118.5 116L1129 125.5L1174.5 123L1200 145.5V156.5L1222 177.5V194L1241 192.5L1244.5 197.5L1246.5 225.5V293H1239V301C1239 301.8 1246.33 ************ 309L1248.5 337H1225L1227 378.5L818 404L796.5 366L794.5 279L791 272L793 231L791 227.5V223.5L793 221V169H814.5V145L871 141V128.5L951 124Z"
                              fill={hovered && hoveredBuilding === "E" ? numberOfFlats[1] === 0 ? "red" : "#D9D9D9" : selectedBuilding === "E" ? "#a3cb47" : "transparent"}
                              fillOpacity="0.5"/>
                        {!isMobile && !isOnHome &&
                            <>
                                <g filter="url(#filter0_bd_1057_4048)">
                                    <path
                                        d="M80.5 671.5V527.5L2893 280L3028 287L3041 324.5L2960 336L2942.5 363L2900 396L2594 423L2515.5 382L2431 453.5V491L1138 615.5L80.5 671.5Z"
                                        fill="#D9D9D9" fillOpacity="0.01" shapeRendering="crispEdges"/>
                                </g>
                                <g filter="url(#filter1_d_1057_4048)" id={"infoF"}>
                                    <rect x="325" y="506.293" width="332" height="57" rx="28.5"
                                          transform="rotate(-4.36946 325 506.293)"
                                          stroke={hovered && hoveredBuilding === "F" ? "white" : "transparent"}
                                          strokeWidth={4}
                                          fill={hovered && hoveredBuilding === "F" ? "#B4CC7A" : "#D1D1B6"}/>
                                </g>
                                <g filter="url(#filter2_d_1057_4048)" id={"infoE"}>
                                    <rect x="881" y="457.293" width="332" height="57" rx="28.5"
                                          transform="rotate(-4.36946 881 457.293)"
                                          stroke={hovered && hoveredBuilding === "E" ? "white" : "transparent"}
                                          strokeWidth={4}
                                          fill={hovered && hoveredBuilding === "E" ? "#B4CC7A" : "#D1D1B6"}/>
                                </g>
                                <g filter="url(#filter3_d_1057_4048)" id={"infoD"}>
                                    <rect x="1366" y="416.293" width="332" height="57" rx="28.5"
                                          transform="rotate(-4.36946 1366 416.293)"
                                          stroke={hovered && hoveredBuilding === "D" ? "white" : "transparent"}
                                          strokeWidth={4}
                                          fill={hovered && hoveredBuilding === "D" ? "#B4CC7A" : "#D1D1B6"}/>
                                </g>
                                <g filter="url(#filter4_d_1057_4048)" id={"infoC"}>
                                    <rect x="1763" y="382.293" width="332" height="57" rx="28.5"
                                          transform="rotate(-4.36946 1763 382.293)"
                                          stroke={hovered && hoveredBuilding === "C" ? "white" : "transparent"}
                                          strokeWidth={4}
                                          fill={hovered && hoveredBuilding === "C" ? "#B4CC7A" : "#D1D1B6"}/>
                                </g>
                                <g filter="url(#filter5_d_1057_4048)">
                                    <rect x="2115" y="353.293" width="332" height="57" rx="28.5"
                                          transform="rotate(-4.36946 2115 353.293)"
                                          stroke={hovered && hoveredBuilding === "B" ? "white" : "transparent"}
                                          strokeWidth={4}
                                          fill={hovered && hoveredBuilding === "B" ? "#B4CC7A" : "#D1D1B6"}/>
                                </g>
                                <g filter="url(#filter6_d_1057_4048)">
                                    <rect x="2470" y="323.293" width="332" height="57" rx="28.5"
                                          transform="rotate(-4.36946 2470 323.293)"
                                          stroke={hovered && hoveredBuilding === "A" ? "white" : "transparent"}
                                          strokeWidth={4}
                                          fill={hovered && hoveredBuilding === "A" ? "#B4CC7A" : "#D1D1B6"}/>
                                </g>
                            </>}
                    </svg>
                    {!isMobile && !isOnHome && <div className={"textiky absolute bottom-0"}>
                        <p className={"left-[10.7vw] text-[1vw] flex gap-2 font-bold -rotate-[4deg] bottom-[4.5vw] absolute"}>
                            <span>{numberOfFlats[0]}</span><span>{numberOfFlats[0] >= 5 || numberOfFlats[0] === 0 ? "voľných" : numberOfFlats[0] > 1 && numberOfFlats[0] <= 4 ? "voľné" : "voľný"}</span>
                        </p>
                        <p className={"left-[28.9vw] text-[1vw] flex gap-2 font-bold -rotate-[4deg] bottom-[6.2vw] absolute"}>
                            <span>{numberOfFlats[1]}</span><span>{numberOfFlats[1] >= 5 || numberOfFlats[1] === 0 ? "voľných" : numberOfFlats[1] > 1 && numberOfFlats[1] <= 4 ? "voľné" : "voľný"}</span>
                        </p>
                        <p className={"left-[45vw] text-[1vw] flex gap-2 font-bold -rotate-[4deg] bottom-[7.6vw] absolute"}>
                            <span>{numberOfFlats[2]}</span><span>{numberOfFlats[2] >= 5 || numberOfFlats[2] === 0 ? "voľných" : numberOfFlats[2] > 1 && numberOfFlats[2] <= 4 ? "voľné" : "voľný"}</span>
                        </p>
                        <p className={"left-[58.2vw] text-[1vw] flex gap-2 font-bold -rotate-[4deg] bottom-[8.7vw] absolute"}>
                            <span>{numberOfFlats[3]}</span><span>{numberOfFlats[3] >= 5 || numberOfFlats[3] === 0 ? "voľných" : numberOfFlats[3] > 1 && numberOfFlats[3] <= 4 ? "voľné" : "voľný"}</span>
                        </p>
                        <p className={"left-[69.7vw] text-[1vw] flex gap-2 font-bold -rotate-[4deg] bottom-[9.7vw] absolute"}>
                            <span>{numberOfFlats[4]}</span><span>{numberOfFlats[4] >= 5 || numberOfFlats[4] === 0 ? "voľných" : numberOfFlats[4] > 1 && numberOfFlats[4] <= 4 ? "voľné" : "voľný"}</span>
                        </p>
                        <p className={"left-[81.4vw] text-[1vw] flex gap-2 font-bold -rotate-[4deg] bottom-[10.7vw] absolute"}>
                            <span>{numberOfFlats[5]}</span><span>{numberOfFlats[5] >= 5 || numberOfFlats[5] === 0 ? "voľných" : numberOfFlats[5] > 1 && numberOfFlats[5] <= 4 ? "voľné" : "voľný"}</span>
                        </p>
                    </div>}
                    {isOnHome || isMobile && <>
                        {numberOfFlats &&
                            <>
                                <div
                                    className={"flex gap-3 mb-2 w-full px-5 mt-3 text-[3vw] justify-center"}>
                                    {numberOfFlats.map((number: number, index: number) => (
                                        <p key={index}
                                           className={number > 0 ? "bg-[#B4CC7A] rounded-lg w-full py-1 text-center px-2" : "bg-[#7F4F2A] text-center py-1 text-white w-full rounded-lg px-4"}>{isMobile ?
                                            <span>{number}</span> :
                                            <span>{number === 1 ? number + " voľný" : number > 1 && number < 5 ? number + " voľné" : number + " voľných"}</span>}</p>
                                    ))}
                                </div>
                                <p>Počet voľných {room === "all" ? "" : room + "-izbových"} bytov</p></>
                        }
                    </>
                    }
                </> : <></>
            }
        </div>
    )
}