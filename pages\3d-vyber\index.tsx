import ModelApp from "../../components/ModelComponents/ModelApp";
import executeQuery from "../../utils/dbConnection";

export const getServerSideProps = async () => {
    try {
        let flaty = await executeQuery({
            query: `SELECT
                        budova,
                        poschodie,
                        COUNT(*) AS vsetky,
                        SUM(CASE WHEN dostupnost = 1 THEN 1 ELSE 0 END) AS dostupne
                    FROM
                        byty
                    WHERE
                        budova IN ('A', 'B', 'C', 'D', 'E', 'F')
                        AND poschodie IN (1, 2, 3, 4, 5)
                    GROUP BY
                        budova,
                        poschodie;`,
            values: []
        });

        flaty = JSON.stringify(flaty)

        return {props: {flaty}};
    } catch (error) {
        console.error("Error fetching data:", error);
        return {props: {error: "Error fetching data"}};
    }
};


export default function ThreeDPage({flaty}) {
    flaty = JSON.parse(flaty)
    return (
        <section className={"overflow-y-hidden overflow-x-hidden"}>
            <ModelApp flatsAvaibility={flaty}/>
        </section>
    )
}