import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        let {floor} = req.query;
        floor = parseInt(floor);
        floor = floor + 1;
        const {building} = req.query;
        const {rooms} = req.query;
        const rows = await executeQuery({
            query: "SELECT * FROM byty WHERE poschodie = ? AND budova = ? AND rooms = ? AND dostupnost = 1",
            values: [floor, building, rooms]
        });

        console.log(`SELECT * FROM byty WHERE poschodie ${floor} AND budova = ${building} AND canRooms = ${rooms} AND dostupnost = 1`);

        const canRooms = await executeQuery({
            query: "SELECT * FROM byty WHERE poschodie = ? AND budova = ? AND canRooms = ? AND dostupnost = 1",
            values: [floor, building, rooms]
        });

        let more = rows.concat(canRooms);

        res.status(200).json({data: more});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}