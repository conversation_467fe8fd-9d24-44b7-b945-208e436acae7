//@ts-nocheck
import { useGLTF } from "@react-three/drei";
import {Ivy} from "./Ivy";

export function BuildingAModel() {
    const { nodes, materials } = useGLTF("/A.glb");
    return (
        <group position={[35.55, 3.51, 0.25]}>
            <group scale={0.5}>
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009["geometry"]}
                    material={materials.atika}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009_1["geometry"]}
                    material={materials.DREVO}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009_2["geometry"]}
                    material={materials["Material #-2147483567.011"]}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009_3["geometry"]}
                    material={materials["Material #-2147483567.026"]}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009_4["geometry"]}
                    material={materials.Travicka}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009_5["geometry"]}
                    material={materials.BACKPLATE}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009_6["geometry"]}
                    material={materials["Drevo vpredu"]}
                />
                <mesh
                    castShadow
                    receiveShadow
                    geometry={nodes.Mesh009_7["geometry"]}
                    material={materials.PaletteMaterial001}
                />
            </group>
            <group>
                <Ivy position={[5, 1.4, -7.2]}
                     rotation={[1.571, 0.009, 3.15]}
                     scale={[2.3, 0.56, 1.7]}/>
            </group>
        </group>
    );
}

useGLTF.preload("/A.glb");