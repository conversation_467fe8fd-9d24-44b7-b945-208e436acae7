import executeQuery from "../../../utils/dbConnection";

export default async function handler(req, res) {
    const {section} = req.query;
    try {
        const rows = await executeQuery({
            query: 'SELECT * FROM parking WHERE section = ?',
            values: [section]
        })
        res.status(200).json({data: rows});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}