import dynamic from "next/dynamic";
import {Button} from "flowbite-react";

const ArticlesGrid = dynamic(() => import("../components/articlesGrid"));
const VyberBytu = dynamic(() => import("../components/home/<USER>"));
const LokalitaIndex = dynamic(() => import("../components/home/<USER>"));
const InfoGrid = dynamic(() => import("../components/home/<USER>"));
const Vyhody = dynamic(() => import("../components/home/<USER>"));
const Harmonogram = dynamic(() => import("../components/indexComponents/harmonogram"));
const Developer = dynamic(() => import("../components/indexComponents/developer"));
const Image = dynamic(() => import("next/image"));
const Link = dynamic(() => import("next/link"));

export default function App({isMobile}) {
    return <>
        <div className="App relative">
            {isMobile ? <div className="bg-[url('/heroIndex.webp')] h-[70vh] bg-cover mt-10 lg:mt-0">
                <div className="relative text-center z-20 px-0 top-10">
                    <h1 className={"text-[5vw] mb-3"}>Výnimočne <strong className={"text-white"}>dostupné
                        bývanie</strong></h1>
                    <div className={"flex justify-evenly"}>
                        <div
                            className={"flex p-1 items-start flex-col justify-left"}>
                            <p className={"text-[3w] font-light"}>Rezidenčný komplex</p>
                            <div className={"flex gap-2 items-center"}>
                                <h2 className={"text-[5vw] font-bold"}>6 budov</h2>
                                <span>|</span>
                                <h2 className={"text-[5vw] font-bold"}>97 bytov</h2>
                            </div>
                        </div>
                        <div className={"p-1 flex flex-col items-end"}>
                            <p className={"text-[3w] font-light"}>Podunajské Biskupice</p>
                            <h2 className={"text-[5vw] font-bold"}>Špaldová ulica</h2>
                        </div>
                    </div>
                </div>
            </div> : <div className="heroImage relative mt-20 lg:mt-0">
                <Image src={"/heroIndex.jpg"} alt="vizualizácia projektu Laurin Dvor"
                       className={"lg:-mt-[5vw] xl:-mt-[11vw]"}
                       width={2560} quality={10} priority
                       height={1440}/>
                {!isMobile &&
                    <Image src="/heroFrame.svg" className="absolute -bottom-10 Framik z-0" width={3840} quality={5}
                           height={1080}
                           alt="frame"/>}
                <div className="relative h-40 -mt-64">
                    <div className="w-full z-10">
                        <Link href={"/ponuka-bytov/1-izbove"}>
                            <button className="kokotskyButton absolute z-40 buttonPad flex flex-col"
                                    style={{right: "52vw", bottom: "2.5vw"}}>
                                <p className={"text-[1.5vw]"}>1-izbové</p>
                                <small>byty</small></button>
                        </Link>
                        <Link href={"/ponuka-bytov/2-izbove"}>
                            <button className="kokotskyButton absolute z-40 buttonPad flex flex-col"
                                    style={{right: "42vw", bottom: "3.5vw"}}><p className={"text-[1.5vw]"}>2-izbové</p>
                                <small>byty</small></button>
                        </Link>
                        <Link href={"/ponuka-bytov/3-izbove"}>
                            <button className="kokotskyButton absolute z-40 buttonPad flex flex-col"
                                    style={{right: "32vw", bottom: "5vw"}}><p className={"text-[1.5vw]"}>3-izbové</p>
                                <small>byty</small>
                            </button>
                        </Link>
                        <Link href={"/ponuka-bytov/4-izbove"}>
                            <button className="kokotskyButton absolute z-40 buttonPad flex flex-col"
                                    style={{right: "22.5vw", bottom: "7vw"}}><p className={"text-[1.5vw]"}>4-izbové</p>
                                <small>byty</small>
                            </button>
                        </Link>
                        <Link href={"/ponuka-bytov/5-izbove"}>
                            <button className="kokotskyButton absolute z-40 buttonPad flex flex-col"
                                    style={{right: "13vw", bottom: "9.5vw"}}><p className={"text-[1.5vw]"}>5-izbové</p>
                                <small>byty</small>
                            </button>
                        </Link>
                        <Link href={"/parkovacie-miesta"}>
                            <button className="kokotskyButton absolute z-40 buttonPad flex flex-col"
                                    style={{right: "2vw", bottom: "12vw"}}><p className={"text-[1.5vw]"}>parkovacie<br/>miesta
                            </p>
                            </button>
                        </Link>
                    </div>
                </div>
            </div>}
            <VyberBytu isMobile={isMobile}/>
            <LokalitaIndex isMobile={isMobile}/>
            <InfoGrid/>
            <Vyhody isMobile={isMobile}/>
            <div className={"vyhody"}>
                <h2 className={"text-3xl lg:text-6xl mt-20 lg:mt-0 lg:font-medium px-16 lg:px-0 lg:mb-20 font-normal text-center"}>Harmonogram</h2>
            </div>
            <Harmonogram isMobile={isMobile}/>
            <div className={"developer"}>
                <h2 className={"text-[10vw] lg:text-6xl -mt-20 lg:mt-0 mb-10 lg:font-medium px-16 lg:px-0 lg:mb-20 font-normal text-center"}>Developer</h2>
            </div>
            <Developer/>
            <div className={"mb-20"}>
                <h2 className={"text-[10vw] lg:text-6xl -mt-20 lg:mt-0 lg:font-medium px-16 lg:px-0 font-normal text-center"}>Čo
                    nové na dvore?</h2>
                <ArticlesGrid/>
                <div className={"w-full md:-mt-20 flex justify-center"}>
                    <Link href={"/aktuality"}>
                        <Button color="dark" className={"uppercase"}>Pozrieť všetky aktuality</Button>
                    </Link>
                </div>
            </div>
        </div>
    </>
}
