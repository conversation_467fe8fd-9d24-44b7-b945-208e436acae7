export default function Harmonogram({isMobile}) {
    return <>
        {isMobile ? <div className={"relative flex pb-24 justify-center py-14 overflow-x-hidden"}>
                <div
                    className={"h-[43rem] absolute z-30 rounded-md overflow-hidden"}>
                    <img src={"/harm_mobil_far.png"} alt="harm_mobil_far"/>
                </div>
               <img src={"/sipky_harmonogram.png"} className={"rotate-90 z-30 absolute top-[33.7rem]"}/>
                <div
                    className={"h-full z-20 rounded-md"}>
                    <img src={"/harm_mobil_sedy.png"} alt="harm_mobil_far" className={"mb-40 rounded-lg"}/>
                </div>
            </div> :
            <div className={"relative px-14 xl:px-36 -mt-36 mb-10 overflow-hidden"}>
                <div className={"loadingbar w-full py-10 h-72 mb-40"}>
                    <div className={"w-[33vw] mt-14 absolute flex items-center z-20 h-[40vh] overflow-hidden"}>
                        <div
                            className={"h-[40vh] w-[85vw] absolute mt-[1.95rem] bg-[url('/harm_desktop_biely.svg')] bg-contain bg-no-repeat"}>

                        </div>
                        <img src={"/sipky_harmonogram.png"} loading={"lazy"} alt={"sipky"}
                             className={"h-[17vw] absolute -right-1 -top-[0]"}/>
                    </div>
                    <div className={"w-full absolute top-28 h-[40vh] overflow-hidden"}>
                        <div
                            className={"h-[40vh] w-[85vw] bg-[url('/harm_desktop.svg')] bg-contain bg-no-repeat"}>
                        </div>
                    </div>
                </div>
            </div>
        }
    </>
}
