import Image from "next/image";
import {useEffect, useState} from "react";
import Router, {useRouter} from "next/router";
import toast from "react-hot-toast";

export default function FloorChoose({flatSelected, getter, numberOfFlatsFloor, isMobile, isFlatDetail, room}) {
    const [selectedFloor, setSelectedFloor] = useState(null)
    const [hoveredFloor, setHoveredFloor] = useState(null)
    const [imageLoaded, setImageLoaded] = useState<boolean>(false)
    const router = useRouter()

    useEffect(() => {
        const query = router.asPath.split('?')[1];
        let values;
        let keyValuePairs;
        if (query != undefined) {
            keyValuePairs = query.split('&');
            values = keyValuePairs.map(pair => pair.split('=')[1]);
            if (router.asPath.includes("floor")) {
                setSelectedFloor(parseInt(values[1]))
            } else {
                setSelectedFloor(null)
            }
        }
    }, [router.asPath])

    useEffect(() => {
        if (selectedFloor !== null) {
            if (!isFlatDetail) {
                getter(selectedFloor);
            }
            if (router.asPath.match(/byt=(\d+)/) !== null) {
                router.back();
            }
        }
    }, [selectedFloor])

    if (flatSelected === "") {
        return null
    }

    const getXAxis = (value: number): number => {
        if (imageLoaded) {
            return value / 223 * document.getElementById("flooringsChoose").clientWidth
        }
        return value;
    }
    const getYAxis = (value: number): number => {
        if (imageLoaded) {
            return value / 183 * document.getElementById("flooringsChoose").clientHeight
        }
        return value;
    }

    return (
        <div className={"flex relative lg:px-5 justify-center -ml-5 lg:ml-0 items-start"}>
            {isFlatDetail ? <Image id={"flooringsChoose"} className={""}
                                   src={hoveredFloor !== null ? "/floors/np0" + hoveredFloor + ".svg" : flatSelected ? "/floors/np0" + (flatSelected["poschodie"]) + ".svg" : "/floors/np00.svg"}
                                   alt={"bokorys budovy"} useMap={"#floorings"} onLoad={() => setImageLoaded(true)}
                                   width={500} height={200}/> :
                <Image id={"flooringsChoose"} className={isMobile && "scale-125 mt-10 -ml-20"}
                       src={hoveredFloor !== null ? "/floors/np0" + hoveredFloor + ".svg" : selectedFloor ? "/floors/np0" + selectedFloor + ".svg" : "/floors/np00.svg"}
                       alt={"bokorys budovy"} useMap={"#floorings"}
                       onLoad={() => setImageLoaded(true)}
                       width={500} height={200}/>
            }

            <map name="floorings">
                <area style={{cursor: "pointer"}} title="1" onMouseEnter={() => setHoveredFloor(1)}
                      onMouseLeave={() => setHoveredFloor(null)} onClick={() => {
                    if (numberOfFlatsFloor[0] === 0) {
                        toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                            position: "bottom-center"
                        })
                    } else {
                        if (isFlatDetail) {
                            Router.replace("/ponuka-bytov/" + room + "-izbove?selected=" + flatSelected["budova"] + "&floor=" + 1).then()
                        } else {
                            setSelectedFloor(1)
                        }
                    }
                }}
                      coords={getXAxis(68) + "," + getYAxis(112) + "," + getXAxis(161) + "," + getYAxis(133)}
                      shape="rect" alt={""}/>
                <area target="" style={{cursor: "pointer"}} title="2" onMouseEnter={() => setHoveredFloor(2)}
                      onMouseLeave={() => setHoveredFloor(null)} onClick={() => {
                    if (numberOfFlatsFloor[1] === 0) {
                        toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                            position: "bottom-center"
                        })
                    } else {
                        if (isFlatDetail) {
                            Router.replace("/ponuka-bytov/" + room + "-izbove?selected=" + flatSelected["budova"] + "&floor=" + 2).then()
                        } else {
                            setSelectedFloor(2)
                        }
                    }
                }}
                      coords={getXAxis(68) + "," + getYAxis(89) + "," + getXAxis(161) + "," + getYAxis(109)}
                      shape="rect" alt={"2"}/>
                <area target="" alt="" style={{cursor: "pointer"}} title="3" onMouseEnter={() => setHoveredFloor(3)}
                      onMouseLeave={() => setHoveredFloor(null)} onClick={() => {
                    if (numberOfFlatsFloor[2] === 0) {
                        toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                            position: "bottom-center"
                        })
                    } else {
                        if (isFlatDetail) {
                            Router.replace("/ponuka-bytov/" + room + "-izbove?selected=" + flatSelected["budova"] + "&floor=" + 3).then()
                        } else {
                            setSelectedFloor(3)
                        }
                    }
                }}
                      coords={getXAxis(68) + "," + getYAxis(67) + "," + getXAxis(161) + "," + getYAxis(87)}
                      shape="rect"/>
                <area target="" alt="" style={{cursor: "pointer"}} title="4" onMouseEnter={() => setHoveredFloor(4)}
                      onMouseLeave={() => setHoveredFloor(null)} onClick={() => {
                    if (numberOfFlatsFloor[3] === 0) {
                        toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                            position: "bottom-center"
                        })
                    } else {
                        if (isFlatDetail) {
                            Router.replace("/ponuka-bytov/" + room + "-izbove?selected=" + flatSelected["budova"] + "&floor=" + 4).then()
                        } else {
                            setSelectedFloor(4)
                        }
                    }
                }}
                      coords={getXAxis(68) + "," + getYAxis(44) + "," + getXAxis(161) + "," + getYAxis(65)}
                      shape="rect"/>
                <area target="" alt="" style={{cursor: "pointer"}} title="5" onMouseEnter={() => setHoveredFloor(5)}
                      onMouseLeave={() => setHoveredFloor(null)} onClick={() => {
                    if (numberOfFlatsFloor[4] === 0) {
                        toast.error("Vo Vami zvolenej budove neexistuje žiaden byt podľa zvolených kritérií!", {
                            position: "bottom-center"
                        })
                    } else {
                        if (isFlatDetail) {
                            Router.replace("/ponuka-bytov/" + room + "-izbove?selected=" + flatSelected["budova"] + "&floor=" + 5).then()
                        } else {
                            setSelectedFloor(5)
                        }
                    }
                }}
                      coords={getXAxis(68) + "," + getYAxis(11) + "," + getXAxis(161) + "," + getYAxis(42)}
                      shape="rect"/>
            </map>
            {numberOfFlatsFloor !== undefined &&
                <div
                    className={"flex absolute lg:relative right-0 lg:right-6 flex-col-reverse gap-3.5 lg:gap-5 lg:mt-12 mt-9"}>
                    {numberOfFlatsFloor.map((number: number, index: number) => (
                        <p key={index}
                           className={number > 0 ? "bg-[#B4CC7A] rounded-lg py-1 text-center px-2" : "bg-[#7F4F2A] text-center py-1 text-white mb-0.5 rounded-lg px-1 lg:px-4"}>{number === 1 ? number + " voľný" : number > 1 && number < 5 ? number + " voľné" : number + " voľných"}</p>
                    ))}
                </div>
            }
        </div>
    )
}