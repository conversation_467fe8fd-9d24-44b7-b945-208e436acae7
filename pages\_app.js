import '../styles/globals.css'
import AppNavigation from "../components/AppNavigation";
import Head from "next/head";
import ContactBar from "../components/contactBar";
import {useEffect, useState} from "react";
import LegalFooter from "../components/legalFooter";
import GTM from "../components/GTM";
import {Inter} from 'next/font/google'

const inter = Inter({subsets: ['latin']})


function MyApp({Component, pageProps}) {
    const [isMobile, setIsMobile] = useState(false);

    useEffect(() => {
        setIsMobile(window.matchMedia("(max-width: 700px)").matches)
    }, [isMobile])

    return (<>
        <Head>
            <title>Laurin dvor | Výnimočne dostupné bývanie</title>
            <meta name='description'
                  content='97 bytov | 6 obytných budov | komunitný bazén | Výnimočne dostupné bývanie v 1- až 5-izbových bytoch na začiatku Podunajských Biskupíc.'/>
            <meta property="og:description"
                  content="97 bytov | 6 obytných budov | komunitný bazén | Výnimočne dostupné bývanie v 1- až 5-izbových bytoch na začiatku Podunajských Biskupíc."/>
            <meta property="og:title" content="Laurin Dvor | Výnimočne dostupné bývanie"/>
            <meta name="twitter:title" content="Laurin Dvor | Výnimočne dostupné bývanie"/>
            <meta name="twitter:card" content="summary_large_image"/>
            <meta property="og:type" content="website"/>
            <meta property="og:image" content="https://www.laurindvor.com/Laurin_Dvor.jpg"/>
            <meta name="twitter:image" content="https://www.laurindvor.com/Laurin_Dvor.jpg"/>
            <meta name="twitter:description"
                  content="97 bytov | 6 obytných budov | komunitný bazén | Výnimočne dostupné bývanie v 1- až 5-izbových bytoch na začiatku Podunajských Biskupíc."/>
            <meta name="keywords" content="dvor, laurin dvor, podunajské biskupice, špaldová ulica, mladé rodiny,
                nehnuteľnosť, reality, reality bratislava, nehnuteľnosť bratislava, byty bratislava, predaj bytov
                bratislava, predaj bytu bratislava, predaj bytov, 1-izbové byty, 2-izbové byty, 3-izbové byty, 4-izbové
                byty, 5-izbové byty, jednoizbový, dvojizbový, trojizbový, štvorizbový, päťizbový, bazén, vlastný bazén
                pred domom, komunitný bazén, detský bazén, detské ihrisko, podlahové kúrenie, vlastné parkovacie miesto,
                parkovanie, nabíjanie elektroauta, obytná budova, cyklotrasa, hrádza, malý dunaj, dostupné bývanie,
                cenovo dostupné bývanie, novostavba, predaj nehnuteľností, predaj byty, developerské projekty
                bratislava, predaj nehnuteľností bratislava, sympatia, leila, bývanie bratislava, projekty bratislava,
                investovanie, investičný developer, rezidenčné projekty"/>
            <link rel="apple-touch-icon" type="image/png" sizes="180x180" href="/favicon_180.png"/>
            <link rel="icon" type="image/png" sizes="16x16" href="/favicon_16.png"/>
            <link rel="icon" type="image/png" sizes="32x32" href="/favicon_32.png"/>
            <link rel="icon" type="image/png" sizes="180x180" href="/favicon_180.png"/>
            <link rel="icon" type="image/png" sizes="192x192" href="/favicon_192.png"/>
            <link rel="icon" type="image/png" sizes="512x512" href="/favicon_512.png"/>
            <style jsx global>{`
                html {
                    font-family: ${inter.style.fontFamily};
                }
            `}</style>
        </Head>
        <GTM gtmId="GTM-NGP75MND"/>
        <AppNavigation isMobile={isMobile}/>
        <Component {...pageProps} isMobile={isMobile}/>
        <LegalFooter isMobile={isMobile}/>
        <ContactBar isMobile={isMobile}/>
    </>)
}

export default MyApp