//@ts-nocheck
import {<PERSON><PERSON>, <PERSON><PERSON>, Label, RangeSlider, Tooltip} from "flowbite-react";
import {useEffect, useState} from "react";
import buildingConfigurations from "../../utils/buildingConfig";
import Image from "next/image";

interface ControlProps {
    active: boolean,
    time: number,
    shadowsEnabled: boolean,
    cameraRef: {
        current: {
            reset: (arg0: boolean) => void;
            setLookAt: (arg0: number, arg1: number, arg2: number, arg3: number, arg4: number, arg5: number, arg6: boolean) => void;
            camera: { zoom: number; };
            dolly: (arg0: number, arg1: boolean) => void;
        };
    };
    stateChanger: (arg0: string, valueToSet?: number | Array<number> | boolean) => void;
    summerTime: boolean;
    isMobile: boolean;
    spaceEnabled: boolean;
}

/**
 * Component that handling all user input controls of a 3D model.
 * @param props
 * @returns {JSX.Element}
 * @constructor
 */
export default function ModelControls(props: ControlProps): JSX.Element {
    const [time, setTime] = useState(props.time)
    const winterSunCalculation = [550 * (Math.cos((28.24 + (time - 7) * 15) * Math.PI / 180)), -(550 * 0.42) + 550 * Math.sin((28.24 + (time - 7) * 15) * Math.PI / 180), 550 * Math.sin((28.24 + (time - 7) * 15) * Math.PI / 180)];
    const summerSunCalculation = [550 * (Math.cos((-35.74 + (time - 5) * 15) * Math.PI / 180)), (550 * 0.5) + 550 * Math.sin((-35.74 + (time - 5) * 15) * Math.PI / 180), 550 * Math.sin((-35.74 + (time - 5) * 15) * Math.PI / 180)];
    const [hovered, setHovered] = useState(false)

    useEffect(() => {
        setSunToPosition()
    }, [props.summerTime])

    const zoomCameraToObject = (coordinates: number[]) => {
        props.cameraRef.current?.setLookAt(coordinates[0], coordinates[1], coordinates[2], coordinates[3], coordinates[4], coordinates[5], true);
    }

    const resetCamera = () => {
        if (props.isMobile) {
            props.cameraRef.current?.setLookAt(-240, 10, 150, 0, 5, 0, true);
        } else {
            props.cameraRef.current?.reset(true);
        }
        props.stateChanger("cameraReset")
    }

    const setSunToPosition = () => {
        props.stateChanger("setSunPosition", props.summerTime ? summerSunCalculation : winterSunCalculation);
        props.stateChanger("setSunStrength", time <= (props.summerTime ? 5 : 7) || time >= (props.summerTime ? 21.5 : 15.6) ? 0 : 1.2 * time)
    }

    return (
        <>
            {props.isMobile ?
                <>
                    <div
                        className="fixed top-[10.5rem] z-20 flex w-full text-center justify-center items-center px-3.5">
                        <Badge color="warning" className={"shadow-md"} size="sm">
                            <small>Pre najlepší zážitok si túto stránku otvorte na svojom PC</small>
                        </Badge>
                    </div>
                    <div className="fixed pb-2 top-14 pt-4 z-20 w-full bg-white shadow-md">
                        <div className="buttons flex flex-nowrap w-100 justify-evenly items-center px-6">
                            <button type="button"
                                    onClick={() => zoomCameraToObject(buildingConfigurations[0].buildingZoomMobile)}
                                    className="text-white bg-gray-500 hover:bg-gray-900 focus:outline-none focus:ring-4 mt-2 focus:ring-gray-300 font-bold rounded-lg text-sm px-3.5 py-3 shadow-md me-2  dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">
                                <strong>A</strong>
                            </button>
                            <button type="button"
                                    onClick={() => zoomCameraToObject(buildingConfigurations[1].buildingZoomMobile)}
                                    className="text-white bg-gray-500 hover:bg-gray-900 focus:outline-none focus:ring-4 mt-2 focus:ring-gray-300 font-bold rounded-lg text-sm px-3.5 py-3 shadow-md me-2  dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">
                                <strong>B</strong>
                            </button>
                            <button type="button"
                                    onClick={() => zoomCameraToObject(buildingConfigurations[2].buildingZoomMobile)}
                                    className="text-white bg-gray-500 hover:bg-gray-900 focus:outline-none focus:ring-4 mt-2 focus:ring-gray-300 font-bold rounded-lg text-sm px-3.5 py-3 shadow-md me-2  dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">
                                <strong>C</strong>
                            </button>
                            <button type="button" onClick={resetCamera}
                                    className="text-white mt-2 bg-gray-900 hover:bg-gray-900 focus:outline-none focus:ring-4 focus:ring-gray-300 font-bold rounded-full text-sm p-4 me-2  dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">
                                <svg className="w-8 h-8 text-white dark:text-white" aria-hidden="true"
                                     xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 20">
                                    <path stroke="currentColor"
                                          d="M16 1v5h-5M2 19v-5h5m10-4a8 8 0 0 1-14.947 3.97M1 10a8 8 0 0 1 14.947-3.97"/>
                                </svg>
                            </button>
                            <button type="button"
                                    onClick={() => zoomCameraToObject(buildingConfigurations[3].buildingZoomMobile)}
                                    className="text-white bg-gray-500 hover:bg-gray-900 focus:outline-none focus:ring-4 mt-2 focus:ring-gray-300 font-bold rounded-lg text-sm px-3.5 py-3 shadow-md me-2  dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">
                                <strong>D</strong>
                            </button>
                            <button type="button"
                                    onClick={() => zoomCameraToObject(buildingConfigurations[4].buildingZoomMobile)}
                                    className="text-white bg-gray-500 hover:bg-gray-900 focus:outline-none focus:ring-4 mt-2 focus:ring-gray-300 font-bold rounded-lg text-sm px-3.5 py-3 shadow-md me-2  dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">
                                <strong>E</strong>
                            </button>
                            <button type="button"
                                    onClick={() => zoomCameraToObject(buildingConfigurations[5].buildingZoomMobile)}
                                    className="text-white bg-gray-500 hover:bg-gray-900 focus:outline-none focus:ring-4 mt-2 focus:ring-gray-300 font-bold rounded-lg text-sm px-3.5 py-3 shadow-md me-2  dark:bg-gray-800 dark:hover:bg-gray-700 dark:focus:ring-gray-700 dark:border-gray-700">
                                <strong>F</strong>
                            </button>
                        </div>
                    </div>
                </>
                :
                <div className={"mt-20"}>
                    <button className="myBtn top-[1rem] z-10 shadow-lg right-[0.1vw] flex gap-2" type="button"
                            onClick={resetCamera}>
                        Resetovať kameru <svg className="w-6 h-6 text-white" aria-hidden="true"
                                              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                              d="M17.7 7.7A7.1 7.1 0 0 0 5 10.8M18 4v4h-4m-7.7 8.3A7.1 7.1 0 0 0 19 13.2M6 20v-4h4"/>
                    </svg>
                    </button>
                    <button
                        className="text-white top-7 absolute left-5 z-20 border border-black p-1 rounded-md"
                        color="gray"
                        type="button"
                        onClick={() => props.cameraRef.current?.dolly(props.cameraRef.current?.camera.zoom * 35, true)}>
                        <svg className="w-4 h-4 text-gray-800 dark:text-white" aria-hidden="true"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 18">
                            <path stroke="currentColor" d="M9 1v16M1 9h16"/>
                        </svg>
                    </button>
                    <button
                        className="text-white top-16 absolute left-5 z-20 border border-black p-1 rounded-md"
                        color="gray"
                        type="button"
                        onClick={() => props.cameraRef.current?.dolly(-props.cameraRef.current?.camera.zoom * 35, true)}>
                        <svg className="w-4 h-4 text-gray-800 dark:text-white" aria-hidden="true"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 18 2">
                            <path stroke="currentColor"
                                  d="M1 1h16"/>
                        </svg>
                    </button>
                    <div className={"modelControls absolute flex gap-1 top-[4rem] right-0 z-20 m-4"}>
                        {buildingConfigurations.map((config) => <Button
                            key={config.letter}
                            onClick={() => zoomCameraToObject(config.buildingZoom)}
                            size="xs"
                            color="dark"
                            pill>
                            {config.letter}
                        </Button>)}
                    </div>
                    <div
                        className={hovered ? "absolute z-20 top-[2rem] right-[22rem] max-w-2xl opacity-100" : props.spaceEnabled ? "hidden" : "absolute z-20 opacity-50 top-[2rem] right-[22rem] max-w-2xl"}
                        onMouseEnter={() => setHovered(true)}
                        onMouseLeave={() => setHovered(false)}>
                        <div className={"flex gap-4"}>
                            <div className="mb-1 block">
                                <Label htmlFor="md-range"><strong>Ovládanie času</strong></Label>
                            </div>
                            <RangeSlider min={props.summerTime ? 4.5 : 6.5} step={0.1} value={time}
                                         max={props.summerTime ? 22.8 : 16.5} onChange={(e) => {
                                setTime(parseFloat(e.target.value));
                                props.stateChanger("setTime", parseFloat(e.target.value))
                                setSunToPosition()
                            }} id="md-range" className="mb-2" sizing="md"/>
                            <strong>{parseInt(String(time))}:00</strong>
                        </div>
                        <div className={hovered ? "opacity-100 transition-all mt-2" : "opacity-0 transition-all mt-2"}>
                            <label className="relative inline-flex items-center mb-2 cursor-pointer">
                                <input type="checkbox" onChange={(e) => {
                                    props.stateChanger("toggleSummerTime", e.target.checked)
                                    setSunToPosition()
                                }} className="sr-only peer"/>
                                <div
                                    className="w-11 h-4 border border-gray-600 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[3px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:w-5 after:h-3.5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                                <span
                                    className="ms-3 text-sm font-medium text-gray-900 dark:text-gray-300">Letný čas</span>
                            </label>
                        </div>
                    </div>
                    <div className={"absolute flex right-[13rem] flex-col gap-1 top-[1rem] z-20 m-4"}>
                        <Tooltip placement={"bottom"}
                                 content={props.shadowsEnabled ? "Vypnúť tiene (môže zlepšiť výkon)" : "Zapnúť tiene (môže zhoršiť výkon)"}
                                 className={"w-32"}>
                            <Button onClick={() => props.stateChanger("toggleShadows")} size="xs" color="dark">
                                {props.shadowsEnabled ?
                                    <svg className="w-6 h-6 text-white" aria-hidden="true"
                                         xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                        <path fillRule="evenodd"
                                              d="M13 3a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0V3ZM6.3 5A1 1 0 0 0 5 6.2l1.4 1.5a1 1 0 0 0 1.5-1.5L6.3 5Zm12.8 1.3A1 1 0 0 0 17.7 5l-1.5 1.4a1 1 0 0 0 1.5 1.5L19 6.3ZM12 7a5 5 0 1 0 0 10 5 5 0 0 0 0-10Zm-9 4a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2H3Zm16 0a1 1 0 1 0 0 2h2a1 1 0 1 0 0-2h-2ZM7.8 17.7a1 1 0 1 0-1.5-1.5L5 17.7A1 1 0 1 0 6.3 19l1.5-1.4Zm9.9-1.5a1 1 0 0 0-1.5 1.5l1.5 1.4a1 1 0 0 0 1.4-1.4l-1.4-1.5ZM13 19a1 1 0 1 0-2 0v2a1 1 0 1 0 2 0v-2Z"
                                              clipRule="evenodd"/>
                                    </svg>
                                    : <svg className="w-6 h-6 text-white" aria-hidden="true"
                                           xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                              strokeWidth="2"
                                              d="M12 5V3m0 18v-2M7 7 5.7 5.7m12.8 12.8L17 17M5 12H3m18 0h-2M7 17l-1.4 1.4M18.4 5.6 17 7.1M16 12a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z"/>
                                    </svg>}
                            </Button>
                        </Tooltip>
                    </div>
                    <div className={"absolute flex right-[16.5rem] flex-col gap-1 top-[1rem] z-20 m-4"}>
                        <Tooltip placement={"bottom"}
                                 content={props.spaceEnabled ? "Vypnúť okolie (môže zlepšiť výkon)" : "Zapnúť okolie (môže zhoršiť výkon)"}
                                 className={"w-32"}>
                            <Button onClick={() => props.stateChanger("toggleSpace")} size="xs" color="dark">
                                {props.spaceEnabled ?
                                    <svg className="w-6 h-6 text-white" aria-hidden="true"
                                         xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                                        <path fillRule="evenodd"
                                              d="M4 4c0-.6.4-1 1-1h14a1 1 0 1 1 0 2v14a1 1 0 1 1 0 2H5a1 1 0 1 1 0-2V5a1 1 0 0 1-1-1Zm5 2a1 1 0 0 0-1 1v1c0 .6.4 1 1 1h1c.6 0 1-.4 1-1V7c0-.6-.4-1-1-1H9Zm5 0a1 1 0 0 0-1 1v1c0 .6.4 1 1 1h1c.6 0 1-.4 1-1V7c0-.6-.4-1-1-1h-1Zm-5 4a1 1 0 0 0-1 1v1c0 .6.4 1 1 1h1c.6 0 1-.4 1-1v-1c0-.6-.4-1-1-1H9Zm5 0a1 1 0 0 0-1 1v1c0 .6.4 1 1 1h1c.6 0 1-.4 1-1v-1c0-.6-.4-1-1-1h-1Zm-3 4a2 2 0 0 0-2 2v3h2v-3h2v3h2v-3a2 2 0 0 0-2-2h-2Z"
                                              clipRule="evenodd"/>
                                    </svg>

                                    : <svg className="w-6 h-6 text-white" aria-hidden="true"
                                           xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                              strokeWidth="2"
                                              d="M6 4h12M6 4v16M6 4H5m13 0v16m0-16h1m-1 16H6m12 0h1M6 20H5M9 7h1v1H9V7Zm5 0h1v1h-1V7Zm-5 4h1v1H9v-1Zm5 0h1v1h-1v-1Zm-3 4h2a1 1 0 0 1 1 1v4h-4v-4a1 1 0 0 1 1-1Z"/>
                                    </svg>}
                            </Button>
                        </Tooltip>
                    </div>
                    <div
                        className="flex opacity-50 hover:opacity-100 transition gap-2 text-center absolute z-20 top-5 left-16">
                        <div
                            className="flex controlsHints max-w-20 rounded-md backdrop-blur-sm flex-col items-center justify-center">
                            <Image src={"/hand.png"} alt="drag hand" width={25} height={25}/>
                            <small className="text-xs">Otáčajte myšou</small>
                        </div>
                        <div
                            className="flex controlsHints max-w-20 rounded-md p-1 backdrop-blur-sm flex-col items-center justify-center">
                            <Image src={"/scroll.png"} alt="drag hand" width={25} height={25}/>
                            <small className="text-xs">Priblíženie kolieskom myši</small>
                        </div>
                    </div>
                </div>
            }
        </>
    )
}
