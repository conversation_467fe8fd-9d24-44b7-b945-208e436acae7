//@ts-nocheck
import {useEffect, useRef, useState} from "react";
import {useFrame} from "@react-three/fiber";
import {Html, useCursor} from "@react-three/drei";
import {Modal} from "flowbite-react";
import FlatPick from "./FlatPick";
import {FloorProps} from "../../../utils/interfaces/FloorProps";
import dynamic from "next/dynamic";

const FloorInfoPopup = dynamic(() => import('./FloorInfoPopup'));
const FloorInfoMobile = dynamic(() => import('./FloorInfoMobile'));

/**
 * Component for interactive clicking on floor.
 * @param props
 * @constructor
 */
export default function Floor(props: FloorProps) {
    const ref = useRef()
    const [hovered, setHovered] = useState(false)
    const [clicked, setClicked] = useState(false)
    const [openModal, setOpenModal] = useState(false);
    const [flatData, setFlatData] = useState({allFlats: 0, freeFlats: 0});
    const [popupMessage, setPopupMessage] = useState("0/0")

    // @ts-ignore
    useFrame((state) => ref.current.scale.setScalar(hovered ? 1 + Math.sin(state.clock.elapsedTime * 5) / 80 : 1))
    useCursor(hovered)

    const zoomCameraToObject = () => {
        props.cameraRef.current?.setLookAt(props.position[0], props.position[1] - 10, props.position[2], props.position[0], props.position[1] - 10, props.position[2], true);
    }

    const mobileModalOpener = () => {
        setOpenModal(true)
    }

    const handleClick = (e: any) => {
        if (props.isMobile) {
            setClicked(true)
        } else {
            if (props.zoomed) {
                e.stopPropagation();
                zoomCameraToObject()
                setTimeout(() => {
                    setOpenModal(true)
                    props.stateChanger('openFlatModal')
                }, 1000)
            }
        }
    }

    useEffect(() => {
        if (hovered) {
            const findObject = (data, budova, poschodie) => {
                return data.find(item => item.budova === budova && item.poschodie === poschodie);
            };
            const result = findObject(props.flatsAvaibility, props.letter, props.floor + 1);
            setFlatData({allFlats: result.vsetky, freeFlats: result.dostupne})
            setPopupMessage(`${result.dostupne}/${result.vsetky}`)
        }
    }, [hovered]);

    useEffect(() => {
        setClicked(false)
    }, [props.active])

    return (
        <>
            <mesh position={props.position} ref={ref} rotation={props.rotation} onPointerOver={(e) => {
                e.stopPropagation();
                setHovered(true);
            }} onClick={(e) => handleClick(e)} onPointerMissed={() => setClicked(false)}
                  onPointerOut={() => setHovered(false)}>
                <boxGeometry args={props.geometry}/>
                <meshStandardMaterial transparent={true} opacity={hovered || clicked ? 0.8 : 0.01}
                                      color={flatData["freeFlats"] === 0 && hovered || flatData["freeFlats"] === 0 && clicked ? '#FF0000' : flatData["freeFlats"] === 1 && flatData["allFlats"] > 2 && hovered || flatData["freeFlats"] === 1 && flatData["allFlats"] > 2 && clicked ? '#FFAB48' : "#B4CC7A"}/>
                {hovered && <FloorInfoPopup popupMessage={popupMessage} floor={props.floor + 1} letter={props.letter}/>}
                {clicked && props.isMobile &&
                    <FloorInfoMobile modalOpener={mobileModalOpener} popupMessage={popupMessage} clicked={clicked}
                                     floor={props.floor + 1}
                                     letter={props.letter}/>}
                {openModal && <Html prepend={true}><Modal className="FloorModal relative items-start mt-20 w-full"
                                                          show={openModal}
                                                          onClose={() => {
                                                              setOpenModal(false)
                                                              props.cameraRef.current?.setLookAt(-150, 40, 150, 0, 5, 0, true);
                                                              props.unZoom()
                                                          }}><Modal.Header
                    className={props.isMobile ? "fixed top-20 w-full -ml-3 z-50 shadow-lg bg-white" : "bg-white rounded-2xl"}>{props.floor ? props.floor + '. poschodie' : "Prízemie"}</Modal.Header>
                    <Modal.Body className="FloorModalBody relative pb-20 pt-20 mb-20 overflow-visible">
                        <FlatPick floor={props.floor + 1} isMobile={props.isMobile} isFlatDetail={false} is3D={true}
                                  flatSelectedInPick={[]} building={props.letter}/>
                    </Modal.Body>
                </Modal></Html>}
            </mesh>
        </>
    )
}