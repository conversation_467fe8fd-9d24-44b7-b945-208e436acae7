import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {floor} = req.query;
        const {building} = req.query;
        const {rooms} = req.query;
        const {stav} = req.query;
        let {canrooms} = req.query;

        if (canrooms === "POČET IZIEB") {
            canrooms = "";
        } else {
            canrooms = ` AND canRooms = ${canrooms}`;
        }


        let query;
        let query2;
        let count;
        if (floor === "" && building === "" && rooms === "" && stav === "") {
            query = "SELECT * FROM byty WHERE isVariant = 0";
            count = "SELECT COUNT(*) as count FROM byty WHERE isVariant = 0";
        } else {
            query = "SELECT * FROM byty WHERE " + floor + building + rooms + stav + " AND isVariant = 0";
            query = query.replace(/\bAND\b/, '');
            query2 = "SELECT * FROM byty WHERE 1=1 " + canrooms + stav + building + floor + " AND isVariant = 0";
            count = "SELECT COUNT(*) as count FROM byty WHERE 1=1 AND " + floor + building + rooms + stav + " AND isVariant = 0";
            count = count.replace(/\bAND\b/, '');
        }
        console.log(query2);
        const rows = await executeQuery({
            query: query
        });

        const canRooms = await executeQuery({
            query: query2
        });

        console.log(canRooms.length)

        let more = rows.concat(canRooms);
        more = more.sort((a, b) => a.id - b.id);
        const data = more.filter((obj, index, self) =>
            index === self.findIndex((t) => (t.id === obj.id))
        );
        const countQuery = await executeQuery({
            query: count
        });

        console.log(data.count)
        res.status(200).json({data: data, count: countQuery[0].count, rooms: canrooms});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}
