import dynamic from "next/dynamic";

const Image = dynamic(() => import("next/image"))
const Link = dynamic(() => import("next/link"))

export default function Developer() {
    return <>
        <div className="grid md:grid-cols-4 relative items-center z-10 grid-cols-1 lg:pl-24 pb-0 -mt-10 gap-14">
            <div className="max-w-xl lg:pl-14 px-10 col-span-2 lg:px-0">
                <Image src={"/logo_Sympatia_Group.svg"} className={"mb-4"} alt={"sympatia logo"} width={300}
                       height={100}/>
                <p className={"mb-4"}>Sympatia je renomovaný a úspešný investičný developer a zároveň jeden z
                    najstarších obchodníkov s cennými papiermi na Slovensku. Pôsobí na trhu viac ako 21 rokov. </p>
                <p className={"mb-4"}>Zameriava sa na investovanie vlastných a klientskych finančných prostriedkov
                    prevažne v segmente priemyselných nehnuteľností do výstavby logistických parkov a výrobných hál
                    na
                    Slovensku, v Chorvátsku a Slovinsku a tiež do rezidenčných projektov na Slovensku.</p>
                <p className={"mb-4"}>Doteraz úspešne zrealizovala 15 projektov za takmer 200 miliónov € a aktuálne
                    má v
                    portfóliu 22 projektov v celkovej hodnote viac ako 620 miliónov € pri dokončení. Jej silnou
                    stránkou
                    je úspešné vyhľadávanie, analyzovanie a realizovanie prémiových projektov, ktoré sú zárukou
                    optimálneho pomeru výnosu k riziku. Umožňuje tak investorom maximalizovať zhodnotenie nimi
                    zverených
                    prostriedkov a pravidelne dosahovať finančný úspech.</p>
                <p className={"mb-4"}>Ako skúsený developer má za sebou niekoľko rezidenčných projektov určených pre
                    široké spektrum klientely od bytových domov až po prémiové projekty “na kľúč” pre tých
                    najnáročnejších.</p>
                <Link href={"https://www.sympatia.sk"}><strong
                    className={"underline"}>www.sympatia.sk</strong></Link>
            </div>
            <Image src={"/developerSympatia.png"} className={"col-span-1 md:col-span-2 lg:pl-24 pb-0 pr-0 lg:pt-24"}
                   alt={"rodina v byte"} width={1920} quality={30}
                   height={1080}/>
        </div>
        <div className="grid md:grid-cols-4 -mb-20 lg:mb-20 mt-10 items-center grid-cols-1 pt-0 lg:pl-24 gap-14">
            <Image src={"/lila.png"}
                   className={"col-span-1 order-last lg:order-first md:col-span-2 mb-40 lg:mb-0 pr-0 lg:pt-24"}
                   alt={"rodina v byte"} width={1920} quality={30}
                   height={1080}/>
            <div className="max-w-xl lg:pl-14 col-span-2 px-10 lg:px-0">
                <h3 className="mb-10 text-[8vw] lg:text-5xl font-semibold lg:font-normal">Leila s.r.o.</h3>
                <p className={"mb-4"}>Leila je slovenský developer viacerých úspešných rezidenčných projektov v
                    Bratislave a širšom okolí. Pôsobí na trhu od roku 2015 a zameriava sa na realizáciu rodinných domov
                    s dôrazom na individuálne riešenia pre klientov.
                    <br/><br/>
                    Doteraz zrealizoval 4 developerské projekty v hodnote viac ako 11 miliónov eur. Jeho silnou stránkou
                    je výnimočná znalosť lokálnych pomerov a špecifík v rezidenčnom developmente v Podunajských
                    Biskupiciach, vo Vrakuni, ale aj v rakúskych pohraničných obciach.
                    <br/><br/>
                    V roku 2021, po ukončení projektu Julianin Dvor s 24 domovými jednotkami vo Vrakuni sa spoločnosť
                    Leila rozhodla spojiť sily so Sympatiou pri ďalšom projekte Laurin Dvor. Ich spoločnou snahou je
                    priniesť klientom cenovo dostupné bývanie v bezpečnej lokalite s prémiovými benefitmi.

                </p>
            </div>
        </div>
    </>
}
