import Link from "next/link";
import {Button} from "flowbite-react";

export default function FlatNavigation({isMobile, room, getter}) {
    return (
        <>
            {isMobile ? <div
                    className={"flex mt-20 top-[5rem] w-full items-center py-3 bg-[#E1E5ED] shadow-md gap-2 z-20 justify-center"}>
                    {[...Array(5)].map((e, i) => (
                        <Link key={i} href={"/ponuka-bytov/" + (i + 1) + "-izbove"} onClick={() => getter("reset")}>
                            <button
                                className={parseInt(room) === i + 1 ? "buttonLinkActive shadow-md border border-gray-400 text-white py-[1vw] rounded-full text-[5vw] px-[3vw]" : "buttonLink text-white py-[1vw] rounded-full text-[5vw] px-[3vw]"}>{i + 1}i
                            </button>
                        </Link>
                    ))}
                    <Link href={"/ponuka-bytov/vsetky-byty"} onClick={() => getter("reset")}>
                        <button
                            className={room === "all" ? "buttonLinkActive shadow-md border border-gray-400 text-[4vw] py-[2vw] px-[3vw]" : "buttonLink text-[5vw] py-[1vw] px-[3vw]"}>Byty
                        </button>
                    </Link>
                    <Link href={"/parkovacie-miesta"} onClick={() => getter("reset")}>
                        <button
                            className={room === "parking" ? "buttonLinkActive shadow-md border border-gray-400 text-[4vw] py-[1vw] px-[3vw]" : "bg-[#0259A0] text-[4vw] hover:bg-black transition-all rounded-xl text-white py-[1vw] px-[3vw]"}>
                            <strong className={"text-xl"}>P</strong>
                        </button>
                    </Link>
                </div> :
                <div
                    className={"flex items-center fixed top-[5.3rem] w-full shadow-md py-3 pt-5 px-10 z-30 bg-white justify-between"}>
                    <div
                        className={"flex items-center justify-start"}>
                        {[...Array(5)].map((e, i) => (
                            <Link key={i} href={"/ponuka-bytov/" + (i + 1) + "-izbove"} onClick={() => getter("reset")}>
                                <button
                                    className={parseInt(room) === i + 1 ? "buttonLinkActive shadow-md border border-gray-400 py-2 px-4 mr-2" : "buttonLink py-2 px-4 mr-2"}>{i + 1}-izbové
                                </button>
                            </Link>
                        ))}
                        <Link href={"/ponuka-bytov/vsetky-byty"} onClick={() => getter("reset")}>
                            <button
                                className={room === "all" ? "buttonLinkActive shadow-md border border-gray-400 py-2 px-4 mr-2" : "buttonLink py-2 px-4 mr-2"}>Všetky
                                byty
                            </button>
                        </Link>
                        <Link href={"/parkovacie-miesta"} onClick={() => getter("reset")}>
                            <button
                                className={room === "parking" ? "buttonLinkActive shadow-md border border-gray-400 py-2 px-4 mr-2" : "bg-[#0259A0] hover:bg-black transition-all rounded-xl text-white py-2 px-4 mr-2"}>
                                <strong className={"text-xl"}>P</strong>arkovanie
                            </button>
                        </Link>
                    </div>
                    <div className={"relative"}>
                        <div
                            className={"bg-red-500 text-white p-1 rounded-xl text-xs flex items-center justify-center absolute -top-4 -right-4 z-20"}>NOVINKA!
                        </div>
                        <Link href={"/3d-vyber"}>
                            <Button color={"dark"} className={"hover:bg-gray-700"} pill>3D výber</Button>
                        </Link>
                    </div>
                </div>
            }
        </>
    )
}