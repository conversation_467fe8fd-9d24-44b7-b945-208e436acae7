//@ts-nocheck
import {useGLTF} from "@react-three/drei";

export function Val() {
    const {nodes, materials} = useGLTF("/val.glb");
    return (
        <group scale={0.5} position={[-55, -0.1, 0.85]} dispose={null}>
            <mesh
                castShadow
                receiveShadow
                geometry={nodes.Plane["geometry"]}
                material={materials["TRAVA.002"]}
            />
        </group>
    );
}

useGLTF.preload("/val.glb");