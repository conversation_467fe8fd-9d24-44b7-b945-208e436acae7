import {useEffect, useRef, useState} from "react";
import {Spin<PERSON>, Toolt<PERSON>} from 'flowbite-react';
import Link from "next/link";
import Image from "next/image"
import dynamic from "next/dynamic";

const MapTooltip = dynamic(() => import('./MapTooltip'));

export default function ParkingPick({section, isMobile, place, isDetail}) {
    let imageWidth: number, imageHeight: number;
    switch (section) {
        case '1':
            imageWidth = 550
            imageHeight = 225
            break;
        case '2':
            imageWidth = 450
            imageHeight = 322
            break;
        case '3':
            imageWidth = 350
            imageHeight = 235
            break;
        case '4':
            imageWidth = 550
            imageHeight = 220
            break;
        case 'V':
            imageWidth = 350
            imageHeight = 503
            break;
        case 'V2':
            imageWidth = 100
            imageHeight = 400
            break;
        case '7':
            imageWidth = 1000
            imageHeight = 1080
            break;
        case 'VS':
            imageWidth = 695
            imageHeight = 85
    }

    const [data, setData] = useState([]);
    const [hoveredFlat, setHoveredFlat] = useState()
    const [loading, setLoading] = useState(true)
    const [imageData, setImageData] = useState([])
    const [imageLoaded, setImageLoaded] = useState(false)
    let actualImage = useRef(null)

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch('/api/parking/getAllFromSection?section=' + section);
                const result = await response.json();
                setData(result.data);
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };
        fetchData().then(() => setLoading(false))
    }, [section]);

    const handleMouseEnter = (e: any): void => {
        setHoveredFlat(e.target.id)
        document.getElementById("tooltipik").style.display = "block";
    };

    const handleMouseLeave = (): void => {
        document.getElementById("tooltipik").style.display = "none";
    }

    const setMousePosition = (e: { clientX: number; clientY: number; }) => {
        let leftOffset: number;
        if (((e.clientX / 10) / 2.5) < 42) {
            leftOffset = ((e.clientX / 10) / 2.5) * 2;
        } else {
            leftOffset = ((e.clientX / 10) / 2.5) / 2;
        }
        document.getElementById("tooltipik").style.left = leftOffset + "%";
        document.getElementById("tooltipik").style.top = e.clientY / 10 + "%";
    }

    useEffect(() => {
        setImageData([])
        if (actualImage.current !== undefined) {
            if (actualImage.current.clientHeight < imageHeight || actualImage.current.clientHeight > imageHeight) {
                setImageData([
                    imageWidth,
                    imageHeight
                ]);
            } else {
                setImageData([
                    actualImage.current.clientWidth ?? "0",
                    actualImage.current.clientHeight ?? "0"
                ]);
            }
        }
    }, [imageLoaded, section]);

    return (
        <div onMouseMove={setMousePosition}
             className={section === "V2" && isDetail ? "p-5 rotate-90" : section === "V2" ? "overflow-visible rotate-90 relative" : "overflow-visible relative"}>
            <Tooltip id={"tooltipik"} animation="duration-500" trigger="hover" arrow={false}
                     className={section === "V2" ? "shadow-lg -rotate-90 max-w-xs hidden overflow-visible" : "shadow-lg max-w-xs hidden overflow-visible"}
                     content={<MapTooltip flat={hoveredFlat}/>}>
                <Image
                    id={"floorImage"} ref={actualImage}
                    src={"/parkovacie-miesta/velka situ_vnutorne_" + section + ".svg"}
                    alt={"podorys"}
                    className={section === "V" ? "px-16" : section === "V2" ? "p-5" : "p-10"}
                    width={imageWidth} onLoad={() => setImageLoaded(true)}
                    height={imageHeight}
                />
                {loading ? <Spinner/> :
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className={section === "V2" && isDetail ? "svgOverlay p-10 -ml-5 svgcko overflow-visible" : section === "V" ? "px-10 svgOverlay svgcko overflow-visible" : section === "V2" && isDetail ? "svgOverlay p-10 svgcko overflow-visible" : section === "V2" ? "svgOverlay p-5 svgcko overflow-visible" : "svgOverlay svgcko p-10 overflow-visible"}
                        viewBox={"0 0 " + imageData[0] + " " + imageData[1]}>
                        {data != undefined && data.map((flat, index) =>
                            <Link key={index} id={index.toString()}
                                  style={{pointerEvents: flat.dostupnost === 1 ? "auto" : "none"}}
                                  href={flat.dostupnost === 1 ? "/parkovacie-miesta/" + flat.id : ""}>
                                <rect
                                    onMouseEnter={handleMouseEnter}
                                    onMouseLeave={handleMouseLeave}
                                    className={flat.rotation ? "rotate-[25deg]" : ""}
                                    id={JSON.stringify(flat)}
                                    x={flat.x}
                                    y={flat.y}
                                    fill={place === flat.id ? "black" : flat.dostupnost === 1 ? "#B4CC7A" : flat.dostupnost === 2 ? "#FFAB48" : "#FF0000"}
                                    style={{
                                        height: flat.height + "rem",
                                        width: flat.width + "rem",
                                        opacity: .7
                                    }}/>
                            </Link>
                        )}
                    </svg>}
            </Tooltip>
        </div>
    )
}