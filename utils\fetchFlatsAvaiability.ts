/**
 * This function return object of number of free and all available flats based on floor and building declared in params.
 * @param floor
 * @param letter
 */
export default async function fetchFlatData(floor: number, letter: string): Promise<any> {
    try {
        const [allFlatsResponse, freeFlatsResponse] = await Promise.all([
            fetch(`/api/hello?floor=${floor}&building=${letter}`),
            fetch(`/api/getAllFlatsForMobile?floor=${floor}&building=${letter}`)
        ]);

        const [allFlatsResult, freeFlatsResult] = await Promise.all([
            allFlatsResponse.json(),
            freeFlatsResponse.json()
        ]);

        return {
            allFlats: allFlatsResult.data.length,
            freeFlats: freeFlatsResult.data.length
        }
    } catch (error) {
        console.error('Error fetching data:', error);
        return error;
    }
};