import {Footer} from "flowbite-react";
import Link from "next/link";
import Image from "next/image";
import {useRouter} from "next/router";
import {useEffect, useState} from "react";

export default function LegalFooter({isMobile}) {
    const [hide, setHide] = useState(false)
    const router = useRouter()

    useEffect(() => {
        if (router.pathname === "/napiste-nam" || router.pathname.includes("admin") || router.pathname === "/3d-vyber") {
            setHide(true)
        }
    })


    if (hide) {
        return null
    }

    return (
        <Footer container className={"z-50 relative"}>
            <div className="w-full text-center">
                <div className="w-full sm:flex sm:items-center sm:justify-between p-5">
                    <div
                        className={"flex mb-10 md:mb-0 items-center justify-center flex-wrap lg:flex-nowrap xl:gap-32 lg:gap-20 md:gap-10"}>
                        <Link href={"/"}>
                            <Image src={"/logo.svg"} alt={"Laurin dvor Logo"} width={150} height={50}/>
                        </Link>
                        <Link href={"https://www.unicreditbank.sk"}>
                            <Image src={"/ucbank_logo.jpg"} alt={"Unit Credit Bank Logo"} width={250} height={50}/>
                        </Link>
                    </div>
                    <Footer.LinkGroup
                        className={isMobile ? "grid grid-cols-2 gap-5" : "flex-col md:flex-row flex w-full text-lg items-center justify-center gap-8"}>
                        <span>Laurin dvor s. r. o.</span>
                        <span>Vajnorská 21 A</span>
                        <span>831 03 Bratislava</span>
                        <span>IČO: <b>********</b></span>
                        <span>IČ DPH: <b>SK2122114247</b></span>
                    </Footer.LinkGroup>
                    {isMobile && <><Footer.Divider/><p>SLEDUJTE NÁS</p></>}
                    <div className="mt-4 justify-center flex space-x-6 sm:mt-0 sm:justify-center">
                        <Link href={"https://www.facebook.com/profile.php?id=**************"}>
                            <svg width="2vw" height="2vw" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"
                                 fill="none">
                                <path fill="#1877F2"
                                      d="M15 8a7 7 0 00-7-7 7 7 0 00-1.094 13.915v-4.892H5.13V8h1.777V6.458c0-1.754 1.045-2.724 2.644-2.724.766 0 1.567.137 1.567.137v1.723h-.883c-.87 0-1.14.54-1.14 1.093V8h1.941l-.31 2.023H9.094v4.892A7.001 7.001 0 0015 8z"/>
                                <path fill="#ffffff"
                                      d="M10.725 10.023L11.035 8H9.094V6.687c0-.553.27-1.093 1.14-1.093h.883V3.87s-.801-.137-1.567-.137c-1.6 0-2.644.97-2.644 2.724V8H5.13v2.023h1.777v4.892a7.037 7.037 0 002.188 0v-4.892h1.63z"/>
                            </svg>
                        </Link>
                    </div>
                </div>
                <Footer.Divider/>
                <div className={"p-5 flex flex-col lg:flex-row gap-6 justify-around lg:px-[2vw]"}>
                    <small className={"text-gray-400 lg:w-1/2"}>Developer si ako budúci predávajúci vyhradzuje právo na
                        zmenu a
                        doplnenie informácií zverejnených na
                        webovej stránke www.laurindvor.com. Právne záväzné informácie budúci kupujúci obdrží v zmluve o
                        budúcej
                        zmluve o prevode vlastníctva bytu a/ alebo v zmluve o prevode vlastníctva bytu.</small>
                    <Link href={"/technicke-specifikacie"}
                          className={"flex items-center bg-gray-700 text-white font-bold px-3 rounded-lg hover:bg-gray-900 transition-all gap-4"}>
                        <p>Technické špecifikácie</p>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
                             stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                             className="lucide lucide-square-arrow-out-up-right">
                            <path d="M21 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h6"/>
                            <path d="m21 3-9 9"/>
                            <path d="M15 3h6v6"/>
                        </svg>
                    </Link>
                    <Link href={"/Laurin_Dvor_Katalog_bytov_250210.pdf"} target={"_blank"}
                          className={"flex items-center gap-4"}>
                        <p>Stiahnuť katalóg bytov <small>(PDF 18,5MB)</small></p>
                        <svg className="w-10 h-10 text-gray-800 dark:text-white" aria-hidden="true"
                             xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
                             viewBox="0 0 24 24">
                            <path fillRule="evenodd"
                                  d="M9 2.221V7H4.221a2 2 0 0 1 .365-.5L8.5 2.586A2 2 0 0 1 9 2.22ZM11 2v5a2 2 0 0 1-2 2H4a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2 2 2 0 0 0 2 2h12a2 2 0 0 0 2-2 2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2V4a2 2 0 0 0-2-2h-7Zm-6 9a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h.5a2.5 2.5 0 0 0 0-5H5Zm1.5 3H6v-1h.5a.5.5 0 0 1 0 1Zm4.5-3a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1.376A2.626 2.626 0 0 0 15 15.375v-1.75A2.626 2.626 0 0 0 12.375 11H11Zm1 5v-3h.375a.626.626 0 0 1 .625.626v1.748a.625.625 0 0 1-.626.626H12Zm5-5a1 1 0 0 0-1 1v5a1 1 0 1 0 2 0v-1h1a1 1 0 1 0 0-2h-1v-1h1a1 1 0 1 0 0-2h-2Z"
                                  clipRule="evenodd"/>
                        </svg>
                    </Link>
                </div>
                <Footer.Divider/>
                <div className={"p-5"}>
                    <Footer.Copyright href="https://sympatia.sk" by=" Sympatia™" year={2025}/>
                    <Link href={"https://mrstrawberry.dev"} target={"_blank"} className={"text-transparent"}>Coding</Link>
                </div>
            </div>
        </Footer>
    );
}