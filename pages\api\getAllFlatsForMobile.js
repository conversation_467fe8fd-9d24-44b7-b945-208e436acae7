import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {floor} = req.query;
        const {building} = req.query;
        const rows = await executeQuery({
            query: "SELECT * FROM byty WHERE poschodie = ? AND budova = ? AND dostupnost = 1 AND isVariant = 0",
            values: [floor, building]
        })
        res.status(200).json({data: rows});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}