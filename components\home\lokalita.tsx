import Link from "next/link";
import Image from "next/image";

export default function LokalitaIndex({isMobile}: { isMobile: boolean }) {
    return (
        <div className={"flex flex-col justify-center items-center pt-0 mb-20"}>
            <h2 className={"text-3xl lg:text-6xl px-16 lg:px-0 w-full mb-8 font-normal text-center"}>Výnimočne <strong>dostupná
                lokalita</strong></h2>
            {isMobile ?
                <Link href={"/lokalita"}><Image src={"/lokalitaPod.png"} className={"shadow-2xl rounded-2xl"}
                                                width={800} height={500} quality={50}
                                                alt={"Lokalita obytného komplexu"}/></Link> :
                <Link href={"/lokalita"}>
                    <div className={"flex justify-center items-center mb-20"}>
                        <Image src={"/lokalitaPod.png"} className={"rounded-3xl shadow-xl"} alt={"mapa lokality"}
                               width={1920} quality={60}
                               height={1080}/>
                    </div>
                </Link>}
        </div>
    )
}