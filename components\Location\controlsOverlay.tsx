interface ControlsOverlayProps {
    isZoomed: boolean,
    changer: () => void
}

export default function ControlsOverlay(props: ControlsOverlayProps) {
    return (
        <div className={"controlsOverlay h-screen absolute -mt-20 w-full"}>
            <div className={"relative h-full flex justify-center w-full"}>
                <div onClick={props.changer}
                     className={"bg-white bottom-5 cursor-pointer hover:bg-gray-800 transition-all hover:text-white rounded-xl shadow-2xl z-30 fixed flex justify-center items-center p-2 font-medium px-4"}>
                    {props.isZoomed ? <div className={"flex gap-2 text-[1vw] items-center"}><span>Zobraziť širšie okolie</span>
                            <svg className="w-[1.5vw] h-[1.5vw]" aria-hidden="true"
                                 xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                      d="M12 19V5m0 14-4-4m4 4 4-4"/>
                            </svg>
                        </div>
                        :
                        <div className={"flex gap-2 text-[1vw] items-center"}><span>Zobraziť bližšie okolie</span>
                            <svg className="w-[1.5vw] h-[1.5vw]" aria-hidden="true"
                                 xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none"
                                 viewBox="0 0 24 24">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                      strokeWidth="2" d="M12 6v13m0-13 4 4m-4-4-4 4"/>
                            </svg>
                        </div>
                    }
                </div>
            </div>
        </div>
    )
}