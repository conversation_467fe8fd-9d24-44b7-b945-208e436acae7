<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="SAM" uuid="3b4eaafa-47ca-44cb-b049-dc355acc3e1c">
      <driver-ref>postgresql</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>org.postgresql.Driver</jdbc-driver>
      <jdbc-url>********************************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="SYMPATIA ORACLE" uuid="9a03a04d-b4aa-4a24-821c-1adca5e690f5">
      <driver-ref>oracle.19</driver-ref>
      <synchronize>true</synchronize>
      <auto-commit>false</auto-commit>
      <jdbc-driver>oracle.jdbc.OracleDriver</jdbc-driver>
      <jdbc-url>*****************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="@192.168.1.25" uuid="7bcdbeb6-c048-41ce-83be-646db7a6a94f">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>******************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="database.introspection.mysql.dbe5060" value="true" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>