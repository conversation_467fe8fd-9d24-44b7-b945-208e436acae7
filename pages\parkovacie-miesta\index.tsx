'use client'
import executeQuery from "../../utils/dbConnection";
import dynamic from "next/dynamic";
import {useEffect, useRef, useState} from "react";
import {useRouter} from "next/router";
import Link from "next/link";
import {useSearchParams} from "next/navigation";
import FlatNavigation from "../../components/ponuka-bytov/izba/flatNavigation";

const ParkingPick = dynamic(() => import('../../components/parkovacie-miesta/parkingPick'));
const Parkovacka = dynamic(() => import('../../components/parkovacie-miesta/parkovacka'));
const ResponsiveImgMap = dynamic(() => import('../../components/ponuka-bytov/izba/responsiveImgMap'));
const DesktopLayoutParking = dynamic(() => import('../../components/parkovacie-miesta/desktopLayout'));
const MobileLayoutParking = dynamic(() => import('../../components/parkovacie-miesta/mobileLayout'));

export const getServerSideProps = (async (context: any) => {
    let placeNo: number;
    if (context.req.url.match(/place=(\d+)/)) {
        placeNo = context.req.url.match(/place=(\d+)/)[1];
    }

    const budovy = ['1', '2', '3', '4', 'V', 'V2', 'VS'];
    const numberOfFlatsPromises = budovy.map(async (budova) => {
        const result = await executeQuery({
            query: 'SELECT COUNT(id) AS id FROM parking WHERE section = ? AND dostupnost = 1',
            values: [budova]
        });
        return result[0].id;
    });
    const numberOfFlats = await Promise.all(numberOfFlatsPromises);

    let place = []
    if (placeNo !== undefined) {
        let flaty = await executeQuery({
            query: 'SELECT * FROM parking WHERE id = ?',
            values: [placeNo]
        })
        Object.keys(flaty).forEach((k) => {
            place.push(flaty[k])
        });
        // @ts-ignore
        place = JSON.stringify(place[0]);
    }
    return {props: {numberOfFlats, place}}
})

export default function ParkovacieMiesta({place, numberOfFlats, isMobile}) {
    const parkingSpots = useRef("");
    const [selectedFlat, setSelectedFlat] = useState<string>("")
    const [flatS, setFlatS] = useState([])
    const router = useRouter();
    const params = useSearchParams()
    const sectionParam = params.get("section")

    useEffect(() => {
        if (sectionParam !== null)
            getSelectedFlat(sectionParam)
    }, []);

    const getSelectedFlat = (flat: string) => {
        setSelectedFlat(flat)
        if (isMobile) {
            setTimeout(() => {
                //@ts-ignore
                parkingSpots.current?.scrollIntoView({behavior: "smooth"})
            }, 200)
        } else {
            setTimeout(() => {
                //@ts-ignore
                parkingSpots.current?.scrollIntoView({behavior: "smooth", block: "center"})
            }, 200)
        }
    }

    useEffect(() => {
        async function getSelectedFlats() {
            try {
                const response = await fetch('/api/parking/getAllFromSection?section=' + selectedFlat);
                const result = await response.json();
                setFlatS(result.data)
            } catch (error) {
                console.error('Error fetching data:', error);
            }
        }

        if (selectedFlat !== "") {
            getSelectedFlats().then()
        }
    }, [selectedFlat]);

    useEffect(() => {
        router.replace(router.asPath).then()
    }, [router.asPath])

    return (
        <>
            <div className="App lg:pt-48 relative">
                <FlatNavigation isMobile={isMobile} room={"parking"} getter={() => {}}/>
                <div className={"px-10 pt-10 lg:pt-0 text-center"}>
                    <div className="flex gap-2 items-center justify-center">
                        {selectedFlat ?
                            <svg className="w-7 lg:w-12 h-7 lg:h-12 text-gray-800 mt-1.5 -ml-[5vw]" aria-hidden="true"
                                 xmlns="http://www.w3.org/2000/svg" fill="#51B314" viewBox="0 0 24 24">
                                <path fillRule="evenodd"
                                      d="M2 12a10 10 0 1 1 20 0 10 10 0 0 1-20 0Zm13.7-1.3a1 1 0 0 0-1.4-1.4L11 12.6l-1.8-1.8a1 1 0 0 0-1.4 1.4l2.5 2.5c.4.4 1 .4 1.4 0l4-4Z"
                                      clipRule="evenodd"/>
                            </svg>
                            : <svg className="w-7 lg:w-12 h-7 lg:h-12 text-gray-800 mt-1.5 -ml-[5vw]" aria-hidden="true"
                                   xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round"
                                      strokeWidth="1.5"
                                      d="M8.5 11.5 11 14l4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                            </svg>}
                        <h2 className={"font-medium text-[7vw] lg:text-[3vw] text-center"}>Vyberte si sekciu</h2>
                    </div>
                    <div className={"mb-14"}>
                        <small className={"text-gray-500"}>Parkovaciu sekciu si vyberiete ťuknutím na oblasť, o
                            ktorú máte záujem</small>
                    </div>
                </div>
                <div className={"flex flex-col justify-center gap-10"}>
                    {isMobile ? <ResponsiveImgMap getter={getSelectedFlat} predmetZaujmu={"parking"}
                                                  numberOfFlats={numberOfFlats} room={undefined}
                                                  selectedBuilding={selectedFlat} isMobile={isMobile}
                                                  isFlatDetail={false}/> :
                        <Parkovacka isFlatDetail={false} getter={getSelectedFlat}
                                    flatSelected={sectionParam ? sectionParam : selectedFlat}/>
                    }

                </div>
                {/*@ts-ignore*/}
                <div ref={parkingSpots} className={"pt-20 pb-40 mb-20 px-5"}>
                    {selectedFlat !== "" ?
                        <>
                            <div className={"mb-10 text-center mt-40 px-5"}>
                                <h2 className={"font-medium text-[7vw] lg:text-[3vw] text-center"}>Vyberte si konkrétne
                                    parkovacie miesto</h2>
                                <small className={"text-gray-500 text-center"}>Parkovacie miesto si
                                    vyberiete kliknutím
                                    na tlačidlo nižšie s príslušným číslom parkovacieho miesta</small>
                            </div>
                            <div className={"podorysY items-start justify-center gap-16 flex"}>
                                <div className="text-center flex justify-center flex-col gap-4 relative">
                                    <ParkingPick section={selectedFlat} isMobile={isMobile} place={undefined}
                                                 isDetail={false}/>
                                </div>
                            </div>
                            {isMobile &&
                                <div className={"grid grid-cols-3 py-10 gap-2"}>
                                    {flatS.map((park: object, index: number) => (
                                        <>
                                            {park["dostupnost"] === 0 ? <button key={index} style={{
                                                backgroundColor: park["dostupnost"] === 1 ? "#B4CC7A" : park["dostupnost"] === 2 ? "#FFAB48" : "#FF0000",
                                            }}
                                                                                onClick={() => alert("Toto parkovacie miesto už nie je k dispozícii!")}
                                                                                className="rounded-full py-3 w-full">{park["parking_number"]}</button> : park["dostupnost"] === 2 ?
                                                <button key={index} style={{
                                                    backgroundColor: park["dostupnost"] === 1 ? "#B4CC7A" : park["dostupnost"] === 2 ? "#FFAB48" : "#FF0000",
                                                }}
                                                        onClick={() => alert("Toto parkovacie miesto je momentálne rezervované.")}
                                                        className="rounded-full py-3 w-full">{park["parking_number"]}</button> :
                                                <Link key={index}
                                                      href={"/parkovacie-miesta/" + park["id"]}>
                                                    <button style={{
                                                        backgroundColor: park["dostupnost"] === 1 ? "#B4CC7A" : park["dostupnost"] === 2 ? "#FFAB48" : "#FF0000",
                                                    }}
                                                            className="rounded-full py-3 w-full">{park["parking_number"]}</button>
                                                </Link>}
                                        </>
                                    ))}
                                </div>}
                            <div className={"legend mt-24 flex justify-evenly"}>
                                <div className="flex gap-2 items-center">
                                    <div
                                        className={"w-9 h-9 border border-gray-500 rounded-md bg-[#B4CC7A]"}></div>
                                    <span>voľný</span>
                                </div>
                                <div className="flex gap-2 items-center">
                                    <div
                                        className={"w-9 h-9 border border-gray-500 rounded-md bg-[#FFAB48]"}></div>
                                    <span>rezervovaný</span>
                                </div>
                                <div className="flex gap-2 items-center">
                                    <div
                                        className={"w-9 h-9 border border-gray-500 rounded-md bg-[#FF0000]"}></div>
                                    <span>predaný</span>
                                </div>
                            </div>
                        </>
                        : <></>}
                </div>
                {place.length != 0 &&
                    <>
                        {isMobile ?
                            <MobileLayoutParking place={place}/> :
                            <DesktopLayoutParking place={place} getter={getSelectedFlat} selectedFlat={selectedFlat}
                                                  isMobile={isMobile}/>}
                    </>
                }
            </div>
        </>)
}