interface BuildConfig {
    x: number,
    z: number,
    rotation: number[],
    geometry: number[],
    letter: string,
    letterPosition: number[],
    position: number[],
    buildingZoom: number[],
    buildingZoomMobile: number[]
}

const buildingConfigurations: BuildConfig[] = [
    {
        x: 38.6,
        z: 0.5,
        rotation: [0, Math.PI / 2, Math.PI],
        geometry: [19.7, 1.5, 7],
        letter: 'A',
        letterPosition: [39, 12, 0],
        position: [39, 12, 0],
        buildingZoom: [-10, 5, 45, 40, 5, 0],
        buildingZoomMobile: [-30, 5, 70, 40, 5, 0]
    },
    {
        x: 23.5,
        z: -1.4,
        rotation: [-Math.PI, 0, 0],
        geometry: [14, 1.5, 6.8],
        letter: 'B',
        letterPosition: [23, 12, -2.55],
        position: [27.2, 6.5, -2.55],
        buildingZoom: [23.2, 5, 75, 25, 5, -3],
        buildingZoomMobile: [23, 5, 100, 23, 5, -3]
    },
    {
        x: 9.9,
        z: -1.4,
        rotation: [-Math.PI, 0, 0],
        geometry: [13.5, 1.5, 6.8],
        letter: 'C',
        letterPosition: [10, 12, -2.55],
        position: [14.2, 6.5, -2.55],
        buildingZoom: [11.02, 5, 75, 11.02, 5, -3],
        buildingZoomMobile: [10, 5, 100, 10, 5, -3]
    },
    {
        x: -5.3,
        z: 4,
        rotation: [-Math.PI, 0, 0],
        geometry: [14.5, 1.5, 6.8],
        letter: 'D',
        letterPosition: [-5.5, 12, 4],
        position: [-1.45, 6.5, 3],
        buildingZoom: [-4.07, 5, 70, -4.07, 5, 0],
        buildingZoomMobile: [-5.5, 5, 105, -5.5, 5, 0]
    },
    {
        x: -22.2,
        z: 4,
        rotation: [-Math.PI, 0, 0],
        geometry: [14.5, 1.5, 6.8],
        letter: 'E',
        letterPosition: [-22.5, 12, 4],
        position: [-18.3, 6.5, 3],
        buildingZoom: [-20.75, 5, 70, -20.75, 5, 0],
        buildingZoomMobile: [-22.3, 5, 105, -22.3, 5, 0]
    },
    {
        x: -39,
        z: 4,
        rotation: [-Math.PI, 0, 0],
        geometry: [14.5, 1.5, 6.8],
        letter: 'F',
        letterPosition: [-39, 12, 4],
        position: [-35, 6.5, 3],
        buildingZoom: [-38, 5, 70, -38, 5, 0],
        buildingZoomMobile: [-39, 5, 105, -39, 5, 0]
    },
];
export default buildingConfigurations;