import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {rooms} = req.query;
        const rows = await executeQuery({
            query: 'SELECT * FROM byty WHERE rooms = ? AND dostupnost = 1',
            values: [rooms]
        })
        res.status(200).json({data: rows});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}