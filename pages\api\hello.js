import executeQuery from "../../utils/dbConnection";

export default async function handler(req, res) {
    try {
        const {floor} = req.query;
        const {building} = req.query;
        const rows = await executeQuery({
            query: 'SELECT * FROM byty WHERE poschodie = ? AND budova = ? AND isVariant = 0',
            values: [floor, building]
        });
        console.log(`SELECT * FROM byty WHERE poschodie = ${floor} AND budova = ${building} AND isVariant = 0`)
        res.status(200).json({data: rows});
    } catch (error) {
        console.error('Error fetching data:', error);
        res.status(500).json({error: 'Internal Server Error'});
    }
}