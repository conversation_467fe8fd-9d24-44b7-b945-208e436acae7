import Image from "next/legacy/image";
import {Badge} from "flowbite-react";

export default function LokalitaTooltip({location}) {
    return (
        <div className={"flex flex-col gap-1"}>
            <div className={"flex gap-4 items-center"}>
                <div className={"flex"}>
                    <Image src={location.image} width={55} height={60}/>
                </div>
                <div className={"info"}>
                    <h3 className={"text-xl"}>{location.name}</h3>
                    <small>{location.street}</small>
                    <div className={"flex mt-2 items-center py-1 gap-10 justify-between"}>
                        {location.openingHours !== "" &&
                            <Badge><p>{location.openingHours}</p></Badge>
                        }
                        {location.busLinky !== undefined &&
                            <div className={"flex my-2 gap-1"}>
                                {location.busLinky?.map((bus: number, index: number) =>
                                    <Badge key={index} color={"teal"}><p>{bus}</p></Badge>
                                )}
                            </div>
                        }
                        <div className={"flex gap-1"}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20px" height="20px" viewBox="0 0 24 24"
                                 fill="currentColor" x="227.5" y="227.5" role="img">
                                <g fill="currentColor">
                                    <path fill="currentColor"
                                          d="M13.5 5.5c1.1 0 2-.9 2-2s-.9-2-2-2s-2 .9-2 2s.9 2 2 2zM9.8 8.9L7 23h2.1l1.8-8l2.1 2v6h2v-7.5l-2.1-2l.6-3C14.8 12 16.8 13 19 13v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1c-.3 0-.5.1-.8.1L6 8.3V13h2V9.6l1.8-.7"/>
                                </g>
                            </svg>
                            <small>{location.walkingDuration} min. pešo</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}