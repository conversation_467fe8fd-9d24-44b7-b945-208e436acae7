@tailwind base;
@tailwind components;
@tailwind utilities;

h2 {
  font-size: 1.5em;
}
.articleIntro h2 {
  font-size: 1rem;
}
.globe {
    display: grid;
    place-items: center;
    height: 100vh;
    cursor: grabbing;
}

.liveView {
    background: linear-gradient(170deg, #a4a0a5, #a2a0a6, #a4a6a0, #a6a0a4);
}

.iframk {
    min-height: 57vh !important;
}

.App {
    min-height: 100vh;
    background: #E1E5ED;
}

.buttonEmail {
    background: #D5E7AB;
    color: black;
    font-weight: 600;
    padding: 1rem;
    border-radius: 10px;
    transition: .3s;
    cursor: pointer;
}

.buttonEmail:hover {
    background: #c5dc90;
}

.myBtn {
    position: absolute;
    background-color: #333;
    margin: 1rem;
    color: white;
    border-radius: 10px;
    font-weight: 700;
    transition: 0.2s;
    padding: .5rem 1rem;
}

.myBtn:hover {
    background-color: #8c8c8c;
    cursor: pointer;
}


.content {
    transform: translate3d(calc(50% + 10vw), 0, 0);
    text-align: left;
    background: #202035;
    color: white;
    padding: 15px 15px 20px;
    border-radius: 15px;
    font-size: 4vw;
    display: flex;
    flex-wrap: wrap;
    min-width: 25vw;
}

.content::before {
    content: "";
    position: absolute;
    top: 25px;
    left: -40px;
    height: 4px;
    width: 10px;
    background: black;
}

.stats {
    font-size: 1.8vw;
    color: gray;
}

.type {
    left: 3vw;
}

input[type="range"]::-moz-range-thumb {
    height: 2rem;
    width: 2.5rem;
    background: url("/slider.svg");
    border: 0;
    appearance: none;
    -moz-appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
}

input[type="range"]::-moz-range-progress {
    background: #F1CB42;
}

.sliderMobile {
    position: absolute;
    top: -1rem;
    background-color: transparent !important;
    width: 100%;
}

.mobileModal {
    transform: unset !important;
    width: 100%;
    display: flex;
    justify-content: center;
    position: fixed;
    top: unset !important;
    bottom: 35vh !important;
}

.mobileModal > div > div {
    min-width: 90vw !important;
}

.bottomNavigation {
    background: transparent !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
}

.glassBackdrop {
    background: rgba(255, 255, 255, 0.05) !important;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
    backdrop-filter: blur(8px) !important;
    -webkit-backdrop-filter: blur(8px) !important;
}

.textBrown {
    color: #7F4F2A !important;
}

.heroImage {
    overflow: hidden;
    max-height: 60rem;
}

body {
    background: #E1E5ED;
}


.ctaBtn {
    background: #657492;
    padding: 0.3rem 3rem;
    border-radius: 4rem;
    color: white;
    font-size: 20px;
    cursor: pointer;
    border: 1px solid transparent;
}

.ctaBtn:hover {
    color: black;
    border: solid 1px #333;
    background: #D5E7AB;
}


.roundedImg {
    border-radius: 36px;
}

.formInput {
    border: none;
    border-radius: 5px;
    margin-bottom: 1rem;
    margin-top: 0.3rem;
}

.formContact {
    color: #657492;
    font-size: 16px;
    font-weight: lighter;
}

.Canvas, .CanvasApp {
    height: 100vh;
}

.Canvas canvas {
    height: 100vh !important;
}

.controlsHints {
    background-color: rgba(255, 255, 255, 0.37);
}

.FloorModal > div {
    max-width: 80vw !important;
    position: unset !important;
    padding-top: 5rem;
}

.FloorModal > div > div {
    all: unset;
    box-shadow: unset;
    background-color: rgba(189, 117, 78, 0.5);
}

.FloorModal {
    background-color: transparent;
    z-index: 0 !important;
    box-shadow: unset !important;
}

.svgcko {
    position: absolute;
    width: 100%;
    top: 0;
    height: 100%;
}

.redWrapper {
    padding: 8rem;
    background: rgba(189, 117, 78, 0.5);
    border-radius: 50%;
    position: absolute;
    left: -7rem;
}

.toto {
    padding: .5rem 1rem !important;
}

.SOS > ul {
    display: flex;
    align-items: center;
}

.ql-editor {
    background-color: white;
    min-height: 10rem;
}

.navH {
    font-size: 2.3vw;
}

.links a {
    background-color: white;
    color: black;
    padding: .3rem 1.5rem;
    border-radius: 15px;
    cursor: pointer;
    transition: .3s;
    font-size: 1vw;
    margin-bottom: .5rem;
    border: solid 1px rgba(148, 149, 151, 0.55) !important;
    max-height: 2.2rem;
    display: flex;
    align-items: center;
}

.links a:hover {
    background-color: #657492;
    color: white;
}

.linksC a {
    background-color: transparent;
    color: white;
    padding: .3rem 1.5rem;
    border-radius: 13px;
    border: solid 1px white !important;
    cursor: pointer;
    transition: .3s;
    font-size: 1vw;
    margin-bottom: .5rem;
    font-weight: bold;
    max-height: 2.2rem;
    display: flex;
    align-items: center;
}

.linksC a:hover {
    background-color: #657492;
    color: white;
}

.dropdown {
    display: none;
    position: absolute;
}

.dropdownWrapper:hover {
    max-height: 100%;
}

.foterLogo > img {
    height: 5rem;
}

.dropdownWrapper:hover .dropdown {
    display: flex;
}

.trid-vyber {
    background-color: #D5E7AB !important;
}

.trid-vyber:hover {
    background-color: #b7da61 !important;
}

.circleLogo {
    width: 17vw;
    height: 17vw;
    border-radius: 50%;
    left: -4.5vw;
    top: -5vw;
}

.logoIMAGE {
    margin: 4vw 0 0 4vw;
    width: 9.5vw;
}

.logoH {
    font-size: 1vw;
    padding: .5vw 1vw 1vw 3.2vw;
    color: #657492;
}

.Framik {
    bottom: -6.5vw;
}

.klikatelneInfo {
    border-radius: 0 0 20px 20px;
}

.mobileInfoBottom {
    font-size: 3vw;
}

.text-biely {
    color: white !important;
}

.kokotskyButton {
    background-color: black;
    color: white;
    border-radius: 50%;
    transition: .3s;
    cursor: pointer;
    border: solid 1px transparent;
    text-align: center;
}

.buttonPad {
    padding: 1vw;
}

#\:R76m\: {
    outline: none !important;
}

.kokotskyButton h3 {
    font-size: 1.7vw;
    font-weight: normal;
}

.kokotskyButton small {
    font-size: 1.1vw;
    text-align: center;
    width: 100%;
}

.kokotskyButton:hover {
    background-color: #D5E7AB;
    color: black;
    border: solid 1px black;
}

.pillC {
    box-shadow: 5px 5px 0px 0px #B6BFD0;
    -webkit-box-shadow: 5px 5px 0px 0px #B6BFD0;
    -moz-box-shadow: 5px 5px 0px 0px #B6BFD0;
    background-color: #657492;
    color: white;
    border-radius: 50%;
    padding: .5rem;
    width: 1vw;
}

.buttonLinkActive {
    background-color: #B4CC7A;
    color: white;
    border-radius: 15px;
    cursor: pointer;
    transition: .3s;
}

.buttonLink {
    background-color: #657492;
    color: white;
    border-radius: 15px;
    cursor: pointer;
    transition: .3s;
}

.buttonLink:hover {
    background-color: #D5E7AB;
    color: black;
}

.svgOverlay rect, path {
    cursor: pointer;
    transition: .1s;
    border-radius: 10rem !important;
}


.svgOverlay rect:hover {
    fill: #657492;
    opacity: .5 !important;
}

.svgOverlay path:hover {
    fill: #657492;
    opacity: .5 !important;
}

#tooltipikLokalita {
    width: fit-content;
}

.buttonBottom {
    background: linear-gradient(91deg, #FB2DFF 3.06%, #83C3FF 64.79%, #0075FF 105.1%);
    border: solid 1px transparent;
    transition: .3s;
}

.buttonBottom:hover {
    color: black;
    border: solid 1px black;
    background: linear-gradient(91deg, #D5E7AB 3.06%, #D5E7AB 64.79%, #D5E7AB 105.1%);
}

.contactButton {
    transition: .2s;
    cursor: pointer;
}

.contactButton:hover {
    background: linear-gradient(91deg, #FB2DFF 3.06%, #83C3FF 64.79%, #0075FF 105.1%);
}

.bytButton {
    border-radius: 50px;
    background: linear-gradient(129deg, #FB2DFF 37.68%, #0075FF 113.68%);
}

.switcher {
    border-radius: 5px !important;
}


.switcher > div {
    background-color: #0085FF !important;
    border: 1px solid #333 !important;
}

.noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.selectOur {
    all: unset;
    border: 1px solid #B6BFD0;
    background-color: white;
    width: 10rem;
    padding-top: .3rem;
    padding-bottom: .3rem;
    padding-left: 2.5rem;
    border-radius: 50px;
    text-align: center;
}

.appNavigationHeading {
    font-size: 2.5vw;
}

.sipocka {
    width: 4.35vw !important;
    height: 2.39vw;
}

.neduh > div::after {
    margin-top: .5rem;
    margin-left: 1rem;
}

.plochaP {
    cursor: pointer;
}

#tooltipikUP {
    width: 50%;
}

#tooltipikUP > .relative {
    width: 90%;
}

@media (max-width: 767px) {
    .burgerMenu {
        width: 3rem !important;
    }

    .mobileMenu {
        top: 5.5rem;
    }

    .bottomNavigation {
        background: rgba(255, 255, 255, 0.05) !important;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1) !important;
        backdrop-filter: blur(8px) !important;
        -webkit-backdrop-filter: blur(8px) !important;
    }

    .links a {
        background-color: white;
        color: black;
        padding: 1.3rem 1rem;
        border-radius: 10px;
        cursor: pointer;
        transition: .3s;
        font-size: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: solid 1px rgba(148, 149, 151, 0.55) !important;
    }

    .kokotskyButton h3 {
        font-size: 5.7vw;
        font-weight: normal;
    }

    .kokotskyButton small {
        font-size: 1.1rem;
    }

    .buttonPad {
        padding: 5vw;
    }

    .selectOur {
        width: unset;
    }

    .FloorModal > div {
        max-width: 100vw !important;
        position: relative !important;
        background-color: white !important;
        padding-top: 1rem !important;
        z-index: 99
    }

    .FloorModal > div {
        height: unset !important;
    }

    .FloorModal {
        position: fixed !important;
        z-index: 80 !important;
    }
}

.lokalitka .st0 {
    fill: none;
}

.lokalitka .st1 {
    fill: #683815;
}

.lokalitka .st2 {
    fill: #AEBC8E;
}

.lokalitka .st3 {
    fill: #FFFFFF;
}

.lokalitka .st3Active {
    fill: #000000;
    transform: scaleY(0.99);
}

.lokalitka .st4 {
    fill: #97D7DF;
}

.lokalitka .st5 {
    fill: #B4CC79;
}

.lokalitka .st6 {
    fill: #F27BA9;
}

.lokalitka .st7 {
    fill: #8D9E63;
}

.lokalitka .st8 {
    fill: #FAAA4A;
}

.lokalitka .st9 {
    fill: #A57C52;
}

.lokalitka .st10 {
    fill: #4169B2;
    transition: .3s;
    border: solid 1px #4169B2;
}

.lokalitka .st10Active {
    fill: #ffffff;
    border: solid 1px #4169B2;
    transform: scaleY(0.99);
    transition: .3s;
}

.lokalitka .st11 {
    fill: none;
    stroke: #FFFFFF;
    stroke-width: 0.525;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.lokalitka .st11Active {
    fill: none;
    stroke: #4169B2;
    stroke-width: 0.525;
    stroke-linecap: round;
    transform: scaleY(0.99);
    stroke-linejoin: round;
}

.lokalitka .st12 {
    fill: none;
    stroke: #FFFFFF;
    stroke-width: 0.7;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.lokalitka .st12Active {
    fill: none;
    stroke: #4169B2;
    stroke-width: 0.7;
    stroke-linecap: round;
    transform: scaleY(0.99);
    stroke-linejoin: round;
}

.lokalitka .st13 {
    fill: #D7B528;
}

.Pin {
    transform: scaleY(0.99);
}

.lokalitka .st14 {
    fill: #C49B6D;
}

.MODALIK > div {
    display: flex;
    align-items: center;
}
