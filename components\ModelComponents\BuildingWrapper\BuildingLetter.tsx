//@ts-nocheck
import {Billboard, Text, useCursor} from "@react-three/drei";
import * as React from "react";
import {Vector3} from "@react-three/fiber";

interface LetterProps {
    active: boolean,
    hovered: boolean,
    letter: string,
    position: Vector3,
    isSpaceEnabled: boolean
}

export default function Letter(props: LetterProps) {
    useCursor(props.hovered)
    return (<Billboard {...props} follow={true} lockX={false} lockY={false} lockZ={false}>
        <Text color={props.isSpaceEnabled ? "yellow" : props.active ? "green" : props.hovered ? "orange" : "black"}
              outlineWidth={props.isSpaceEnabled ? 0.2 : 0} fontSize={3}>{props.letter}</Text>
    </Billboard>)
}