import Link from "next/link";
import {useEffect, useState} from "react";
import {useRouter} from "next/router";
import {useParams} from "next/navigation";

export default function ContactBar() {
    const [is3D, setIs3D] = useState(false)
    const router = useRouter();
    const params = useParams()

    const getContactURI = (): string => {
        if (params?.izba && params?.byt) {
            return "/napiste-nam?byt=" + params.byt + "&rooms=" + params.izba[0]
        }
        if (params?.parking !== undefined) {
            return "/napiste-nam?parking=" + params.parking
        }
        return "/napiste-nam"
    }


    useEffect(() => {
        if (router.pathname === "/3d-vyber" || router.pathname === "/napiste-nam" || router.pathname.includes("admin")) {
            setIs3D(true)
        } else {
            setIs3D(false)
        }
    }, [router])

    if (is3D) {
        return null;
    }

    return (
        <div
            className="flex flex-col md:flex-row bottomNavigation fixed bottom-0 justify-between p-[2vw] md:p-[1vw] z-20 w-full gap-[1vw] md:gap-8 text-center">
            <section className="flex flex-row md:flex-col gap-2 items-start">
                <Link href={"tel:+421917997120"} className={"w-full md:w-auto"}>
                    <button
                        className="bg-black w-full md:w-auto contactButton text-white px-[4vw] md:px-[1vw] py-[.7vw] md:py-[.3vw] rounded-full">
                        <h4 className="text-[3vw] md:text-[1vw] font-medium">+421 917 997 120</h4>
                    </button>
                </Link>
                <Link href={getContactURI()} className={"w-full"}>
                    <button
                        className="bg-black w-full contactButton text-white rounded-full px-[4vw] md:px-[1vw] py-[.7vw] md:py-[.3vw]">
                        <h4 className="text-[3vw] md:text-[1vw] font-medium"><EMAIL></h4>
                    </button>
                </Link>
            </section>
            <section className={"flex items-end"}>
                <Link href={getContactURI()} className={"w-full md:w-auto"}>
                    <button type="button"
                            className="text-white w-full md:w-auto rounded-full buttonBottom focus:ring-4 focus:outline-none focus:ring-cyan-300 dark:focus:ring-cyan-800 font-medium text-[4vw] md:text-[1.7vw] px-[1vw] py-[0.4vw] text-center">
                        mám záujem
                    </button>
                </Link>
            </section>
        </div>
    )
};