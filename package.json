{"name": "lauri<PERSON><PERSON>", "version": "1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/eslint-parser": "^7.27.1", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@react-three/gltfjsx": "^4.3.4", "@types/react": "19.0.4", "camera-controls": "^2.10.1", "flowbite": "^2.3.0", "flowbite-react": "^0.7.3", "framer-motion": "^11.16.3", "mysql2": "3.14.1", "next": "^15.3.2", "nodemailer": "^7.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-gtm-module": "^2.0.11", "react-hot-toast": "^2.5.2", "sass": "^1.83.1", "serverless-mysql": "^2.1.0", "three": "^0.176.0"}, "devDependencies": {"@types/mysql": "^2.15.25", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "autoprefixer": "^10.4.19", "eslint": "^8.56.0", "eslint-config-next": "15.1.4", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "5.4.5"}, "overrides": {"@types/react": "19.0.4"}}