import Image from "next/image";
import dynamic from "next/dynamic";
import Link from "next/link";
import Parkovacka from "./parkovacka";
import ResponsiveImgMap from "../ponuka-bytov/izba/responsiveImgMap";

const ParkingPick = dynamic(() => import('./parkingPick'));

export default function DesktopLayoutParking({place, isMobile, getter, selectedFlat}) {
    if (place === undefined) {
        return null;
    }
    let placeP = JSON.parse(place);

    return (
        <>
            <section
                className={placeP.section === "V2" ? "flex items-center mx-32 p-10 justify-center mt-36 mb-20" : "flex items-center mx-32 p-10 justify-center mt-20 mb-20"}>
                <div className={"podorys flex justify-center w-1/2"}>
                    <Image
                        src={placeP.id >= 136 ? "/parkovacie-miesta/podorysy/136.png" : "/parkovacie-miesta/podorysy/" + placeP.parking_number + ".png"}
                        alt={"pôdorys parkovacieho miesta"}
                        width={placeP.section === "V" || placeP.section === "V2" ? 700 : 300}
                        className={placeP.section === "V" ? "rotate-90 p-20" : placeP.section === "V2" ? "rotate-90" : "p-8"}
                        height={50}/>
                </div>
                <div className={"info py-8 px-14 w-1/2 bg-white shadow-xl relative rounded-3xl"}>
                    <Link href={"/parkovacie-miesta"}
                          className={"bg-black p-2 absolute -top-3 hover:bg-gray-400 cursor-pointer hover:shadow-lg transition-all text-white -left-3 rounded-full"}>
                        <svg className="w-6 h-6" aria-hidden="true"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2"
                                  d="M5 12h14M5 12l4-4m-4 4 4 4"/>
                        </svg>
                    </Link>
                    <div className={"flex justify-between pt-1 gap-[3.5vw]"}>
                        <small
                            className="uppercase text-[0.8vw] bg-[#B4CC7A] px-[.8vw] flex  items-center text-white rounded-2xl">{placeP.section === "V" ? "Vonkajšie parkovanie" : placeP.section === "V2" ? "Vonkajšie kryté" : placeP.section === "VS" ? "Skladové priestory" : "Podzemné parkovanie"}</small>
                        <div className={"bg-black text-white rounded-2xl px-[1vw] flex items-center gap-2"}>
                            <h2
                                className={"text-[0.8vw] mt-1 flex font-light uppercase"}>parkovacie miesto číslo: <span
                                className={"text-[1.5vw] font-extralight"}><strong>{placeP?.parking_number}</strong></span>
                            </h2>
                        </div>
                    </div>
                    <div className={"flex flex-col"}>
                        {isMobile ? <ResponsiveImgMap getter={getter} predmetZaujmu={"parking"}
                                                      room={undefined}
                                                      selectedBuilding={selectedFlat} isMobile={isMobile}
                                                      isFlatDetail={false} numberOfFlats={undefined}/> :
                            <Parkovacka isFlatDetail={true} flatSelected={placeP.section} getter={getter}/>
                        }

                    </div>
                    <div
                        className={placeP.section === "V2" ? "podorysY -mt-32 -mb-24 items-start justify-center flex" : "podorysY items-start justify-center flex"}>
                        <ParkingPick isMobile={false} isDetail={true} section={placeP.section} place={placeP.id}/>
                    </div>
                    <div className={"flex text-[#657492] mt-[0.5vw] justify-between gap-4"}>
                        <div className={"compass flex items-center w-[6vw]"}>
                            <Image src={"/azimut_BCDEF.svg"} alt={"azimut"}
                                   className={placeP.section === "V2" ? "rotate-[92deg]" : ""} width={100}
                                   height={100}/>
                        </div>
                        <div className={"flex mt-4 gap-4"}>
                            <div className={"flex flex-col justify-between"}>
                                <div>
                                    <small className="uppercase">PLOCHA PARKOVANIA</small>
                                    <p className={"text-[1.7vw]"}>{placeP["plocha_parkovania"]}m²</p>
                                </div>
                                <div>
                                    <small className="uppercase">PLOCHA SKLADU</small>
                                    <p className="text-[1.7vw]">{placeP["plocha_skladu"]}m²</p>
                                </div>
                            </div>
                            <div className={"flex flex-col gap-1 justify-between items-end"}>

                                <div>
                                    <small className="uppercase">CELKOVÁ VÝMERA</small>

                                    <p className={"text-[1.7vw] font-bold"}>{placeP["plocha_skladu"] + placeP["plocha_parkovania"]}m²</p>
                                </div>
                                <div className={"text-right"}>
                                    <small className="uppercase text-right">cena s dph</small>
                                    <p className={"text-[1.7vw] -mr-4 bg-[#657492] text-[#E1E5ED] px-5 rounded-full font-bold"}>{placeP["price"] === 0 ? "Predrezrvácia" : placeP["price"].toLocaleString().replace(/,/g, ' ') + "EUR"} </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}