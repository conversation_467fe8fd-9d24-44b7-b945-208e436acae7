import {useEffect, useRef, useState} from "react";
import {<PERSON><PERSON>, Spin<PERSON>, Toolt<PERSON>} from 'flowbite-react';
import Link from "next/link";
import TableDesktop from "../../ponuka-bytov/desktopFilter";
import TableMobile from "../../ponuka-bytov/mobileFilter";

export default function FlatPick(props: {
    floor: number;
    building: string,
    isFlatDetail: boolean,
    flatSelectedInPick?: string[],
    flatSelectedInDetail?: string,
    isEasyChoose?: boolean | false,
    isDetailedFlow?: boolean,
    selectedFlatsInFlow?: any
    is3D?: boolean,
    isMobile?: boolean,
    room?: any
}) {
    const imageWidth = 1423;
    const imageHeight = 666;
    const [data, setData] = useState([]);
    const [hoveredFlat, setHoveredFlat] = useState()
    const [hovered, setHovered] = useState(false)
    const [imageLoaded, setImageLoaded] = useState(false)
    const [imageData, setImageData] = useState([])
    const [selectedFlats, setSelectedFlats] = useState([])

    function isInFirstArray(obj) {
        return props.flatSelectedInPick.some(item => item["id"] === obj["id"]);
    }

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch('/api/hello?floor=' + props.floor + "&building=" + props.building);
                const result = await response.json();
                if (!props.isFlatDetail) {
                    const filteredArray = result.data.filter(obj => !isInFirstArray(obj));
                    setData(filteredArray)
                    setSelectedFlats(props.flatSelectedInPick)
                } else {
                    setData(result.data)
                }

            } catch (error) {
                console.error('Error fetching data:', error);
            }
        };
        fetchData()
    }, [props]);

    let actualImage = useRef(null)

    const easyChooseLShapeCalculationWidth = (flat: any): number => {
        if (props.isEasyChoose || props.isFlatDetail) {
            // @ts-ignore
            return flat.width / imageWidth * actualImage.current?.clientWidth
        }
        // @ts-ignore
        return flat.width / imageWidth * actualImage.current?.clientWidth
    }
    const easyChooseLShapeCalculationHeight = (flat: any): number => {
        if (props.isEasyChoose || props.isFlatDetail) {
            // @ts-ignore
            return flat.height / imageHeight * actualImage.current?.clientHeight
        }
        // @ts-ignore
        return flat.height / imageHeight * actualImage.current?.clientHeight
    }
    const easyChooseLShapeCalculationXAxis = (flat: any): number => {
        if (props.isEasyChoose || props.isFlatDetail) {
            // @ts-ignore
            return flat.x / imageWidth * actualImage.current?.clientWidth
        }
        // @ts-ignore
        return flat.x / imageWidth * actualImage.current?.clientWidth
    }
    const easyChooseLShapeCalculationYAxis = (flat: any): number => {
        if (props.isEasyChoose || props.isFlatDetail) {
            // @ts-ignore
            return flat.y / imageWidth * actualImage.current?.clientHeight
        }
        // @ts-ignore
        return flat.y / imageHeight * actualImage.current?.clientHeight / 2
    }

    const handleMouseEnter = (e) => {
        setHoveredFlat(e.target.id)
        setHovered(true)
        document.getElementById("tooltipik").style.display = "block";
    };

    const handleMouseLeave = () => {
        setHovered(false);
        document.getElementById("tooltipik").style.display = "none";
    }

    const setMousePosition = (e: { clientX: number; clientY: number; }) => {
        let leftOffset: number;
        if (props.isFlatDetail) {
            leftOffset = ((e.clientX / 10) / 2.5) * 2;
        } else {
            if (((e.clientX / 10) / 2.5) < 42) {
                leftOffset = ((e.clientX / 10) / 2.5) * 2;
            } else {
                leftOffset = ((e.clientX / 10) / 2.5) / 2;
            }
        }

        document.getElementById("tooltipik").style.left = leftOffset + "%";
        document.getElementById("tooltipik").style.top = e.clientY / 10 + "%";
    }

    useEffect(() => {
        setImageLoaded(true)
        setImageLoaded(true);
        if (actualImage.current !== undefined) {
            setImageData([
                actualImage.current.clientWidth ?? "0",
                actualImage.current.clientHeight ?? "0"
            ]);
        }
    }, [actualImage]);

    return (
        <>
            <div onMouseMove={setMousePosition}
                 className={props.is3D ? "overflow-visible flex justify-center relative" : "overflow-visible relative"}>
                <Tooltip id={"tooltipik"} animation="duration-500" trigger="hover" arrow={false}
                         className="shadow-lg max-w-xs hidden overflow-visible"
                         content={<FlatTooltip flat={hoveredFlat}/>}>
                    <img
                        id={"floorImage"} ref={actualImage}
                        src={"/podlazia/" + props.building + "-" + props.floor + "-FLOOR.svg"}
                        alt="Responsive Image"
                        useMap="#image_map" onLoad={() => setImageLoaded(true)}
                        width={props.isFlatDetail ? imageWidth / 3 : props.isDetailedFlow || props.is3D ? imageWidth / 2 : imageWidth}
                        height={props.isFlatDetail ? imageHeight / 3 : props.isDetailedFlow || props.is3D ? imageHeight / 2 : imageHeight}/>
                    {imageLoaded ?
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="svgOverlay overflow-visible"
                            style={{
                                position: "absolute",
                                width: props.is3D ? "unset" : "100%",
                                top: 0,
                                display: "block",
                                height: "100%"
                            }}
                            viewBox={"0 0 " + imageData[0] + " " + imageData[1]}
                        >
                            {data !== undefined ? data.map((flat, index) => <>
                                    <Link id={"" + index} style={{
                                        pointerEvents: (props.isDetailedFlow) || flat.dostupnost === 0 ? "none" : "auto",
                                    }}
                                          href={flat.dostupnost === 0 ? "/ponuka-bytov" : `/ponuka-bytov/${flat.rooms}-izbove/${flat.id}`}>
                                        {flat.isL && !flat.isVariant ?
                                            <g aria-kokot={selectedFlats}
                                               transform={"translate(" + (easyChooseLShapeCalculationXAxis(flat)).toFixed() + "," + (easyChooseLShapeCalculationYAxis(flat)).toFixed() + ")"}>
                                                <path onMouseEnter={handleMouseEnter}
                                                      onMouseLeave={(e) => {
                                                          handleMouseLeave();
                                                      }} style={{
                                                    opacity: .8
                                                }} id={JSON.stringify(flat)}
                                                      d={!flat.isLast ? "M0 0H" + (easyChooseLShapeCalculationWidth(flat)).toFixed() + "V" + (easyChooseLShapeCalculationHeight(flat)).toFixed() + "H" + (((flat.width / imageWidth) * (document.getElementById("floorImage").clientWidth)) / (props.isFlatDetail ? 7 : 2.1)).toFixed() + "V" + ((easyChooseLShapeCalculationHeight(flat)) / 3.6).toFixed() + "H0" : "M181 0H0V" + (easyChooseLShapeCalculationHeight(flat)).toFixed() + "H" + ((easyChooseLShapeCalculationWidth(flat)) / 1.6).toFixed() + "V" + ((easyChooseLShapeCalculationHeight(flat)) / 3.3).toFixed() + "H" + (easyChooseLShapeCalculationWidth(flat)).toFixed() + "V0Z"}
                                                      fill={props.flatSelectedInDetail === flat.flatNumber ? "black" : flat.dostupnost === 1 && props.isFlatDetail || props.is3D ? "#B4CC7A" : flat.dostupnost === 1 && !props.isFlatDetail ? "black" : flat.dostupnost === 2 && !props.isFlatDetail ? "black" : flat.dostupnost === 2 && props.isFlatDetail ? "#FFAB48" : flat.dostupnost === 0 && !props.isFlatDetail ? "black" : flat.dostupnost === 0 && props.isFlatDetail && "#FF0000"}/>
                                            </g> :
                                            !flat.isVariant && <rect
                                                onMouseEnter={handleMouseEnter}
                                                onMouseLeave={() => {
                                                    handleMouseLeave();
                                                }}
                                                id={JSON.stringify(flat)}
                                                x={props.isFlatDetail ? (flat.x / imageWidth * actualImage.current?.clientWidth) / 1.15 : (flat.x / imageWidth * actualImage.current?.clientWidth) / 1.15}
                                                y={props.isFlatDetail ? (flat.y / imageHeight * actualImage.current?.clientHeight) * 1.80 : (flat.y / imageHeight * actualImage.current?.clientHeight) * 1.8}
                                                fill={props.flatSelectedInDetail === flat.flatNumber ? "black" : flat.dostupnost === 1 && props.isFlatDetail || props.is3D ? "#B4CC7A" : flat.dostupnost === 1 && !props.isFlatDetail ? "black" : flat.dostupnost === 2 && !props.isFlatDetail ? "black" : flat.dostupnost === 2 && props.isFlatDetail ? "#FFAB48" : flat.dostupnost === 0 && !props.isFlatDetail ? "black" : flat.dostupnost === 0 && props.isFlatDetail && "#FF0000"}
                                                style={{
                                                    height: (flat.height / imageHeight * actualImage.current?.clientHeight) + "rem",
                                                    width: (flat.width / imageWidth * actualImage.current?.clientWidth) + "rem",
                                                    opacity: .8
                                                }}/>}
                                    </Link>
                                </>
                            ) : <></>}
                            {selectedFlats !== undefined ? selectedFlats.map((flat, index) => <>
                                    <Link id={"" + index} aria-kokot={props.room} style={{
                                        pointerEvents: (props.isDetailedFlow) || flat.dostupnost === 0 ? "none" : "auto",
                                    }}
                                          href={flat.dostupnost === 0 ? "/ponuka-bytov" : `/ponuka-bytov/${flat.rooms}-izbove/${flat.id}`}>
                                        {flat.isL ?
                                            <g transform={"translate(" + (easyChooseLShapeCalculationXAxis(flat)).toFixed() + "," + (easyChooseLShapeCalculationYAxis(flat)).toFixed() + ")"}>
                                                <path onMouseEnter={handleMouseEnter}
                                                      onMouseLeave={(e) => {
                                                          handleMouseLeave();
                                                      }} style={{
                                                    opacity: .8
                                                }} id={JSON.stringify(flat)}
                                                      d={!flat.isLast ? "M0 0H" + (easyChooseLShapeCalculationWidth(flat)).toFixed() + "V" + (easyChooseLShapeCalculationHeight(flat)).toFixed() + "H" + (((flat.width / imageWidth) * (document.getElementById("floorImage").clientWidth)) / 2.8).toFixed() + "V" + ((easyChooseLShapeCalculationHeight(flat)) / 3.55).toFixed() + "H0" : "M181 0H0V" + (easyChooseLShapeCalculationHeight(flat)).toFixed() + "H" + ((easyChooseLShapeCalculationWidth(flat)) / 1.6).toFixed() + "V" + ((easyChooseLShapeCalculationHeight(flat)) / 3.3).toFixed() + "H" + (easyChooseLShapeCalculationWidth(flat)).toFixed() + "V0Z"}
                                                      fill={selectedFlats[index]["flatNumber"] === flat.flatNumber ? "#B4CC7A" : flat.dostupnost === 1 ? "black" : flat.dostupnost === 2 ? "black" : "black"}/>
                                            </g>
                                            : <rect
                                                onMouseEnter={handleMouseEnter}
                                                onMouseLeave={() => {
                                                    handleMouseLeave();
                                                }}
                                                id={JSON.stringify(flat)}
                                                x={props.isFlatDetail ? (flat.x / imageWidth * actualImage.current?.clientWidth) / 1.15 : (flat.x / imageWidth * actualImage.current?.clientWidth) / 1.15}
                                                y={props.isFlatDetail ? (flat.y / imageHeight * actualImage.current?.clientHeight) * 1.80 : (flat.y / imageHeight * actualImage.current?.clientHeight) * 1.8}
                                                fill={selectedFlats[index]["flatNumber"] === flat.flatNumber ? "#B4CC7A" : flat.dostupnost === 1 && !props.isFlatDetail ? "black" : flat.dostupnost === 1 && props.isFlatDetail ? "#B4CC7A" : flat.dostupnost === 2 && !props.isFlatDetail ? "#FFAB48" : "blue"}
                                                style={{
                                                    height: (flat.height / imageHeight * actualImage.current?.clientHeight) + "rem",
                                                    width: (flat.width / imageWidth * actualImage.current?.clientWidth) + "rem",
                                                    opacity: .8
                                                }}/>}
                                    </Link>
                                </>
                            ) : <></>}
                        </svg> : <Spinner/>}
                </Tooltip>
            </div>
            {props.is3D && !props.isMobile &&
                <TableDesktop filteredFlats={data} filteredFlatsCount={data.length} handleChangeToSubmit={undefined}
                              is3D={true} room={props.room}
                              orderingVymera={undefined}
                              orderingCena={undefined}/>}
            {props.is3D && props.isMobile &&
                <TableMobile filteredFlats={data}/>
            }
        </>
    )

}

const FlatTooltip = (props) => {
    let flat: object;
    if (props.flat != undefined) {
        flat = JSON.parse(props.flat);
    } else {
        return null;
    }
    return (
        <div className="max-w-sm">
            <div className={"flex gap-10 px-2 justify-between"}>
                <div className={"flatNo w-full px-2"}>
                    <small>Číslo bytu:</small>
                    <h2 className="text-2xl"><strong>{flat["flatNumber"]}</strong></h2>
                </div>
                <div className={"flatRooms w-full"}>
                    <small>Počet izieb:</small>
                    <h2 className="text-2xl">{flat["rooms"]}</h2>
                </div>
            </div>
            <hr className="my-2"/>
            <div className={"flex items-center gap-1 justify-between"}>
                <small>Dostupnosť: </small>
                <Badge color="success">{flat["dostupnost"] === 1 ? <span>Dostupný</span> :
                    <span>Predaný</span>}</Badge>
            </div>
            <hr className="my-2"/>
            <div className={"flex items-center gap-1 justify-between"}>
                <small className={"align-self-left"}>Výmera: </small>
                <div className={"text-center"}>
                    <h2 className="text-xl">{flat["vymera"]}m²</h2>
                </div>
            </div>
            <hr className="my-2"/>
            <div className={"flex items-center gap-1  justify-between"}>
                <small className={"align-self-left"}>Celková cena: </small>
                <div className={"text-center"}>
                    <h2 className="text-xl">{flat["price"].toString().replace(/.{3}/g, '$& ')}€</h2>
                </div>
            </div>
        </div>
    )
}
