import Image from "next/image";
import FlatPick from "../../ModelComponents/BuildingWrapper/FlatPick";
import {<PERSON>ouse<PERSON>, Mo<PERSON>, Spinner, Toolt<PERSON>} from "flowbite-react";
import Link from "next/link";
import ResponsiveImgMap from "./responsiveImgMap";
import FloorChoose from "./floorChoose";
import {Suspense, useState} from "react";

export default function DesktopLayoutPodorys({
                                                 flat,
                                                 flatName,
                                                 gallery,
                                                 room,
                                                 getSelectedFlat,
                                                 getSelectedFloor,
                                                 selectedFlat
                                             }) {
    const [openModal, setOpenModal] = useState(false);
    const [hovered, setHovered] = useState(false)

    if (flat === undefined) {
        return null;
    }

    return (
        <>
            <section
                className={"flex items-center px-[5vw] gap-20 justify-between overflow-x-hidden bg-[#e1e5ed] pt-48 mb-10"}>
                <Suspense fallback={<Spinner/>}>
                    <div
                        className={"podorys relative min-h-[70vh] flex mx-20 justify-center cursor-pointer items-center w-1/2"}
                        onClick={() => setOpenModal(true)} onMouseEnter={() => setHovered(true)}
                        onMouseLeave={() => setHovered(false)} style={{
                        backgroundImage: "url('/byty/" + flat.flatNumber.replace(/\./g, '') + ".png')",
                        backgroundSize: "contain",
                        backgroundRepeat: "no-repeat",
                        backgroundPosition: "center"
                    }}>
                        {hovered && <div
                            className={"absolute flex w-full h-full transition rounded-2xl m-10 items-center justify-center bg-opacity-30 bg-gray-500"}>
                            <div className={"bg-black text-white rounded-full px-10 mr-10 flex items-center gap-2"}>
                                <span className={"text-md font-light"}>NA CELÚ OBRAZOVKU</span>
                            </div>
                        </div>}
                    </div>
                </Suspense>
                <div className={"info max-w-[40vw] px-10 w-1/2 bg-white shadow-xl relative rounded-3xl"}>
                    <div className={"flex items-center justify-between gap-4 pt-6"}>
                        <div className={"bg-black text-white rounded-full px-[1.2vw] flex items-center gap-2"}>
                            <h2 className={"text-[0.8vw] mt-1 font-light"}>BYT ČÍSLO: <span
                                className={"text-[1.5vw] font-extralight"}><strong>{flat?.flatNumber}</strong></span>
                            </h2>
                        </div>
                        <div
                            className={"headingPrice flex gap-1 items-center py-[0.1vw] bg-[#24262A] text-white px-[1.4vw] rounded-full"}>
                            <small className="font-light text-[0.8vw]">CENA</small>
                            <p className={"text-[1.5vw]"}>{flat["price"].toString().replace(/.{3}/g, '$& ')}€</p>
                        </div>
                    </div>
                    <div className={"mt-[1vw] mb-[1vw]"}>
                        <small className="uppercase text-[0.8vw]">SITUÁCIA</small>
                        <div className={"flex justify-center"}>
                            <ResponsiveImgMap isFlatDetail={true} numberOfFlats={[]} isMobile={false}
                                              getter={getSelectedFlat}
                                              room={room} selectedBuilding={selectedFlat} predmetZaujmu={"izba"}/>
                        </div>
                    </div>
                    <div className={"podorysY items-start gap-16 flex"}>
                        <div className="text-center flex flex-col gap-4 relative">
                            <div>
                                <small className="uppercase text-[0.7vw]">Pôdorys podlažia</small>
                                <p className="text-[1.4vw]">{flat["poschodie"]}.NP</p>
                            </div>
                            <FlatPick floor={flat["poschodie"]} is3D={false}
                                      flatSelectedInDetail={flat["flatNumber"]} room={room}
                                      isFlatDetail={true} isDetailedFlow={false} building={flat["budova"]}/>
                        </div>
                        <div className={"flex items-center flex-col px-12 justify-center text-center"}>
                            <div>
                                <small className="uppercase text-[0.7vw]">POSCHODIE</small>
                                <p className={"text-[1.4vw]"}>{flat["poschodie"] === 1 ? "Prízemie" : flat["poschodie"] + ". poschodie"}</p>
                            </div>
                            <div>
                                <FloorChoose getter={getSelectedFloor} isMobile={false} room={room} isFlatDetail={true}
                                             numberOfFlatsFloor={[]}
                                             flatSelected={flat}/>
                            </div>
                        </div>
                    </div>
                    <div className={"flex items-center gap-10"}>
                        <div className={"compass w-1/4"}>
                            {flat["budova"] === "A" ?
                                <Image src={"/azimut_A.svg"} alt={"azimut"} width={100} height={100}/> :
                                <Image src={"/azimut_BCDEF.svg"} alt={"azimut"} width={100} height={100}/>}
                        </div>
                        <div className={"flex flex-col"}>
                            <div className="">
                                <small className="uppercase text-[0.7vw]">CELKOVÁ PLOCHA INTERIÉRU</small>
                                <p className={"mb-[1vw] text-[1.4vw]"}>{flat["plocha_interier"]}m²</p>
                            </div>
                            <div className="relative">
                                <small className="uppercase text-[0.7vw]">POČET IZIEB</small>
                                <div className={"flex gap-1"}>
                                    <p className="text-[1.4vw]">{flat["rooms"]}</p>
                                    {(flat["rooms"] > room) &&
                                        <span className={"text-[1.4vw]"}>/{flat["rooms"] > room ? flat["canRooms"] + 1 : flat["canRooms"]}i</span>
                                    }
                                    {(flat["canRooms"] < room) &&
                                        <span className={"text-[1.4vw]"}>/{flat["canRooms"]}i</span>
                                    }
                                    <span className={"text-[1.4vw]"}>-izbový</span>
                                </div>
                            </div>
                        </div>
                        <div className={"flex flex-col"}>
                            <div className="">
                                <small className="uppercase text-[0.7vw]">CELKOVÁ PLOCHA EXTERIÉRU</small>
                                <p className={"mb-[1vw] text-[1.4vw]"}>{flat["exterier_spolu"]}m²</p>
                            </div>
                            <div className="">
                                <small className="uppercase text-[0.7vw]">CELKOVÁ VÝMERA</small>
                                <p className={"text-[1.4vw] font-bold"}>{flat["vymera"]}m²</p>
                            </div>
                        </div>
                    </div>
                    <div className={"text-center flex justify-start my-5"}>

                    </div>
                </div>
            </section>
            <section className={"relative"}>
                <div className={"absolute z-10 p-10 w-full flex justify-center"}>
                    <div className={"flex gap-4"}>
                        <Link href={"/galeria"}>
                            <button className={"bytButton uppercase text-white px-6 py-1"}>Galéria</button>
                        </Link>
                        <Link href={"/technicke-specifikacie"}>
                            <button
                                className="bg-black contactButton text-white uppercase rounded-full px-6 py-1">
                                <p className="font-medium">Technická špecifikácia</p></button>
                        </Link>
                    </div>
                </div>
                <div className="h-56 mt-10 sm:h-96 lg:min-h-[90vh]">
                    <Carousel slide={false}>
                        {[...Array(gallery[room - 1])].map((e, i) => <Image key={i}
                                                                            src={"/galeria/" + room + "/" + i + ".webp"}
                                                                            alt={"galeria"}
                                                                            width={1920}
                                                                            height={1080}/>
                        )}
                    </Carousel>
                </div>
            </section>
            <Modal show={openModal} dismissible className={"modalino"} size={"7xl"} onClose={() => setOpenModal(false)}>
                <Modal.Body>
                    <Suspense fallback={<Spinner/>}>
                        <Image src={"/byty/" + flat.flatNumber.replace(/\./g, '') + ".png"} alt={"pôdorys bytu"}
                               width={1920} height={1080}/>
                    </Suspense>
                </Modal.Body>
            </Modal>
        </>
    )
}